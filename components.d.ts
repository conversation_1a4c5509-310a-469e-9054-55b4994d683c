/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AAlert: typeof import('ant-design-vue/es')['Alert']
    ABadge: typeof import('ant-design-vue/es')['Badge']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACheckbox: typeof import('ant-design-vue/es')['Checkbox']
    ACol: typeof import('ant-design-vue/es')['Col']
    ACollapse: typeof import('ant-design-vue/es')['Collapse']
    ACollapsePanel: typeof import('ant-design-vue/es')['CollapsePanel']
    ActionColumn: typeof import('./../sigopt-box/src/components/ActionColumn.vue')['default']
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    ADropdown: typeof import('ant-design-vue/es')['Dropdown']
    AEmpty: typeof import('ant-design-vue/es')['Empty']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AFormItemRest: typeof import('ant-design-vue/es')['FormItemRest']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    AList: typeof import('ant-design-vue/es')['List']
    AListItem: typeof import('ant-design-vue/es')['ListItem']
    AListItemMeta: typeof import('ant-design-vue/es')['ListItemMeta']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AModal: typeof import('ant-design-vue/es')['Modal']
    APageHeader: typeof import('ant-design-vue/es')['PageHeader']
    APopconfirm: typeof import('ant-design-vue/es')['Popconfirm']
    APopover: typeof import('ant-design-vue/es')['Popover']
    AProgress: typeof import('ant-design-vue/es')['Progress']
    ARadio: typeof import('ant-design-vue/es')['Radio']
    ARadioGroup: typeof import('ant-design-vue/es')['RadioGroup']
    AreaInteractionHandler: typeof import('./src/components/AreaInteractionHandler.vue')['default']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASpace: typeof import('ant-design-vue/es')['Space']
    ASpin: typeof import('ant-design-vue/es')['Spin']
    AStatistic: typeof import('ant-design-vue/es')['Statistic']
    ASwitch: typeof import('ant-design-vue/es')['Switch']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATabPane: typeof import('ant-design-vue/es')['TabPane']
    ATabs: typeof import('ant-design-vue/es')['Tabs']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATextarea: typeof import('ant-design-vue/es')['Textarea']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    AUpload: typeof import('ant-design-vue/es')['Upload']
    BaseThreeCanvas: typeof import('./src/components/BaseThreeCanvas.vue')['default']
    ChangePassword: typeof import('./../sigopt-box/src/components/ChangePassword.vue')['default']
    ChangePasswordModal: typeof import('./src/components/ChangePasswordModal.vue')['default']
    CommandPanel: typeof import('./../sigopt-box/src/components/CommandPanel.vue')['default']
    Compass: typeof import('./src/components/three/Compass.vue')['default']
    ConfigFileManager: typeof import('./../sigopt-box/src/components/ConfigFileManager.vue')['default']
    ConfigPreview: typeof import('./../sigopt-box/src/components/ConfigPreview.vue')['default']
    ConnectionLine: typeof import('./../sigopt-box/src/components/ConnectionLine.vue')['default']
    DashboardManager: typeof import('./src/components/DashboardManager.vue')['default']
    DataTable: typeof import('./../sigopt-box/src/components/DataTable.vue')['default']
    DateRangePicker: typeof import('./../sigopt-box/src/components/DateRangePicker.vue')['default']
    DeviceInteractionHandler: typeof import('./src/components/DeviceInteractionHandler.vue')['default']
    DeviceListDashboard: typeof import('./src/components/DeviceListDashboard.vue')['default']
    DeviceMonitorModal: typeof import('./src/components/DeviceMonitorModal.vue')['default']
    DeviceSelector: typeof import('./src/components/DeviceSelector.vue')['default']
    DeviceSettingsModal: typeof import('./src/components/DeviceSettingsModal.vue')['default']
    DeviceTooltip: typeof import('./src/components/DeviceTooltip.vue')['default']
    EdgeDeviceForm: typeof import('./src/components/EdgeDeviceForm.vue')['default']
    EditableRow: typeof import('./../sigopt-box/src/components/EditableRow.vue')['default']
    ErrorMessageAlert: typeof import('./../sigopt-box/src/components/ErrorMessageAlert.vue')['default']
    FloatingTrunkLineCreator: typeof import('./src/components/FloatingTrunkLineCreator.vue')['default']
    FlowDirectionForm: typeof import('./../sigopt-box/src/components/FlowDirectionForm.vue')['default']
    FlowDirectionTable: typeof import('./../sigopt-box/src/components/FlowDirectionTable.vue')['default']
    FlowDisplay: typeof import('./../sigopt-box/src/components/FlowDisplay.vue')['default']
    IconAction: typeof import('./../sigopt-box/src/components/icons/IconAction.vue')['default']
    IconLinks: typeof import('./../sigopt-box/src/components/icons/IconLinks.vue')['default']
    IconMonth: typeof import('./../sigopt-box/src/components/icons/IconMonth.vue')['default']
    IconWatch: typeof import('./../sigopt-box/src/components/icons/IconWatch.vue')['default']
    IconWeek: typeof import('./../sigopt-box/src/components/icons/IconWeek.vue')['default']
    IconYear: typeof import('./../sigopt-box/src/components/icons/IconYear.vue')['default']
    LazyComponentLoader: typeof import('./src/components/LazyComponentLoader.vue')['default']
    MainHeader: typeof import('./src/components/three/MainHeader.vue')['default']
    ManualCommandForm: typeof import('./../sigopt-box/src/components/command-forms/ManualCommandForm.vue')['default']
    MapConfigModal: typeof import('./src/components/MapConfigModal.vue')['default']
    MonitorModal: typeof import('./src/components/MonitorModal.vue')['default']
    PageHeader: typeof import('./../sigopt-box/src/components/PageHeader.vue')['default']
    PhaseCommandForm: typeof import('./../sigopt-box/src/components/command-forms/PhaseCommandForm.vue')['default']
    PlanCommandForm: typeof import('./../sigopt-box/src/components/command-forms/PlanCommandForm.vue')['default']
    RoadInteractionHandler: typeof import('./src/components/RoadInteractionHandler.vue')['default']
    RoadNetwork: typeof import('./src/components/three/RoadNetwork.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchForm: typeof import('./../sigopt-box/src/components/SearchForm.vue')['default']
    StageSelector: typeof import('./../sigopt-box/src/components/StageSelector.vue')['default']
    StatsDashboard: typeof import('./src/components/StatsDashboard.vue')['default']
    ThreeCanvas: typeof import('./src/components/ThreeCanvas.vue')['default']
    TrunkLineInteractionHandler: typeof import('./src/components/TrunkLineInteractionHandler.vue')['default']
  }
}
