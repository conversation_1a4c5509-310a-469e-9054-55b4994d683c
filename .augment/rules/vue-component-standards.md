---
type: "always_apply"
description: "Vue 3 项目开发规范与 AI 执行指令"
---
# P0 核心执行指令（必须严格遵循）

## 🔄 工作流程
用户提问 → 需求分析 → 方案设计(2-4个) → 询问用户选择 → 等待确认 → 仅实施选定方案

## 🚫 绝对禁止
同时实施多个方案 | 擅自选择方案 | 添加未要求功能 | 混合方案特性

## 💬 沟通规则
必须使用中文 | 注释使用中文 | 第一人称视角

# P1 开发执行指令

## 🎯 组件开发
必须使用 Vue 3 Composition API (`<script setup>`) | 必须控制单文件组件在500行以内 | 超过1000行必须重构 | 必须优先复用现有组件

## 🔧 API使用
必须优先使用项目现有API | 禁止重复实现相同功能 | 必须扩展现有API而非新建 | 必须抽象相似功能为通用API

## ✅ 代码标准
必须编写无缺陷的完整代码 | 必须遵循项目编码约定 | 禁止修改无关代码 | 必须及时清理冗余代码 | 必须使用 Vue 3 + Pinia + ES6+

## 🚫 测试限制
禁止启动开发服务器进行测试 | 代码完成后不需要运行npm run dev或类似命令 | 通过静态代码分析和语法检查确保代码质量

# P2 严格编码规范（优先级P2 - 最高优先级，必须严格执行）

## 注释规范
注释使用中文 | 采用第一人称视角（"添加检查处理边界情况"） | 避免第三人称（"新增了..."、"删除了..."） | 解释代码的"为什么"和复杂的"如何" | 不在注释中标明变更动作，专注描述功能和目的

## 精简防御型编程
**避免过度条件检查**：仅在处理外部数据源（API响应、用户输入、文件读取）时进行必要的边界检查 | **最小化错误处理**：仅在可能恢复或需要用户反馈的位置添加try-catch块 | **有限参数验证**：内部函数调用默认参数有效，公共API接口可进行关键参数验证 | **谨慎空值检查**：仅在处理不可控外部数据时使用可选链操作符，内部数据流假设数据完整性

## 严格意图执行
**代码即文档**：代码应完全按字面执行，无隐含行为 | **禁止意图推测**：严禁基于推测添加任何形式的功能或逻辑 | **禁止自动纠错**：不得添加自动修正输入或状态的逻辑 | **禁止扩展职责**：函数或方法必须严格执行其定义的职责，不得扩展

## 异常处理原则
**精准异常捕获**：只在能够恢复或需要用户反馈的位置捕获特定异常 | **禁止吞没异常**：禁止使用空catch块或仅打印日志后忽略异常 | **合理异常转换**：可将底层技术异常转换为业务异常，但需保留原始错误信息 | **适度日志记录**：记录关键错误和业务异常，开发环境可适量记录调试信息

## 绝对规范优先
**规范高于修复**：任何代码修改必须首先遵循项目规范，其次才是解决具体问题 | **禁止临时违规**：严禁以"临时解决"、"快速修复"为由违反任何项目规范 | **全局一致性**：局部修改必须与整体代码风格和架构保持一致，即使这增加工作量 | **拒绝例外情况**：不存在可以违反规范的特殊情况，规范适用于所有代码更改

## 🎯 执行原则与适用范围

### 核心规范严格执行
代码质量、注释规范、意图执行等核心原则必须严格遵循 | 实用性平衡：在保证代码质量的前提下，允许在特定场景下适度调整具体实施方式 | 重构优先：当修复与规范冲突时，应优先重构相关代码以符合规范 | 渐进式改进：对于大型重构，可采用渐进式方式，但新增代码必须完全符合规范

### 适用范围与执行原则
本规范适用于所有代码修改，包括但不限于：错误修复、功能添加、性能优化、重构 | 核心原则（组件化、API优先、代码质量）严格执行，具体实施细节可根据实际情况灵活调整 | 在代码评审中，违反核心原则的部分将被标记为必须修复的阻断性问题 | 对于特殊场景（如紧急修复、第三方库集成），可在充分说明理由后适度放宽非核心规则