import js from '@eslint/js'
import vue from 'eslint-plugin-vue'
import vueParser from 'vue-eslint-parser'
import prettier from '@vue/eslint-config-prettier'

export default [
  // 忽略文件配置
  {
    ignores: [
      'node_modules/**',
      'dist/**',
      'build/**',
      '*.log',
      '.env*',
      'components.d.ts',
      'patches/**',
      'vendor/**',
    ],
  },

  // 基础 JavaScript 推荐规则
  js.configs.recommended,

  // Vue 3 推荐规则
  ...vue.configs['flat/recommended'],

  // Prettier 配置（必须放在最后）
  prettier,

  {
    files: ['**/*.{js,mjs,cjs,vue}'],
    languageOptions: {
      parser: vueParser,
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
        extraFileExtensions: ['.vue'],
      },
      globals: {
        // 浏览器环境全局变量
        window: 'readonly',
        document: 'readonly',
        console: 'readonly',
        localStorage: 'readonly',
        sessionStorage: 'readonly',
        URLSearchParams: 'readonly',
        location: 'readonly',
        navigator: 'readonly',
        // 浏览器定时器和动画
        setTimeout: 'readonly',
        clearTimeout: 'readonly',
        setInterval: 'readonly',
        clearInterval: 'readonly',
        requestAnimationFrame: 'readonly',
        cancelAnimationFrame: 'readonly',
        // 浏览器对话框
        alert: 'readonly',
        confirm: 'readonly',
        prompt: 'readonly',
        // Node.js 环境（用于配置文件）
        process: 'readonly',
        __dirname: 'readonly',
        __filename: 'readonly',
      },
    },
    plugins: {
      vue,
    },
    rules: {
      // Vue 3 基础规则
      'vue/multi-word-component-names': 'warn',

      // JavaScript 最佳实践
      'no-var': 'error',
      'prefer-const': 'error',
      'no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_',
        },
      ],
      'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
      'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'warn',

      // 代码风格
      eqeqeq: ['error', 'always'],
      curly: ['error', 'all'],
      'no-eval': 'error',

      // Three.js 相关优化
      'no-new': 'off', // Three.js 经常需要 new 但不赋值
    },
  },

  // 配置文件忽略某些规则
  {
    files: ['vite.config.js', 'eslint.config.js'],
    rules: {
      'no-console': 'off',
    },
  },
]
