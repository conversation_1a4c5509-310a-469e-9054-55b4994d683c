# 智慧交通信号灯中心控制系统

## 项目简介

智慧交通信号灯中心控制系统是一个基于Web的现代化智能交通管理平台，旨在提供直观、高效的城市交通信号灯设备监控与管理解决方案。系统通过3D可视化技术，将城市路网和交通信号灯设备实时状态呈现在统一的管理界面上，支持交通信号灯的状态监控、数据分析和远程控制等功能。

## 核心功能

- **城市路网3D可视化**：基于GeoJSON数据渲染城市道路网络
- **交通信号灯设备管理**：在3D地图上直观展示信号灯设备的位置和状态
- **设备状态实时监控**：通过颜色编码等方式直观展示设备运行状态（正常、故障、离线等）
- **设备详情查看**：支持点击设备查看详细信息和运行数据
- **路口ID快速导航**：支持按路口ID快速定位并进入详细管理页面
- **多维数据展示**：集成设备遥测数据、状态统计等多维数据展示

## 技术栈

- **前端框架**：Vue 3 + Vite
- **3D渲染**：Three.js
- **状态管理**：Pinia
- **UI组件**：Element Plus
- **样式处理**：SCSS
- **数据可视化**：ECharts (可选)
- **地理数据**：GeoJSON

## 项目结构

```
smart-traffic/
├── public/                 # 静态资源
│   └── data/               # GeoJSON等地理数据文件
├── src/
│   ├── api/                # API接口定义
│   ├── assets/             # 静态资源
│   ├── components/         # 通用组件
│   │   └── three/          # Three.js相关组件
│   ├── composables/        # 组合式API
│   ├── router/             # 路由配置
│   ├── stores/             # Pinia状态管理
│   ├── utils/              # 工具函数
│   │   ├── coordinateTransform.js    # 坐标转换工具
│   │   ├── DynamicRoadLoader.js      # 道路数据加载器
│   │   └── GeoJsonRenderer.js        # GeoJSON渲染器
│   ├── views/              # 页面视图
│   │   └── CityModel.vue   # 城市模型主视图
│   ├── App.vue             # 根组件
│   └── main.js             # 入口文件
└── vite.config.js          # Vite配置
```

## 安装与运行

### 环境要求

- Node.js 18+
- npm 8+ 或 pnpm 8+

### 安装依赖

```bash
# 使用npm
npm install

# 或使用pnpm
pnpm install
```

### 开发环境运行

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

## 开发指南

### 核心组件: CityModel.vue

`CityModel.vue` 是系统的核心组件，负责:
- 渲染城市路网（基于GeoJSON数据）
- 在路网上精确可视化交通信号灯设备
- 实时展示各信号灯设备的状态
- 提供交互方式以选中路口/设备，并展示数据
- 支持通过路口ID查询并导航

### 数据管理

系统支持两种主要数据来源:
1. 静态地理数据: 通过GeoJSON文件加载城市路网和设备基础信息
2. 动态设备数据: 通过API实时获取设备状态和遥测数据

### 代码规范

- 组件化开发: 保持组件模块化、可复用，控制单文件组件行数在500行以内
- API优先原则: 尽量使用项目中已封装好的API和工具函数
- 代码质量: 遵循项目编码约定和风格，保持代码清晰可维护

## 项目扩展计划

- 集成实时数据流，支持WebSocket通信
- 增强3D可视化效果和交互体验
- 开发更多设备管理和控制功能
- 集成AI辅助决策系统，实现智能交通调度

## 项目优化（2023年更新）

本次优化主要针对项目接口和工程化进行改进，主要内容包括：

### 1. API模块重构

- 采用模块化结构组织API接口
- 实现更完善的请求/响应拦截器
- 统一API模块的导出和调用方式
- 增强错误处理机制
- 改进请求缓存策略

### 2. 服务器配置优化

- 重构配置管理逻辑，提高可维护性
- 增强环境变量支持
- 优化URL构建逻辑
- 改进配置继承和覆盖机制

### 3. sigopt-box模块解析优化

- 按类别组织模块映射
- 实现更高效的模块缓存机制
- 改进预加载策略，支持并发控制
- 提供更详细的错误信息
- 增加模块分类查询功能

### 4. 主入口文件优化

- 增加完整的错误处理
- 改进初始化流程
- 添加详细的日志输出
- 优化模块预加载过程

### 5. 工程化改进

- 统一代码风格和命名规范
- 完善注释文档
- 优化模块依赖关系
- 提高代码复用性

这些优化旨在提高项目的可维护性、性能和稳定性，同时确保与sigoptbox项目的兼容性