import { createRouter, createWebHistory } from 'vue-router'
import { getToken } from '@/utils/auth'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: {
      title: '登录 - 智能交通信号边缘设施统一管控系统',
      requiresAuth: false, // 不需要身份验证
    },
  },
  {
    path: '/',
    name: 'CityModel',
    component: () => import('../views/CityModel.vue'),
    meta: {
      title: '智能交通信号边缘设施统一管控系统',
      requiresAuth: true, // 需要身份验证
    },
  },
  {
    path: '/demo',
    name: 'Demo',
    component: () => import('../views/demo/ThreeWorldDemo.vue'),
    meta: {
      title: 'Demo - 智能交通信号边缘设施统一管控系统',
      requiresAuth: false, // 不需要身份验证
    },
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

// 添加全局前置守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title || '智能交通信号边缘设施统一管控系统'

  // 检查页面是否需要登录权限
  if (to.meta.requiresAuth !== false) {
    // 默认需要验证
    const hasToken = getToken()

    if (hasToken) {
      // 已登录状态，允许访问请求的页面
      next()
    } else {
      // 未登录状态，重定向到登录页，并传递原目标路径
      next({
        path: '/login',
        query: { redirect: to.fullPath },
      })
    }
  } else {
    // 如果已经登录，且要访问登录页，则重定向到首页
    if (to.path === '/login' && getToken()) {
      next({ path: '/' })
    } else {
      next()
    }
  }
})

export default router
