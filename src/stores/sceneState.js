import { defineStore } from 'pinia';
import { ref } from 'vue';

/**
 * This store manages the state of interactions between UI panels and the 3D scene.
 */
export const useSceneStateStore = defineStore('sceneState', () => {
	/**
	 * @type {import('vue').Ref<boolean>}
	 * The control flag for global loading or processing status.
	 */
	const isProcessing = ref(false);

	/**
	 * @type {import('vue').Ref<Object|null>}
	 * 存储当前要放置的设备信息
	 */
	const deviceForPlacement = ref(null);

	/**
	 * @type {import('vue').Ref<Object|null>}
	 * 存储选中的位置坐标 {lng, lat}
	 */
	const selectedLocation = ref(null);

	/**
	 * @type {import('vue').Ref<Object|null>}
	 * 存储当前在场景中选中的设备对象
	 */
	const selectedDevice = ref(null);

	/**
	 * 设置处理状态
	 * @param {boolean} status - 处理状态
	 */
	function setProcessingStatus(status) {
		isProcessing.value = status;
	}

	/**
	 * 设置要放置的设备
	 * @param {Object|null} device - 设备对象或null（取消放置）
	 */
	function setDeviceForPlacement(device) {
		deviceForPlacement.value = device;
	}

	/**
	 * 设置选中的位置坐标
	 * @param {Object} coordinates - 包含lng和lat的经纬度坐标对象
	 */
	function setSelectedLocation(coordinates) {
		selectedLocation.value = coordinates ? { ...coordinates } : null;
	}

	/**
	 * 清除选中的位置数据
	 */
	function clearSelectedLocation() {
		selectedLocation.value = null;
	}

	/**
	 * 设置选中的设备
	 * @param {Object|null} device - 设备对象或null
	 */
	function setSelectedDevice(device) {
		selectedDevice.value = device;
	}

	return {
		isProcessing,
		deviceForPlacement,
		selectedLocation,
		selectedDevice,
		setProcessingStatus,
		setDeviceForPlacement,
		setSelectedLocation,
		clearSelectedLocation,
		setSelectedDevice,
	};
});
