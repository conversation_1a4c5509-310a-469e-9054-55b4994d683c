import { defineStore } from 'pinia'
import { message } from 'ant-design-vue'
import {
  DB_CONFIG,
  initIndexedDB,
  saveToIndexedDB,
  getFromIndexedDB,
} from '@/utils/indexedDBConfig'
import {
  createTrunkLine,
  validateTrunkLine,
  updateTrunkLineMetadata,
  convertTrunkLineToApiFormat,
  convertApiDataToTrunkLine,
  DEFAULT_TRUNK_LINE_STYLE,
  getLineWidthByClassification,
} from '@/utils/trunkLineTypes.js'
import {
  extractTrunkLineGeoData,
  validateExtractedGeoData,
  optimizeTrunkLineGeoData,
} from '@/utils/trunkLineGeoUtils.js'
import { createRoute, getAllRoutes, deleteRoute } from '@/api/modules/routes.js'

/**
 * 干线数据管理Store
 * 负责干线的创建、编辑、删除、可见性控制等功能
 * 实现与路网数据的独立管理
 */
export const useTrunkLinesStore = defineStore('trunkLines', {
  state: () => ({
    // 干线列表
    trunkLines: [],

    // 当前选中的干线
    selectedTrunkLine: null,

    // 创建模式状态
    isCreating: false,
    creatingTrunkLine: {
      route_name: '',
      classification: '主要干线',
      selectedRoadIds: [],
      style: {
        ...DEFAULT_TRUNK_LINE_STYLE,
        lineWidth: getLineWidthByClassification('主要干线'), // 根据分类设置宽度
      },
    },

    // 编辑模式状态
    isEditing: false,
    editingTrunkLine: null,

    // 加载状态
    loading: false,
    error: null,

    // IndexedDB实例
    db: null,
  }),

  getters: {
    // 检查是否可以创建干线
    canCreateTrunkLine: state => {
      return (
        state.creatingTrunkLine.route_name.trim() !== '' &&
        state.creatingTrunkLine.selectedRoadIds.length > 0
      )
    },

    // 获取干线统计信息（MapConfigModal需要）
    trunkLinesStats: state => {
      return {
        total: state.trunkLines.length,
      }
    },

    // 获取所有分类（MapConfigModal需要）
    getAllClassifications: state => [
      ...new Set(
        state.trunkLines.map(line => line.classification).filter(Boolean)
      ),
    ],
  },

  actions: {
    /**
     * 获取干线详情
     * @param {string} routeId - 干线ID
     * @returns {Object|null} 干线详情
     */
    getTrunkLineById(routeId) {
      return this.trunkLines.find(line => line.route_id === routeId) || null
    },

    /**
     * 高级搜索干线
     * @param {Object} filters - 搜索条件
     * @param {string} filters.keyword - 关键词（搜索名称）
     * @param {string} filters.classification - 分类
     * @param {string} filters.dateFrom - 创建日期起始
     * @param {string} filters.dateTo - 创建日期结束
     * @returns {Array} 过滤后的干线列表
     */
    searchTrunkLinesAdvanced(filters = {}) {
      let results = [...this.trunkLines]

      // 关键词搜索
      if (filters.keyword) {
        const keyword = filters.keyword.toLowerCase()
        results = results.filter(
          line =>
            line.route_name.toLowerCase().includes(keyword) ||
            line.classification.toLowerCase().includes(keyword)
        )
      }

      // 分类过滤
      if (filters.classification) {
        results = results.filter(
          line => line.classification === filters.classification
        )
      }

      // 日期范围过滤
      if (filters.dateFrom) {
        const fromDate = new Date(filters.dateFrom)
        results = results.filter(line => new Date(line.createdAt) >= fromDate)
      }

      if (filters.dateTo) {
        const toDate = new Date(filters.dateTo)
        results = results.filter(line => new Date(line.createdAt) <= toDate)
      }

      return results
    },

    /**
     * 获取干线统计详情
     * @returns {Object} 详细统计信息
     */
    getTrunkLinesDetailedStats() {
      const stats = {
        total: this.trunkLines.length,
        byClassification: {},
        byMonth: {},
        totalRoads: 0,
        totalLength: 0,
      }

      this.trunkLines.forEach(line => {
        // 按分类统计
        const classification = line.classification || '未分类'
        stats.byClassification[classification] =
          (stats.byClassification[classification] || 0) + 1

        // 按月份统计
        const month = new Date(line.createdAt).toISOString().substring(0, 7) // YYYY-MM
        stats.byMonth[month] = (stats.byMonth[month] || 0) + 1

        // 路段和长度统计
        if (line.selectedRoadIds) {
          stats.totalRoads += line.selectedRoadIds.length
        }
        if (line.metadata?.totalLength) {
          stats.totalLength += line.metadata.totalLength
        }
      })

      return stats
    },

    /**
     * 初始化Store
     */
    async initialize() {
      try {
        this.db = await initIndexedDB()

        // 优先从服务器加载最新数据，失败时从本地加载
        try {
          await this.loadTrunkLinesFromServer()
        } catch (serverError) {
          await this.loadTrunkLinesFromDB()
        }
      } catch (error) {
        console.error('初始化干线Store失败:', error)
        this.error = '初始化失败'
      }
    },

    /**
     * 从服务器加载所有干线数据
     */
    async loadTrunkLinesFromServer() {
      try {
        this.loading = true
        this.error = null

        const apiRoutes = await getAllRoutes()

        if (!Array.isArray(apiRoutes)) {
          console.error('API返回的数据不是数组:', apiRoutes)
          throw new Error('API返回的数据格式不正确')
        }

        // 将API数据转换为本地格式
        const trunkLines = apiRoutes.map((apiRoute, index) => {
          // 尝试从本地缓存获取扩展字段
          const existingTrunkLine = this.trunkLines.find(
            tl => tl.route_id === apiRoute.route_id
          )
          const localExtensions = existingTrunkLine
            ? {
                // 不保留旧颜色，让convertApiDataToTrunkLine根据分类重新设置
                // color: existingTrunkLine.color,
                style: existingTrunkLine.style,
                selectedRoadIds: existingTrunkLine.selectedRoadIds,
                createdAt: existingTrunkLine.createdAt,
                metadata: existingTrunkLine.metadata,
                settings: existingTrunkLine.settings,
              }
            : {}

          try {
            const convertedTrunkLine = convertApiDataToTrunkLine(
              apiRoute,
              localExtensions
            )

            return convertedTrunkLine
          } catch (conversionError) {
            console.error(`转换第${index + 1}条干线失败:`, conversionError)
            throw conversionError
          }
        })

        this.trunkLines = trunkLines

        // 保存到本地缓存
        await this.saveTrunkLinesToDB()

        return trunkLines
      } catch (error) {
        console.error('从服务器加载干线数据失败:', error)
        this.error = '加载干线数据失败'
        message.error('加载干线数据失败')
        throw error // 重新抛出错误，让调用者处理
      } finally {
        this.loading = false
      }
    },

    /**
     * 从IndexedDB加载干线数据
     */
    async loadTrunkLinesFromDB() {
      if (!this.db) {
        return
      }

      try {
        this.loading = true
        const data = await getFromIndexedDB(
          this.db,
          DB_CONFIG.STORES.TRUNK_LINES,
          'all'
        )
        if (data && Array.isArray(data)) {
          this.trunkLines = data
        }
      } catch (error) {
        console.error('加载干线数据失败:', error)
        this.error = '加载数据失败'
      } finally {
        this.loading = false
      }
    },

    /**
     * 保存干线数据到IndexedDB
     */
    async saveTrunkLinesToDB() {
      if (!this.db) {
        return
      }

      try {
        // 转换为普通对象以避免响应式代理问题
        const plainTrunkLines = JSON.parse(JSON.stringify(this.trunkLines))
        await saveToIndexedDB(
          this.db,
          DB_CONFIG.STORES.TRUNK_LINES,
          'all',
          plainTrunkLines
        )
      } catch (error) {
        console.error('保存干线数据失败:', error)
        this.error = '保存数据失败'
      }
    },

    /**
     * 获取创建干线的默认状态
     */
    getDefaults() {
      return {
        name: '',
        selectedRoadIds: [],
        style: {
          ...DEFAULT_TRUNK_LINE_STYLE,
          lineWidth: getLineWidthByClassification('主要干线'), // 默认使用主要干线宽度
        },
      }
    },

    /**
     * 开始创建干线
     */
    startCreating() {
      this.isCreating = true
      this.creatingTrunkLine = this.getDefaults()
    },

    /**
     * 取消创建干线
     */
    cancelCreating() {
      this.isCreating = false
      this.creatingTrunkLine = this.getDefaults()
    },

    /**
     * 更新创建中的干线信息
     */
    updateCreatingTrunkLine(updates) {
      Object.assign(this.creatingTrunkLine, updates)
    },

    /**
     * 添加路段到创建中的干线
     */
    addRoadToCreating(roadId) {
      if (!this.creatingTrunkLine.selectedRoadIds.includes(roadId)) {
        this.creatingTrunkLine.selectedRoadIds.push(roadId)
      }
    },

    /**
     * 从创建中的干线移除路段
     */
    removeRoadFromCreating(roadId) {
      const index = this.creatingTrunkLine.selectedRoadIds.indexOf(roadId)
      if (index > -1) {
        this.creatingTrunkLine.selectedRoadIds.splice(index, 1)
      }
    },

    /**
     * 创建干线
     * @param {Object} roadNetworkData - 路网数据，用于复制地理坐标
     * @param {Object} dynamicRoadLoader - 动态路网加载器（可选）
     * @param {Object} options - 创建选项
     */
    async createTrunkLine(
      roadNetworkData,
      dynamicRoadLoader = null,
      options = {}
    ) {
      if (!this.canCreateTrunkLine) {
        message.warning('请填写干线名称并选择路段')
        return false
      }

      try {
        this.loading = true

        // 提取地理数据，创建独立的干线地理数据副本

        const rawGeoData = extractTrunkLineGeoData(
          this.creatingTrunkLine.selectedRoadIds,
          roadNetworkData,
          dynamicRoadLoader
        )

        // 验证提取的地理数据
        const validation = validateExtractedGeoData(rawGeoData)
        if (!validation.isValid) {
          console.error('地理数据验证失败:', validation.errors)
          message.error('地理数据提取失败，请检查选中的路段')
          return false
        }

        // 优化地理数据
        const geoData = optimizeTrunkLineGeoData(rawGeoData, {
          simplifyTolerance: options.simplifyTolerance || 0.0001,
          removeDuplicates: options.removeDuplicates !== false,
          mergeConnected: options.mergeConnected || false,
        })

        // 使用工具函数创建干线对象（适配新API格式）
        const newTrunkLine = createTrunkLine({
          route_name: this.creatingTrunkLine.route_name,
          classification: this.creatingTrunkLine.classification,
          route: geoData, // 使用提取的地理数据作为route字段
          selectedRoadIds: [...this.creatingTrunkLine.selectedRoadIds],
          style: { ...this.creatingTrunkLine.style },
          metadata: {
            description: options.description || '',
            tags: options.tags || [],
          },
        })

        // 更新元数据
        updateTrunkLineMetadata(newTrunkLine)

        // 验证创建的干线对象
        const trunkLineValidation = validateTrunkLine(newTrunkLine)
        if (!trunkLineValidation.isValid) {
          console.error('干线对象验证失败:', trunkLineValidation.errors)
          message.error('干线创建失败，数据格式错误')
          return false
        }

        // 保存到服务器

        const apiData = convertTrunkLineToApiFormat(newTrunkLine)
        const savedRoute = await createRoute(apiData)

        // 更新本地数据，添加服务器返回的route_id
        newTrunkLine.route_id = savedRoute.route_id
        newTrunkLine.updatedAt = new Date().toISOString()

        // 添加到列表
        this.trunkLines.push(newTrunkLine)

        // 保存到本地缓存
        await this.saveTrunkLinesToDB()

        // 重置创建状态
        this.cancelCreating()

        const successMsg = `干线 "${newTrunkLine.route_name}" 创建成功，包含 ${newTrunkLine.metadata.roadCount} 条路段`
        message.success(successMsg)

        return newTrunkLine
      } catch (error) {
        console.error('创建干线失败:', error)
        this.error = '创建干线失败: ' + (error.message || '未知错误')
        message.error('创建干线失败')
        return false
      } finally {
        this.loading = false
      }
    },

    /**
     * 删除干线
     * @param {string} trunkLineId - 干线ID
     */
    async deleteTrunkLine(routeId) {
      try {
        this.loading = true

        const index = this.trunkLines.findIndex(
          line => line.route_id === routeId
        )
        if (index === -1) {
          message.warning('未找到要删除的干线')
          return false
        }

        const trunkLine = this.trunkLines[index]

        // 从服务器删除
        await deleteRoute(routeId)

        // 从列表中移除
        this.trunkLines.splice(index, 1)

        // 如果正在编辑这条干线，取消编辑
        if (
          this.editingTrunkLine &&
          this.editingTrunkLine.route_id === routeId
        ) {
          this.cancelEditing()
        }

        // 如果这条干线被选中，取消选中
        if (
          this.selectedTrunkLine &&
          this.selectedTrunkLine.route_id === routeId
        ) {
          this.selectedTrunkLine = null
        }

        // 保存到本地缓存
        await this.saveTrunkLinesToDB()

        message.success(`干线 "${trunkLine.route_name}" 已删除`)

        return true
      } catch (error) {
        console.error('删除干线失败:', error)
        this.error = '删除干线失败: ' + (error.message || '未知错误')
        message.error('删除干线失败')
        return false
      } finally {
        this.loading = false
      }
    },

    /**
     * 开始编辑干线
     * @param {string} trunkLineId - 干线ID
     */
    startEditing(trunkLineId) {
      const trunkLine = this.trunkLines.find(
        line => line.route_id === trunkLineId
      )
      if (!trunkLine) {
        message.warning('未找到要编辑的干线')
        return false
      }

      this.isEditing = true
      this.editingTrunkLine = JSON.parse(JSON.stringify(trunkLine)) // 深度复制

      return true
    },

    /**
     * 取消编辑干线
     */
    cancelEditing() {
      this.isEditing = false
      this.editingTrunkLine = null
    },

    /**
     * 保存编辑的干线
     * @param {Object} updateData - 编辑后的干线数据
     */
    async saveEditedTrunkLine(updateData) {
      if (!updateData || !updateData.route_id) {
        message.warning('没有正在编辑的干线')
        return false
      }
      try {
        this.loading = true
        const index = this.trunkLines.findIndex(
          line => line.route_id === updateData.route_id
        )
        if (index === -1) {
          message.warning('未找到要保存的干线')
          return false
        }
        // 调用API同步后端
        const { updateRoute } = await import('@/api/modules/routes.js')
        const apiPayload = {
          route_name: updateData.route_name,
          classification: updateData.classification,
          selectedRoadIds: updateData.selectedRoadIds,
        }
        await updateRoute(updateData.route_id, apiPayload)
        // 更新时间戳
        updateData.updatedAt = new Date().toISOString()
        // 更新本地数据
        this.trunkLines[index] = { ...this.trunkLines[index], ...updateData }
        await this.saveTrunkLinesToDB()
        this.isEditing = false
        this.editingTrunkLine = null
        message.success('干线保存成功')
        return true
      } catch (error) {
        console.error('保存编辑的干线失败:', error)
        this.error = '保存失败'
        message.error('保存失败')
        return false
      } finally {
        this.loading = false
      }
    },

    /**
     * 批量删除干线
     * @param {Array} trunkLineIds - 干线ID数组
     */
    async batchDeleteTrunkLines(trunkLineIds) {
      try {
        this.loading = true

        const deletedNames = []
        const validIds = []

        // 收集有效的ID和名称
        trunkLineIds.forEach(id => {
          const trunkLine = this.trunkLines.find(line => line.route_id === id)
          if (trunkLine) {
            deletedNames.push(trunkLine.route_name)
            validIds.push(id)
          }
        })

        if (validIds.length === 0) {
          message.warning('没有找到要删除的干线')
          return false
        }

        // 从服务器批量删除
        const deletePromises = validIds.map(id => deleteRoute(id))
        await Promise.all(deletePromises)

        // 从本地列表中移除
        validIds.forEach(id => {
          const index = this.trunkLines.findIndex(line => line.route_id === id)
          if (index > -1) {
            this.trunkLines.splice(index, 1)
          }
        })

        // 清理编辑和选中状态
        if (
          this.editingTrunkLine &&
          validIds.includes(this.editingTrunkLine.route_id)
        ) {
          this.cancelEditing()
        }
        if (
          this.selectedTrunkLine &&
          validIds.includes(this.selectedTrunkLine.route_id)
        ) {
          this.selectedTrunkLine = null
        }

        // 保存到本地缓存
        await this.saveTrunkLinesToDB()

        message.success(`已删除 ${deletedNames.length} 条干线`)

        return true
      } catch (error) {
        console.error('批量删除干线失败:', error)
        this.error = '批量删除失败: ' + (error.message || '未知错误')
        message.error('批量删除失败')
        return false
      } finally {
        this.loading = false
      }
    },

    /**
     * 导出干线数据
     * @param {Array} trunkLineIds - 要导出的干线ID数组，为空则导出全部
     * @returns {Object} 导出的数据
     */
    exportTrunkLines(trunkLineIds = []) {
      try {
        let linesToExport = this.trunkLines

        if (trunkLineIds.length > 0) {
          linesToExport = this.trunkLines.filter(line =>
            trunkLineIds.includes(line.route_id)
          )
        }

        const exportData = {
          version: '1.0',
          exportedAt: new Date().toISOString(),
          trunkLines: linesToExport,
          metadata: {
            total: linesToExport.length,
            source: '',
          },
        }
        return exportData
      } catch (error) {
        console.error('导出干线数据失败:', error)
        this.error = '导出失败'
        message.error('导出失败')
        return null
      }
    },

    /**
     * 导入干线数据
     * @param {Object} importData - 导入的数据
     * @param {boolean} replaceExisting - 是否替换现有数据
     */
    async importTrunkLines(importData, replaceExisting = false) {
      try {
        this.loading = true

        if (
          !importData ||
          !importData.trunkLines ||
          !Array.isArray(importData.trunkLines)
        ) {
          message.error('导入数据格式无效')
          return false
        }

        if (replaceExisting) {
          this.trunkLines = []
        }

        let importedCount = 0
        let skippedCount = 0

        importData.trunkLines.forEach(line => {
          // 检查是否已存在同名干线
          const existingIndex = this.trunkLines.findIndex(
            existing => existing.route_name === line.route_name
          )

          if (existingIndex > -1) {
            if (replaceExisting) {
              // 替换现有干线
              line.route_id = this.trunkLines[existingIndex].route_id // 保持原ID
              line.updatedAt = new Date().toISOString()
              this.trunkLines[existingIndex] = line
              importedCount++
            } else {
              // 跳过重复的干线
              skippedCount++
            }
          } else {
            // 添加新干线
            line.route_id = `trunk_${Date.now()}_${Math.random()
              .toString(36)
              .substring(2, 11)}`
            line.updatedAt = new Date().toISOString()
            this.trunkLines.push(line)
            importedCount++
          }
        })

        // 保存到数据库
        await this.saveTrunkLinesToDB()

        let resultMessage = `成功导入 ${importedCount} 条干线`
        if (skippedCount > 0) {
          resultMessage += `，跳过 ${skippedCount} 条重复干线`
        }

        message.success(resultMessage)

        return true
      } catch (error) {
        console.error('导入干线数据失败:', error)
        this.error = '导入失败'
        message.error('导入失败')
        return false
      } finally {
        this.loading = false
      }
    },

    /**
     * 清空所有干线
     */
    async clearAllTrunkLines() {
      try {
        this.loading = true

        const count = this.trunkLines.length
        this.trunkLines = []

        // 保存到数据库
        await this.saveTrunkLinesToDB()

        message.success(`已清空 ${count} 条干线`)

        return true
      } catch (error) {
        console.error('清空干线失败:', error)
        this.error = '清空失败'
        message.error('清空失败')
        return false
      } finally {
        this.loading = false
      }
    },

    /**
     * 重置错误状态
     */
    clearError() {
      this.error = null
    },
  },
})
