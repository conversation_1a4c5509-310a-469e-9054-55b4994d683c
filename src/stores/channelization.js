import { defineStore } from 'pinia'
import {
  downloadChannelization,
  uploadChannelization,
} from '../api/modules/channelization'
import { message } from 'ant-design-vue'

export const useChannelizationStore = defineStore('channelization', {
  state: () => ({
    directionStates: {
      north: false,
      northeast: false,
      east: false,
      southeast: false,
      south: false,
      southwest: false,
      west: false,
      northwest: false,
    },
    directionConfig: {},
    flowConfig: {},
    shouldUpdateFlow: false,
    lastUpdated: null,
    loading: false,
    currentConfigId: '1',
    currentEdgeId: 'e001', // 当前选中的边缘设备ID
    error: null,
    autoSaveEnabled: true,
  }),

  actions: {
    disableAutoSave() {
      this.autoSaveEnabled = false
    },

    enableAutoSave() {
      this.autoSaveEnabled = true
    },

    setCurrentEdgeId(edgeId) {
      if (edgeId !== this.currentEdgeId) {
        this.currentEdgeId = edgeId
      }
    },

    async updateDirectionStates(states, saveToServer = true) {
      this.directionStates = states || {}
      this.shouldUpdateFlow = true
      this.lastUpdated = Date.now()

      if (saveToServer && this.autoSaveEnabled) {
        await this.saveConfigToServer(this.currentConfigId)
      }
    },

    async updateDirectionConfig(config, saveToServer = true) {
      this.directionConfig = config || {}
      this.shouldUpdateFlow = true
      this.lastUpdated = Date.now()

      if (saveToServer && this.autoSaveEnabled) {
        await this.saveConfigToServer(this.currentConfigId)
      }
    },

    async updateFlowConfig(config, saveToServer = true) {
      this.flowConfig = config || {}
      this.lastUpdated = Date.now()

      // 检查是否在监控页面，如果是则不保存
      const isMonitorPage =
        window.location.pathname.includes('/homeview') ||
        window.location.pathname.includes('/intersection')

      if (isMonitorPage) {
        return
      }

      if (saveToServer && this.autoSaveEnabled) {
        await this.saveConfigToServer(this.currentConfigId)
      }
    },

    resetUpdateFlag() {
      this.shouldUpdateFlow = false
    },

    async saveConfigToServer(id = null, forceUpload = false) {
      // 防止重复请求
      if (this.loading) {
        return
      }

      // 检查是否需要保存
      if (!this.autoSaveEnabled && !forceUpload) {
        return
      }

      if (!id) id = this.currentConfigId
      this.loading = true
      this.error = null

      try {
        const configData = {
          id: id,
          channelization: {
            directionStates: this.directionStates,
            directionConfig: this.directionConfig,
            flowConfig: this.flowConfig,
            lastUpdated: this.lastUpdated,
          },
        }

        const response = await uploadChannelization(
          this.currentEdgeId,
          configData
        )

        if (response.code === 200) {
          message.success(response.msg)
        }
      } catch (error) {
        console.error('保存配置失败:', error)
        this.error =
          error.message === 'success'
            ? '服务器返回了成功状态但处理出错'
            : error.message || '保存配置到服务器失败'
        message.error(this.error)
      } finally {
        this.loading = false
      }
    },

    async initializeStates(forceLoad = false, ignoreErrors = false) {
      try {
        // 从服务器加载配置
        const serverLoaded = await this.loadConfigFromServer(forceLoad)
        const isValid = this.hasValidConfiguration()

        // 如果服务器配置无效，使用默认配置
        if (!serverLoaded || !isValid) {
          this.useDefaultConfiguration()
        }

        return true
      } catch (error) {
        console.error('初始化状态失败:', error)
        if (!ignoreErrors) {
          throw error
        }

        // 使用默认配置
        this.useDefaultConfiguration()
        return false
      }
    },

    // 检查配置是否有效
    hasValidConfiguration() {
      return (
        Object.keys(this.directionStates).length > 0 &&
        Object.values(this.directionStates).some(v => v === true) &&
        Object.keys(this.directionConfig).length > 0
      )
    },

    // 使用默认配置
    useDefaultConfiguration() {
      this.$patch({
        directionStates: DEFAULT_DIRECTION_STATES,
        directionConfig: DEFAULT_DIRECTION_CONFIG,
        lastUpdated: Date.now(),
      })

      return true
    },

    async changeConfigId(id) {
      if (id === this.currentConfigId) return

      this.currentConfigId = id
      await this.initializeStates()
    },

    resetToDefaults() {
      // 设置默认的方向配置，东西南北方向
      this.directionStates = {
        north: true,
        northeast: false,
        east: true,
        southeast: false,
        south: true,
        southwest: false,
        west: true,
        northwest: false,
      }

      // 设置默认的方向配置
      const defaultConfig = {}
      ;['north', 'east', 'south', 'west'].forEach(dir => {
        defaultConfig[dir] = {
          approachDirection: { north: 1, east: 3, south: 5, west: 7 }[dir],
          lane: 3,
          light: true,
          people: true,
          lanes: [
            { laneNo: '1', tempModels: ['2'] }, // 左转
            { laneNo: '2', tempModels: ['1'] }, // 直行
            { laneNo: '3', tempModels: ['3'] }, // 右转
          ],
        }
      })

      this.directionConfig = defaultConfig
      this.flowConfig = {}
      this.lastUpdated = Date.now()
    },

    async loadConfigFromServer(forceLoad = false) {
      if (this.loading && !forceLoad) return false

      this.loading = true
      this.error = null

      try {
        // 使用边缘API代理获取配置
        const response = await downloadChannelization(
          this.currentEdgeId,
          this.currentConfigId
        )

        // 解析服务器返回的配置数据
        let channelizationData = response.channelization || response

        // 兼容数据可能在data字段下的情况
        if (response.data && response.data.channelization) {
          channelizationData = response.data.channelization
        }

        // 验证配置数据的有效性
        const isValid =
          channelizationData &&
          (channelizationData.directionStates ||
            channelizationData.directionConfig)

        if (!isValid) {
          // 如果配置无效，使用默认配置
          this.useDefaultConfiguration()
          return false
        }

        this.$patch({
          directionStates: channelizationData.directionStates || {},
          directionConfig: channelizationData.directionConfig || {},
          flowConfig: channelizationData.flowConfig || {},
          lastUpdated: channelizationData.lastUpdated || Date.now(),
        })

        return true
      } catch (error) {
        console.error(`从边缘设备[${this.currentEdgeId}]获取配置失败:`, error)
        this.error = error.message || '从服务器获取配置失败'

        // 使用默认配置
        this.useDefaultConfiguration()

        return false
      } finally {
        this.loading = false
      }
    },
  },

  getters: {
    isConfigured: state => {
      return Object.keys(state.directionStates).length > 0
    },
    getTemplates: state => {
      const templates = []
      const directions = [
        '北',
        '东北',
        '东',
        '东南',
        '南',
        '西南',
        '西',
        '西北',
      ]
      const directionValues = {
        north: 1,
        northeast: 2,
        east: 3,
        southeast: 4,
        south: 5,
        southwest: 6,
        west: 7,
        northwest: 8,
      }

      Object.entries(state.directionStates || {}).forEach(
        ([direction, enabled]) => {
          if (enabled) {
            const config = state.directionConfig[direction] || {}
            const directionKey =
              Object.keys(directionValues).find(
                key => directionValues[key] === config.approachDirection
              ) || direction
            const directionIndex = Object.values(directionValues).indexOf(
              directionValues[directionKey]
            )

            // 处理车道配置
            const lanes = []
            for (let i = 0; i < (config.lane || 1); i++) {
              const laneConfig = config.lanes?.[i] || {}
              const tempModels = laneConfig.tempModels || []

              lanes.push({
                laneNo: (i + 1).toString(),
                through: tempModels.includes('1') ? 1 : 0,
                turnLeft: tempModels.includes('2') ? 1 : 0,
                turnRight: tempModels.includes('3') ? 1 : 0,
                turnAround: tempModels.includes('4') ? 1 : 0,
                turnBicycleLane: tempModels.includes('5') ? 1 : 0,
                directionIdentifyings:
                  tempModels
                    .map(model => {
                      const modelMap = {
                        1: '直行',
                        2: '左转',
                        3: '右转',
                        4: '调头',
                        5: '非机动车道',
                      }
                      return modelMap[model]
                    })
                    .filter(Boolean)
                    .join(',') || '',
                ...(config.light
                  ? {
                      channelNumberPhase: '1',
                      markingColorPhase: '2',
                      trafficLightColor: '#ccc',
                    }
                  : {}),
              })
            }

            // 处理人行道配置
            const peoples = config.people
              ? [
                  {
                    laneNo: '0',
                    lfd: '0(人行)',
                    ...(config.light ? { channelNumberPhase: '1' } : {}),
                  },
                  {
                    laneNo: '99',
                    lfd: '99(人行)',
                    ...(config.light ? { channelNumberPhase: '1' } : {}),
                  },
                ]
              : [{}]

            templates.push({
              approachDirection: directionValues[directionKey],
              cdireCtion: directions[directionIndex],
              lane: config.lane || 1,
              lanes: lanes,
              peoples: peoples,
              light: config.light || false,
              people: config.people || false,
            })
          }
        }
      )

      return templates
    },
    isLoaded: state => {
      return (
        Object.values(state.directionStates).some(value => value === true) ||
        Object.keys(state.directionConfig).length > 0
      )
    },
  },
})

// 添加默认配置
const DEFAULT_DIRECTION_STATES = {
  north: true,
  east: true,
  south: true,
  west: true,
}

const DEFAULT_DIRECTION_CONFIG = {
  north: {
    approachDirection: 1,
    lane: 2,
    light: true,
    people: true,
    lanes: [
      { laneNo: '1', tempModels: ['1', '2'] }, // 直行+左转
      { laneNo: '2', tempModels: ['1', '3'] }, // 直行+右转
    ],
  },
  east: {
    approachDirection: 3,
    lane: 2,
    light: true,
    people: true,
    lanes: [
      { laneNo: '1', tempModels: ['1', '2'] },
      { laneNo: '2', tempModels: ['1', '3'] },
    ],
  },
  south: {
    approachDirection: 5,
    lane: 2,
    light: true,
    people: true,
    lanes: [
      { laneNo: '1', tempModels: ['1', '2'] },
      { laneNo: '2', tempModels: ['1', '3'] },
    ],
  },
  west: {
    approachDirection: 7,
    lane: 2,
    light: true,
    people: true,
    lanes: [
      { laneNo: '1', tempModels: ['1', '2'] },
      { laneNo: '2', tempModels: ['1', '3'] },
    ],
  },
}
