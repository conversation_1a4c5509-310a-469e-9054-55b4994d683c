import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import { edgeDeviceApi, edgeApiProxy } from '@/api'
import { message } from 'ant-design-vue'

// This store will manage the state of all monitored devices
export const useDevicesStore = defineStore('devices', () => {
  // --- STATE ---

  // The list of all devices being monitored.
  // Each device is an object with static and reactive properties.
  const devices = ref([])
  const isLoading = ref(false)

  // 定时刷新相关
  let refreshTimer = null

  // --- ACTIONS ---

  /**
   * 从服务器获取所有边缘设备列表
   */
  async function fetchDevices() {
    isLoading.value = true
    try {
      // 调用真实API获取设备列表
      const response = await edgeDeviceApi.getAllEdges()
      devices.value = response || []
    } catch (error) {
      console.error('获取设备列表失败:', error)
      message.error('获取设备列表失败: ' + (error.message || '未知错误'))
      devices.value = []
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 新增一个设备
   * @param {object} deviceData - 来自表单的设备数据
   */
  async function addDevice(deviceData) {
    isLoading.value = true
    try {
      // 调用API添加设备
      await edgeDeviceApi.addEdge(deviceData)

      // 添加成功后重新获取设备列表
      await fetchDevices()

      return true
    } catch (error) {
      console.error(`新增设备失败:`, error)
      throw new Error(error.response?.data?.message || '添加设备失败')
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 更新一个设备
   * @param {object} deviceData - 来自表单的设备数据
   */
  async function updateDevice(deviceData) {
    isLoading.value = true
    try {
      // 调用API更新设备
      await edgeDeviceApi.updateEdge(deviceData)

      // 更新成功后重新获取设备列表或更新本地设备数据
      const device = devices.value.find(d => d.edge_id === deviceData.edge_id)
      if (device) {
        Object.assign(device, deviceData)
      } else {
        // 如果找不到设备，重新获取列表
        await fetchDevices()
      }

      return true
    } catch (error) {
      console.error(`更新设备 ${deviceData.edge_id} 失败:`, error)
      throw new Error(error.response?.data?.message || '更新设备失败')
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 删除一个设备
   * @param {string} edgeId - 要删除的设备的edge_id
   */
  async function deleteDevice(edgeId) {
    isLoading.value = true
    try {
      // 调用API删除设备
      await edgeDeviceApi.deleteEdge(edgeId)

      // 在本地删除设备
      const index = devices.value.findIndex(d => d.edge_id === edgeId)
      if (index !== -1) {
        devices.value.splice(index, 1)
      }

      return true
    } catch (error) {
      console.error(`删除设备 ${edgeId} 失败:`, error)
      throw new Error(error.response?.data?.message || '删除设备失败')
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 获取单个设备详情
   */
  async function getDeviceDetail(edgeId) {
    try {
      return await edgeDeviceApi.getEdge(edgeId)
    } catch (error) {
      console.error(`获取设备 ${edgeId} 详情失败:`, error)
      throw error
    }
  }

  /**
   * 直接设置设备列表（用于演示和测试）
   * @param {Array} deviceList - 设备列表数据
   */
  function setDevices(deviceList) {
    devices.value = deviceList
  }

  /**
   * 启动定时刷新设备列表（每5秒刷新一次）
   */
  function startAutoRefresh() {
    // 清除已存在的定时器
    if (refreshTimer) {
      clearInterval(refreshTimer)
    }

    // 设置新的定时器，每5秒刷新一次
    refreshTimer = setInterval(() => {
      fetchDevices()
    }, 5000)

    console.log('设备列表自动刷新已启动（每5秒刷新一次）')
  }

  /**
   * 停止定时刷新设备列表
   */
  function stopAutoRefresh() {
    if (refreshTimer) {
      clearInterval(refreshTimer)
      refreshTimer = null
      console.log('设备列表自动刷新已停止')
    }
  }

  return {
    devices,
    isLoading,
    fetchDevices,
    addDevice,
    updateDevice,
    deleteDevice,
    getDeviceDetail,
    setDevices,
    startAutoRefresh,
    stopAutoRefresh,
  }
})
