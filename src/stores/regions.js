import { defineStore } from 'pinia'
import { message } from 'ant-design-vue'
import {
  DB_CONFIG,
  initIndexedDB,
  saveToIndexedDB,
  getFromIndexedDB,
} from '@/utils/indexedDBConfig'
import {
  createRegion,
  getAllRegions,
  updateRegion,
  deleteRegion,
} from '@/api/modules/regions.js'

/**
 * 区域数据管理Store
 * 管理用户创建的区域数据，支持本地存储和服务器同步
 */
export const useRegionsStore = defineStore('regions', {
  state: () => ({
    regions: [], // 区域列表
    loading: false, // 加载状态
    error: null, // 错误信息
    db: null, // IndexedDB实例
  }),

  getters: {
    /**
     * 获取区域总数
     */
    regionsCount: state => state.regions.length,

    /**
     * 根据分类获取区域
     */
    getRegionsByClassification: state => classification => {
      return state.regions.filter(
        region => region.classification === classification
      )
    },

    /**
     * 根据ID获取区域
     */
    getRegionById: state => regionId => {
      return state.regions.find(region => region.region_id === regionId)
    },

    /**
     * 获取区域名称列表
     */
    regionNames: state => state.regions.map(region => region.region_name),
  },

  actions: {
    /**
     * 初始化Store
     */
    async initialize() {
      try {
        this.db = await initIndexedDB()

        // 优先从服务器加载最新数据，失败时从本地加载
        try {
          await this.loadRegionsFromServer()
        } catch (serverError) {
          await this.loadRegionsFromDB()
        }
      } catch (error) {
        console.error('初始化区域Store失败:', error)
        this.error = '初始化失败'
      }
    },

    /**
     * 从服务器加载所有区域数据
     */
    async loadRegionsFromServer() {
      try {
        this.loading = true
        this.error = null

        const apiRegions = await getAllRegions()

        if (!Array.isArray(apiRegions)) {
          console.error('API返回的区域数据不是数组:', apiRegions)
          throw new Error('API返回的区域数据格式不正确')
        }

        this.regions = apiRegions

        // 同步到本地存储
        await this.saveRegionsToDB()
      } catch (error) {
        console.error('从服务器加载区域数据失败:', error)
        this.error = '加载服务器数据失败'
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 从IndexedDB加载区域数据
     */
    async loadRegionsFromDB() {
      if (!this.db) {
        return
      }

      try {
        this.loading = true
        const data = await getFromIndexedDB(
          this.db,
          DB_CONFIG.STORES.REGIONS,
          'all'
        )
        if (data && Array.isArray(data)) {
          this.regions = data
        }
      } catch (error) {
        console.error('加载区域数据失败:', error)
        this.error = '加载数据失败'
      } finally {
        this.loading = false
      }
    },

    /**
     * 保存区域数据到IndexedDB
     */
    async saveRegionsToDB() {
      if (!this.db) {
        return
      }

      try {
        // 转换为普通对象以避免响应式代理问题
        const plainRegions = JSON.parse(JSON.stringify(this.regions))
        await saveToIndexedDB(
          this.db,
          DB_CONFIG.STORES.REGIONS,
          'all',
          plainRegions
        )
      } catch (error) {
        console.error('保存区域数据失败:', error)
        this.error = '保存数据失败'
      }
    },

    /**
     * 创建新区域
     */
    async createNewRegion(regionData) {
      try {
        this.loading = true
        this.error = null

        // 发送到服务器
        const createdRegion = await createRegion(regionData)

        // 添加到本地状态
        this.regions.push(createdRegion)

        // 保存到本地存储
        await this.saveRegionsToDB()

        message.success(`区域 "${regionData.region_name}" 创建成功`)
        return createdRegion
      } catch (error) {
        console.error('创建区域失败:', error)
        this.error = '创建区域失败'
        message.error(`创建区域失败: ${error.message}`)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 更新区域
     */
    async updateExistingRegion(regionId, updateData) {
      try {
        this.loading = true
        this.error = null

        // 发送到服务器
        const updatedRegion = await updateRegion(regionId, updateData)

        // 更新本地状态
        const index = this.regions.findIndex(
          region => region.region_id === regionId
        )
        if (index !== -1) {
          this.regions[index] = updatedRegion
        }

        // 保存到本地存储
        await this.saveRegionsToDB()

        message.success(`区域更新成功`)
        return updatedRegion
      } catch (error) {
        console.error('更新区域失败:', error)
        this.error = '更新区域失败'
        message.error(`更新区域失败: ${error.message}`)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 删除区域
     */
    async removeRegion(regionId) {
      try {
        this.loading = true
        this.error = null

        // 从服务器删除
        await deleteRegion(regionId)

        // 从本地状态删除
        this.regions = this.regions.filter(
          region => region.region_id !== regionId
        )

        // 保存到本地存储
        await this.saveRegionsToDB()

        message.success('区域删除成功')
      } catch (error) {
        console.error('删除区域失败:', error)
        this.error = '删除区域失败'
        message.error(`删除区域失败: ${error.message}`)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * 清空所有区域数据
     */
    clearRegions() {
      this.regions = []
      this.error = null
    },
  },
})
