/**
 * 环境配置管理
 * 统一管理项目中使用的环境变量，方便后续修改和维护
 */

// 开发环境API服务器配置
const DEV_API_SERVER = 'http://*************:8084'
// 生产环境默认使用当前主机
const PROD_API_SERVER = '' // 空字符串表示使用当前主机

// 获取环境变量，使用默认值兼容各种环境
export const ENV = {
  // 基础环境变量
  NODE_ENV: import.meta.env.NODE_ENV || 'development',
  MODE: import.meta.env.MODE || 'development',
  BASE_URL: import.meta.env.BASE_URL || '/',
  DEV: import.meta.env.DEV || false,
  PROD: import.meta.env.PROD || false,

  // 应用配置
  APP_TITLE: import.meta.env.VITE_APP_TITLE || '智能交通系统',

  // API配置
  API_SERVER:
    import.meta.env.VITE_API_SERVER ||
    (import.meta.env.DEV ? DEV_API_SERVER : PROD_API_SERVER),
  SERVER_IP:
    import.meta.env.VITE_SERVER_IP ||
    (import.meta.env.DEV ? '*************' : ''),
  SERVER_PORT:
    parseInt(
      import.meta.env.VITE_SERVER_PORT || (import.meta.env.DEV ? '8084' : '0'),
      10
    ) || (import.meta.env.DEV ? 8084 : null),
  API_TIMEOUT: parseInt(import.meta.env.VITE_API_TIMEOUT || '60000', 10), // 增加默认超时时间到60秒

  // 调试相关
  DEBUG: import.meta.env.VITE_DEBUG === 'true',
}

/**
 * 获取完整的API服务器URL
 * @param {String} path 可选的路径
 * @returns {String} 完整URL
 */
export function getApiServer(path = '') {
  // 如果已配置完整API服务器地址，直接使用
  if (ENV.API_SERVER) {
    return `${ENV.API_SERVER}${path}`
  }

  // 构建URL
  let baseUrl = ''

  // 如果运行在浏览器环境
  if (typeof window !== 'undefined') {
    // 开发环境使用固定配置
    if (ENV.DEV) {
      baseUrl =
        ENV.SERVER_IP && ENV.SERVER_PORT
          ? `http://${ENV.SERVER_IP}:${ENV.SERVER_PORT}`
          : DEV_API_SERVER
    } else {
      // 生产环境使用当前主机地址
      const { protocol, hostname } = window.location
      baseUrl = `${protocol}//${hostname}`

      // 如果配置了端口，使用配置的端口
      if (ENV.SERVER_PORT) {
        baseUrl += `:${ENV.SERVER_PORT}`
      } else if (window.location.port) {
        baseUrl += `:${window.location.port}`
      }
    }
  } else {
    // 如果不在浏览器环境，使用配置的IP和端口
    if (ENV.SERVER_IP) {
      baseUrl = ENV.SERVER_PORT
        ? `http://${ENV.SERVER_IP}:${ENV.SERVER_PORT}`
        : `http://${ENV.SERVER_IP}`
    } else {
      // 默认开发地址
      baseUrl = DEV_API_SERVER
    }
  }

  return `${baseUrl}${path}`
}

// 判断当前是否为开发环境
export const isDev = ENV.DEV

// 获取当前环境的API基础URL（统一使用/api）
export const getCurrentApiBaseUrl = () => '/api'

export default {
  ENV,
  getApiServer,
  isDev,
  getCurrentApiBaseUrl,
}
