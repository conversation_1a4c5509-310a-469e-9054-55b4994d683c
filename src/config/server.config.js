/**
 * 服务器配置模块
 * 统一管理服务器配置，支持开发和生产环境
 */

// 环境变量优先级：
// 1. import.meta.env中的环境变量
// 2. window.location中的信息 (仅在浏览器环境)
// 3. 默认配置

// 统一的服务器配置（兼容ESM和CJS环境）
import { ENV, getApiServer, isDev, getCurrentApiBaseUrl } from './env.config'

// 默认API服务器URL
const DEFAULT_API_SERVER = 'http://*************:8084'

let SERVER_CONFIG = {
  // IP地址
  get ip() {
    return (
      ENV.SERVER_IP ||
      (isDev
        ? '*************'
        : typeof window !== 'undefined'
        ? window.location.hostname
        : '127.0.0.1')
    )
  },

  // 端口
  get port() {
    return (
      ENV.SERVER_PORT ||
      (isDev
        ? 8084
        : typeof window !== 'undefined' && window.location.port
        ? parseInt(window.location.port)
        : null)
    )
  },

  // 完整URL
  get url() {
    return ENV.API_SERVER || (isDev ? DEFAULT_API_SERVER : '')
  },

  // API基础路径（用于前端API请求）
  get apiBasePath() {
    return getCurrentApiBaseUrl()
  },

  // API版本
  apiVersion: 'v1',

  // 超时时间
  timeout: ENV.API_TIMEOUT,
}

// 开发环境配置
if (ENV.DEV) {
  // 开发环境固定使用配置好的服务器
} else if (typeof window !== 'undefined') {
  // 生产环境下，动态获取当前主机信息
}

// 导出完整 URL 辅助函数
export const getServerUrl = (path = '') => {
  return `${SERVER_CONFIG.url}${path}`
}

// 更新配置的辅助函数
export const updateServerConfig = env => {
  if (env && ENV.DEV) {
    // 仅在开发环境中允许通过此函数更新
    if (env.VITE_SERVER_IP) {
      SERVER_CONFIG.ip = env.VITE_SERVER_IP
    }

    if (env.VITE_SERVER_PORT) {
      SERVER_CONFIG.port = parseInt(env.VITE_SERVER_PORT)
    }

    if (env.VITE_API_TIMEOUT) {
      SERVER_CONFIG.timeout = parseInt(env.VITE_API_TIMEOUT)
    }

    console.log('配置更新: 服务器配置已更新:', SERVER_CONFIG)
  }

  return SERVER_CONFIG
}

// 获取API基础URL
export const getApiBaseUrl = () => {
  // 在开发环境中，返回相对路径供代理使用
  if (isDev) {
    return '' // 返回空字符串，让API调用直接使用相对路径如/api/xxx
  }
  return SERVER_CONFIG.url
}

// 获取不同服务的API基础URL
export const getBaseUrl = serviceType => {
  const apiServices = {
    // 地理数据API
    geoData: isDev ? '/api' : `${SERVER_CONFIG.url}/api`,
    // 其他API可以在这里添加
    default: isDev ? '/api' : `${SERVER_CONFIG.url}/api`,
  }

  return apiServices[serviceType] || apiServices.default
}

// 导出服务器配置
export { SERVER_CONFIG }
