/**
 * 应用主入口文件
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'
import Antd from 'ant-design-vue'

// 简洁的样式引入方式
import 'ant-design-vue/dist/reset.css'
import '@sigopt/styles/index.css'
import './assets/styles/main.scss'

import { SERVER_CONFIG, updateServerConfig } from './config/server.config'
import './utils/dayjs'
import { injectModuleResolver } from './utils/sigoptBoxResolver'

if (import.meta.env) {
  updateServerConfig(import.meta.env)
}

injectModuleResolver()

const app = createApp(App)

app.config.errorHandler = err => {
  console.error('应用错误:', err)
}

if (typeof window !== 'undefined') {
  window.addEventListener('modal-closed', event => {
    if (event.detail && event.detail.type) {
      // 清理相关资源
    }
  })
}

const pinia = createPinia()

// 通过环境变量控制Pinia开发工具
if (import.meta.env.DEV) {
  // 如果设置了 VITE_DISABLE_PINIA_DEVTOOLS=true，则禁用开发工具输出
  const disableDevtools = import.meta.env.VITE_DISABLE_PINIA_DEVTOOLS === 'true'
  if (disableDevtools) {
    pinia._devtools = false
  }
}

app.use(pinia)
app.use(router)
app.use(Antd)

app.config.globalProperties.$serverConfig = SERVER_CONFIG

app.mount('#app')
