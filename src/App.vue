<script setup>
	// 此处用于添加应用级别逻辑
	import { ref } from 'vue';

	// 在script部分检测环境
	const isDev = ref(process.env.NODE_ENV === 'development');
</script>

<template>
	<router-view v-slot="{ Component }">
		<transition name="fade" mode="out-in">
			<component :is="Component" />
		</transition>
	</router-view>
</template>

<style>
	#app {
		width: 100%;
		height: 100vh;
		margin: 0;
		padding: 0;
	}

	.fade-enter-active,
	.fade-leave-active {
		transition: opacity 0.3s ease;
	}

	.fade-enter-from,
	.fade-leave-to {
		opacity: 0;
	}
</style>
