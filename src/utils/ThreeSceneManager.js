import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import {
  cleanupMemory,
  startMemoryMonitoring,
  stopMemoryMonitoring,
} from './memoryManager.js'

class ThreeSceneManager {
  constructor() {
    this.scene = null
    this.camera = null
    this.renderer = null
    this.controls = null
    this.animationFrameId = null
    this.container = null
    this.clock = new THREE.Clock()
    this.materialsCache = {}
    this.needsRender = true
    this.memoryMonitorId = null
    this.onCameraChangeCallback = null
    this.boundOnWindowResize = null
    this._customControlsListeners = null
  }

  init(
    containerElement,
    initialCameraPos,
    initialControlsTarget,
    onCameraChange,
    options = {}
  ) {
    if (!containerElement) {
      console.error(
        'ThreeSceneManager: Container element is required for initialization.'
      )
      return
    }
    this.container = containerElement
    this.onCameraChangeCallback = onCameraChange

    // Scene
    this.scene = new THREE.Scene()
    this.scene.background = new THREE.Color(0x000000)

    // Camera
    this.camera = new THREE.PerspectiveCamera(
      75,
      this.container.clientWidth / this.container.clientHeight,
      0.001,
      1000
    )
    this.camera.position.set(
      initialCameraPos.x,
      initialCameraPos.y,
      initialCameraPos.z
    )

    // Renderer
    this.renderer = new THREE.WebGLRenderer({ antialias: true })
    this.renderer.setPixelRatio(window.devicePixelRatio)
    this.renderer.setSize(
      this.container.clientWidth,
      this.container.clientHeight
    )
    this.container.appendChild(this.renderer.domElement)

    // Lights
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5)
    this.scene.add(ambientLight)
    const directionalLight = new THREE.DirectionalLight(0xffffff, 1)
    directionalLight.position.set(5, 10, 7.5)
    this.scene.add(directionalLight)

    // Controls
    this.controls = new OrbitControls(this.camera, this.renderer.domElement)
    this.controls.target.set(
      initialControlsTarget.x,
      initialControlsTarget.y,
      initialControlsTarget.z
    )
    this.controls.enableDamping = true
    this.controls.dampingFactor = 0.05
    this.controls.screenSpacePanning = false
    this.controls.minDistance = options.minDistance || 1
    this.controls.maxDistance = options.maxDistance || 500
    this.controls.minPolarAngle = 0
    this.controls.maxPolarAngle = Math.PI / 2
    this.controls.zoomSpeed = 0.5
    this.controls.mouseButtons = {
      LEFT: 2,
      MIDDLE: 1,
      RIGHT: 0,
    }

    this.controls.keys = {
      LEFT: 'ArrowLeft',
      UP: 'ArrowUp',
      RIGHT: 'ArrowRight',
      BOTTOM: 'ArrowDown',
    }

    this._setupCustomMouseControls()

    if (
      this.onCameraChangeCallback &&
      typeof this.onCameraChangeCallback === 'function'
    ) {
      this.controls.addEventListener('change', this.onCameraChangeCallback)
    }
    // 绑定 resize 事件处理器并保存引用以便后续清理
    this.boundOnWindowResize = this.onWindowResize.bind(this)
    window.addEventListener('resize', this.boundOnWindowResize, {
      passive: true,
    })

    if (import.meta.env.DEV) {
      this.memoryMonitorId = startMemoryMonitoring(stats => {
        if (stats.jsHeapSize && stats.jsHeapSize.used > 200) {
          console.warn('内存使用情况:', stats)
        }
      }, 60000)
    }
  }

  _setupCustomMouseControls() {
    if (!this.renderer || !this.controls) {
      return
    }

    const domElement = this.renderer.domElement
    let isCtrlPressed = false

    const handleKeyDown = event => {
      if (event.ctrlKey || event.metaKey) {
        isCtrlPressed = true
      }
    }

    const handleKeyUp = event => {
      if (!event.ctrlKey && !event.metaKey) {
        isCtrlPressed = false
      }
    }

    const handleMouseDown = event => {
      if (event.button === 0 && isCtrlPressed) {
        this.controls.mouseButtons.LEFT = 0
      } else if (event.button === 0) {
        this.controls.mouseButtons.LEFT = 2
      }
    }

    const handleMouseUp = event => {
      if (event.button === 0) {
        this.controls.mouseButtons.LEFT = 2
      }
    }

    // 添加 passive 选项以避免滚动阻塞警告
    document.addEventListener('keydown', handleKeyDown, { passive: false })
    document.addEventListener('keyup', handleKeyUp, { passive: false })
    domElement.addEventListener('mousedown', handleMouseDown, {
      passive: false,
    })
    domElement.addEventListener('mouseup', handleMouseUp, { passive: false })

    this._customControlsListeners = {
      keydown: handleKeyDown,
      keyup: handleKeyUp,
      mousedown: handleMouseDown,
      mouseup: handleMouseUp,
    }
  }

  setMaterialsForResizing(materialsCache) {
    this.materialsCache = materialsCache || {}
  }

  onWindowResize() {
    // 添加更严格的检查以避免 undefined 错误
    if (!this.camera || !this.renderer || !this.container) {
      console.warn(
        'ThreeSceneManager: Cannot resize - missing required components'
      )
      return
    }

    try {
      this.camera.aspect =
        this.container.clientWidth / this.container.clientHeight
      this.camera.updateProjectionMatrix()
      const newWidth = this.container.clientWidth
      const newHeight = this.container.clientHeight
      this.renderer.setSize(newWidth, newHeight)

      // 安全地更新材质缓存
      if (this.materialsCache && typeof this.materialsCache === 'object') {
        Object.values(this.materialsCache).forEach(material => {
          if (material && material.isLineMaterial && material.resolution) {
            material.resolution.set(newWidth, newHeight)
          }
        })
      }
    } catch (error) {
      console.error('ThreeSceneManager: Error during resize:', error)
    }
  }

  startAnimationLoop() {
    if (!this.animationFrameId) {
      this._animate()
    }
  }

  _animate() {
    this.animationFrameId = requestAnimationFrame(this._animate.bind(this))

    // 性能优化：减少不必要的计算
    const shouldRender =
      this.needsRender || (this.controls && this.controls.enabled)

    if (this.controls && this.controls.enabled) {
      this.controls.update()
    }

    // 只在需要时渲染以提高性能
    if (shouldRender && this.scene && this.camera && this.renderer) {
      this.renderer.render(this.scene, this.camera)
      this.needsRender = false
    }
  }

  stopAnimationLoop() {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId)
      this.animationFrameId = null
    }
  }

  add(object) {
    if (this.scene && object) {
      this.scene.add(object)
      this.needsRender = true
    }
  }

  remove(object) {
    if (this.scene && object) {
      this.scene.remove(object)
      this.needsRender = true
    }
  }

  markNeedsRender() {
    this.needsRender = true
  }

  getScene() {
    return this.scene
  }

  getCamera() {
    return this.camera
  }

  getControls() {
    return this.controls
  }

  getRenderer() {
    return this.renderer
  }

  getContainer() {
    return this.container
  }

  focusOnPoint(targetPoint, preferredDistance = 30) {
    // 添加更严格的参数验证
    if (!this.camera || !this.controls || !targetPoint) {
      console.warn(
        'ThreeSceneManager: Cannot focus on point - camera, controls, or targetPoint missing.'
      )
      return
    }

    // 验证 targetPoint 的属性
    if (
      typeof targetPoint.x !== 'number' ||
      typeof targetPoint.y !== 'number' ||
      typeof targetPoint.z !== 'number'
    ) {
      console.warn(
        'ThreeSceneManager: Invalid targetPoint - x, y, z must be numbers'
      )
      return
    }

    try {
      let originallyDisabled = false
      if (!this.controls.enabled) {
        console.warn(
          'ThreeSceneManager: OrbitControls were disabled, temporarily enabling for focusOnPoint.'
        )
        this.controls.enabled = true
        originallyDisabled = true
      }

      // 安全地设置控制目标
      if (
        this.controls.target &&
        typeof this.controls.target.copy === 'function'
      ) {
        this.controls.target.copy(targetPoint)
      }

      const newCameraY = targetPoint.y + preferredDistance * 0.6
      const newCameraZ = targetPoint.z + preferredDistance * 0.5
      const newCameraX = targetPoint.x + preferredDistance * 0.2

      this.camera.position.set(newCameraX, newCameraY, newCameraZ)
      this.camera.lookAt(this.controls.target)
      this.controls.update()

      if (originallyDisabled) {
        this.controls.enabled = false
        console.warn(
          'ThreeSceneManager: Restoring OrbitControls to disabled state after focusOnPoint.'
        )
      }

      console.log(
        `ThreeSceneManager: Camera focused and moved. Target: ${targetPoint.x.toFixed(
          2
        )}, ${targetPoint.y.toFixed(2)}, ${targetPoint.z.toFixed(
          2
        )}. New Cam Pos: ${this.camera.position.x.toFixed(
          2
        )}, ${this.camera.position.y.toFixed(
          2
        )}, ${this.camera.position.z.toFixed(2)}`
      )
    } catch (error) {
      console.error('ThreeSceneManager: Error in focusOnPoint:', error)
    }
  }

  dispose() {
    this.stopAnimationLoop()

    // 清理自定义控制事件监听器
    if (this._customControlsListeners) {
      document.removeEventListener(
        'keydown',
        this._customControlsListeners.keydown
      )
      document.removeEventListener('keyup', this._customControlsListeners.keyup)
      if (this.renderer && this.renderer.domElement) {
        this.renderer.domElement.removeEventListener(
          'mousedown',
          this._customControlsListeners.mousedown
        )
        this.renderer.domElement.removeEventListener(
          'mouseup',
          this._customControlsListeners.mouseup
        )
      }
      this._customControlsListeners = null
    }

    if (this.controls && this.onCameraChangeCallback) {
      this.controls.removeEventListener('change', this.onCameraChangeCallback)
    }
    if (this.controls) {
      this.controls.dispose()
    }
    if (this.renderer) {
      this.renderer.dispose()
      if (
        this.renderer.domElement &&
        this.renderer.domElement.parentNode === this.container
      ) {
        this.container.removeChild(this.renderer.domElement)
      }
    }

    // 清理 resize 事件监听器
    if (this.boundOnWindowResize) {
      window.removeEventListener('resize', this.boundOnWindowResize)
      this.boundOnWindowResize = null
    }

    if (this.memoryMonitorId) {
      stopMemoryMonitoring(this.memoryMonitorId)
      this.memoryMonitorId = null
    }

    cleanupMemory()

    this.scene = null
    this.camera = null
    this.renderer = null
    this.controls = null
    this.container = null
    this.materialsCache = {}
    this.onCameraChangeCallback = null

    console.log('ThreeSceneManager disposed.')
  }
}

export default ThreeSceneManager
