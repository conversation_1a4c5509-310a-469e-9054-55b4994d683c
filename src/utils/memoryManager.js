/**
 * 简化版内存管理工具
 * 提供Three.js对象清理和基础内存监控功能
 */

/**
 * 批量释放Three.js对象
 * @param {Array|Object} objects - 要释放的对象或对象数组
 */
export function disposeThreeObjects(objects) {
  if (!Array.isArray(objects)) {
    objects = [objects]
  }

  objects.forEach(obj => {
    if (!obj) {
      return
    }

    // 释放几何体
    if (obj.geometry) {
      obj.geometry.dispose()
    }

    // 释放材质
    if (obj.material) {
      if (Array.isArray(obj.material)) {
        obj.material.forEach(mat => {
          if (mat.map) mat.map.dispose()
          mat.dispose()
        })
      } else {
        if (obj.material.map) obj.material.map.dispose()
        obj.material.dispose()
      }
    }

    // 释放纹理
    if (obj.texture) {
      obj.texture.dispose()
    }
  })
}

/**
 * 获取内存使用统计
 * @returns {Object} 内存统计信息
 */
export function getMemoryStats() {
  const stats = {}

  // 获取JavaScript堆内存信息
  if (window.performance && window.performance.memory) {
    const memory = window.performance.memory
    stats.jsHeapSize = {
      used: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
      total: Math.round(memory.totalJSHeapSize / 1024 / 1024), // MB
      limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024), // MB
    }
  }

  return stats
}

/**
 * 增强内存清理
 */
export function cleanupMemory() {
  // 强制垃圾回收（如果可用）
  if (window.gc) {
    window.gc()
  }

  // 尝试释放未使用的内存
  if (window.performance && window.performance.memory) {
    // 延迟执行第二次垃圾回收
    setTimeout(() => {
      if (window.gc) {
        window.gc()
      }
    }, 100)
  }
}

/**
 * 监控内存使用情况
 * @param {Function} callback - 回调函数，接收内存统计信息
 * @param {number} interval - 监控间隔（毫秒）
 * @returns {number} 定时器ID
 */
export function startMemoryMonitoring(callback, interval = 30000) {
  return setInterval(() => {
    const stats = getMemoryStats()
    callback(stats)

    // 如果内存使用过高，自动清理
    if (stats.jsHeapSize && stats.jsHeapSize.used > 300) {
      cleanupMemory()
    }
  }, interval)
}

/**
 * 停止内存监控
 * @param {number} monitorId - 监控ID
 */
export function stopMemoryMonitoring(monitorId) {
  clearInterval(monitorId)
}
