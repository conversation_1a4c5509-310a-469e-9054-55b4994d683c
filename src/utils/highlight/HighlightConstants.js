/**
 * 高亮效果相关常量配置
 * 统一管理所有高亮相关的配置参数
 */

export const HIGHLIGHT_CONSTANTS = {
  // 颜色配置
  DEFAULT_COLOR: 0x4fd1c5,           // 默认高亮颜色（青绿色）
  SELECTION_COLOR: 0x38b2ac,         // 选择高亮颜色（深青绿色）
  
  // 线宽配置
  LINE_WIDTH_MULTIPLIER: 1.3,       // 线宽倍数（从原来的2-3倍降到1.3倍）
  MIN_LINE_WIDTH: 0.1,              // 最小线宽
  
  // LineMaterial 特殊配置
  LINE_MATERIAL_MIN_WIDTH: 0.15,    // LineMaterial 最小线宽
  
  // 透明度配置
  OPACITY_MULTIPLIER: 1.3,          // 透明度倍数
  MAX_OPACITY: 1.0,                 // 最大透明度
  
  // 自发光配置
  EMISSIVE_INTENSITY: 0.7,          // 自发光强度
  
  // 动画配置
  ANIMATION_DURATION: 200,          // 高亮动画持续时间（毫秒）
  
  // 高亮类型
  HIGHLIGHT_TYPES: {
    ROAD_SELECTION: 'road_selection',     // 路线选择高亮
    TRUNK_LINE: 'trunk_line',             // 干线高亮
    DEVICE: 'device',                     // 设备高亮
    AREA: 'area',                         // 区域高亮
    POI: 'poi',                           // 兴趣点高亮
  },
  
  // 不同类型的高亮配置
  TYPE_CONFIGS: {
    road_selection: {
      color: 0x4fd1c5,
      lineWidthMultiplier: 1.3,
      minLineWidth: 0.1,
    },
    trunk_line: {
      color: 0xff6b6b,
      lineWidthMultiplier: 1.5,
      minLineWidth: 0.15,
    },
    device: {
      color: 0x38b2ac,
      opacityMultiplier: 1.5,
      maxOpacity: 1.0,
    },
    area: {
      color: 0x4fd1c5,
      opacityMultiplier: 1.3,
      maxOpacity: 0.8,
    },
    poi: {
      color: 0x38b2ac,
      opacityMultiplier: 1.5,
      maxOpacity: 1.0,
    },
  },
}

/**
 * 获取指定类型的高亮配置
 * @param {string} type - 高亮类型
 * @returns {Object} 高亮配置
 */
export function getHighlightConfig(type) {
  return HIGHLIGHT_CONSTANTS.TYPE_CONFIGS[type] || HIGHLIGHT_CONSTANTS.TYPE_CONFIGS.road_selection
}

/**
 * 高亮配置验证器
 * @param {Object} config - 配置对象
 * @returns {boolean} 是否有效
 */
export function validateHighlightConfig(config) {
  if (!config || typeof config !== 'object') {
    return false
  }
  
  // 检查必要的配置项
  const requiredFields = ['color']
  return requiredFields.every(field => config.hasOwnProperty(field))
}

/**
 * 合并高亮配置
 * @param {string} type - 高亮类型
 * @param {Object} customConfig - 自定义配置
 * @returns {Object} 合并后的配置
 */
export function mergeHighlightConfig(type, customConfig = {}) {
  const baseConfig = getHighlightConfig(type)
  return { ...baseConfig, ...customConfig }
}
