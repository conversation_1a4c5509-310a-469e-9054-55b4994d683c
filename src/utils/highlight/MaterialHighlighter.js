import * as THREE from 'three'
import { HIGHLIGHT_CONSTANTS } from './HighlightConstants.js'

/**
 * 材质高亮处理器
 * 统一处理不同类型材质的高亮效果，消除代码重复
 */
export class MaterialHighlighter {
  /**
   * 创建高亮材质
   * @param {THREE.Material} originalMaterial - 原始材质
   * @param {Object} options - 高亮选项
   * @returns {THREE.Material} 高亮材质
   */
  static createHighlightMaterial(originalMaterial, options = {}) {
    const config = {
      color: options.color || HIGHLIGHT_CONSTANTS.DEFAULT_COLOR,
      lineWidthMultiplier: options.lineWidthMultiplier || HIGHLIGHT_CONSTANTS.LINE_WIDTH_MULTIPLIER,
      opacityMultiplier: options.opacityMultiplier || HIGHLIGHT_CONSTANTS.OPACITY_MULTIPLIER,
      minLineWidth: options.minLineWidth || HIGHLIGHT_CONSTANTS.MIN_LINE_WIDTH,
      maxOpacity: options.maxOpacity || HIGHLIGHT_CONSTANTS.MAX_OPACITY,
    }

    const highlightMaterial = originalMaterial.clone()

    // 统一设置颜色
    if (highlightMaterial.color) {
      highlightMaterial.color.set(config.color)
    }

    // 根据材质类型应用不同的高亮策略
    if (originalMaterial.isLineMaterial) {
      this._applyLineMaterialHighlight(highlightMaterial, config)
    } else if (this._isMeshMaterial(originalMaterial)) {
      this._applyMeshMaterialHighlight(highlightMaterial, config)
    } else {
      this._applyGenericMaterialHighlight(highlightMaterial, config)
    }

    return highlightMaterial
  }

  /**
   * 应用 LineMaterial 高亮效果
   */
  static _applyLineMaterialHighlight(material, config) {
    if (material.linewidth !== undefined) {
      material.linewidth = Math.max(
        material.linewidth * config.lineWidthMultiplier,
        config.minLineWidth
      )
    }
  }

  /**
   * 应用 Mesh 材质高亮效果
   */
  static _applyMeshMaterialHighlight(material, config) {
    // 对于网格材质，主要调整透明度
    if (material.opacity !== undefined) {
      material.opacity = Math.min(
        material.opacity * config.opacityMultiplier,
        config.maxOpacity
      )
    }

    // 为标准材质添加自发光效果
    if (material.isMeshStandardMaterial || material.isMeshPhysicalMaterial) {
      material.emissive = new THREE.Color(config.color)
      material.emissiveIntensity = HIGHLIGHT_CONSTANTS.EMISSIVE_INTENSITY
    }
  }

  /**
   * 应用通用材质高亮效果
   */
  static _applyGenericMaterialHighlight(material, config) {
    // 对于其他类型的材质，尝试调整线宽
    if (material.linewidth !== undefined) {
      material.linewidth = Math.max(
        material.linewidth * config.lineWidthMultiplier,
        config.minLineWidth
      )
    }
  }

  /**
   * 检查是否为网格材质
   */
  static _isMeshMaterial(material) {
    return (
      material.isMeshBasicMaterial ||
      material.isMeshLambertMaterial ||
      material.isMeshStandardMaterial ||
      material.isMeshPhysicalMaterial
    )
  }

  /**
   * 恢复原始材质
   * @param {THREE.Object3D} object - 3D对象
   */
  static restoreOriginalMaterial(object) {
    if (!object || !object.userData.originalMaterial) {
      return false
    }

    try {
      object.material = object.userData.originalMaterial
      delete object.userData.originalMaterial
      delete object.userData.isHighlighted
      return true
    } catch (error) {
      console.warn('恢复原始材质失败:', error)
      return false
    }
  }

  /**
   * 应用高亮材质到对象
   * @param {THREE.Object3D} object - 3D对象
   * @param {Object} options - 高亮选项
   */
  static applyHighlight(object, options = {}) {
    if (!object?.material || object.userData.isHighlighted) {
      return false
    }

    try {
      // 保存原始材质
      object.userData.originalMaterial = object.material

      // 创建并应用高亮材质
      const highlightMaterial = this.createHighlightMaterial(object.material, options)
      object.material = highlightMaterial
      object.userData.isHighlighted = true

      return true
    } catch (error) {
      console.warn('应用高亮效果失败:', error)
      return false
    }
  }

  /**
   * 批量应用高亮
   * @param {Array} objects - 3D对象数组
   * @param {Object} options - 高亮选项
   */
  static applyBatchHighlight(objects, options = {}) {
    const results = []
    objects.forEach(object => {
      const success = this.applyHighlight(object, options)
      results.push({ object, success })
    })
    return results
  }

  /**
   * 批量恢复原始材质
   * @param {Array} objects - 3D对象数组
   */
  static restoreBatchMaterials(objects) {
    const results = []
    objects.forEach(object => {
      const success = this.restoreOriginalMaterial(object)
      results.push({ object, success })
    })
    return results
  }
}
