import * as THREE from 'three'
import { transformGeoJsonCoordinates } from '@/utils/coordinateTransform'

/**
 * 智能道路标签管理器
 * 继承并优化现有功能，实现类似电子地图的路名系统
 * - 主干道路名放大显示（默认启用）
 * - 按缩放级别分层显示
 * - 智能路名合并，避免重复
 * - 密度控制，避免过多标签
 */
class SmartRoadLabelManager {
  constructor() {
    this.styleConfig = this.getDefaultStyleConfig()
    this.visibilityConfig = this.getDefaultVisibilityConfig()
    this.roadPriorityConfig = this.getRoadPriorityConfig()
    this.lastCameraDistance = -1
    this.lastLabelsData = null
  }

  /**
   * 获取默认样式配置
   */
  getDefaultStyleConfig() {
    return {
      color: 0x1a2b3d, // 使用更暗的深蓝色，降低亮度
      enableRoadTypeScaling: true, // 默认启用主干道放大功能
    }
  }

  /**
   * 获取默认可见性配置
   */
  getDefaultVisibilityConfig() {
    return {
      baseFontSize: 1.0, // 基础字体大小（PlaneGeometry宽度）
      bufferRatio: 0.15, // 道路两端的缓冲区比例，避免在开头和结尾放置标签
      maxDistance: 100.0, // 最大显示距离，超过此距离隐藏所有标签
    }
  }

  /**
   * 获取道路优先级配置
   */
  getRoadPriorityConfig() {
    return {
      // 道路等级字体缩放倍数（主干道放大）
      roadTypeScaleMultipliers: {
        motorway: 1.8, // 高速公路放大1.8倍
        trunk: 1.6, // 快速路放大1.6倍
        primary: 1.5, // 主干道放大1.5倍
        secondary: 1.3, // 次干道放大1.3倍
        tertiary: 1.1, // 支路放大1.1倍
        unclassified: 1.0, // 未分类道路保持原大小
        residential: 0.9, // 住宅道路稍微缩小
        service: 0.8, // 服务道路缩小
      },
    }
  }

  /**
   * 计算两点间距离
   */
  calculateDistance(point1, point2) {
    const dx = point2[0] - point1[0]
    const dy = point2[1] - point1[1]
    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * 获取道路类型的字体缩放倍数
   * @param {string} roadType - 道路类型
   * @param {string} roadLevel - 道路级别
   * @returns {number} 缩放倍数
   */
  getRoadTypeScaleMultiplier(roadType, roadLevel = 'all') {
    if (!this.styleConfig.enableRoadTypeScaling) {
      return 1.0 // 如果禁用缩放，返回默认大小
    }

    // 根据道路级别确定使用的缩放规则
    let targetRoadType = roadType

    if (roadLevel === 'primary') {
      // 显示1级道路时：1级道路使用1级的大小和缩放规则
      // 只显示主要道路，保持原有缩放
      targetRoadType = roadType
    } else if (roadLevel === 'primarySecondary') {
      // 显示2级道路时：1级和2级道路都使用2级道路的大小和缩放规则
      if (['motorway', 'trunk', 'primary', 'secondary'].includes(roadType)) {
        targetRoadType = 'secondary' // 1级和2级道路都使用2级规则
      } else {
        targetRoadType = roadType // 其他道路保持原有类型
      }
    } else if (roadLevel === 'all') {
      // 显示所有道路时：所有道路都使用3级道路的大小和缩放规则
      targetRoadType = 'tertiary' // 所有道路都使用3级规则
    }

    // 使用目标道路类型的缩放倍数
    return (
      this.roadPriorityConfig.roadTypeScaleMultipliers[targetRoadType] || 1.0
    )
  }

  /**
   * 检查道路是否应该在指定级别显示
   * @param {Object} feature - 道路特征
   * @param {string} roadLevel - 道路级别 ('primary', 'primarySecondary', 'all', 'hide')
   */
  shouldShowRoadAtLevel(feature, roadLevel) {
    if (roadLevel === 'hide') {
      return false // 隐藏级别不显示任何道路
    }

    if (roadLevel === 'all') {
      return true // 显示所有道路
    }

    const roadType = feature.properties?.highway || 'unclassified'

    if (roadLevel === 'primary') {
      // 只显示主要道路
      return ['motorway', 'trunk', 'primary'].includes(roadType)
    }

    if (roadLevel === 'primarySecondary') {
      // 显示主要道路和次要道路
      return ['motorway', 'trunk', 'primary', 'secondary'].includes(roadType)
    }

    return true // 默认显示
  }

  /**
   * 根据相机距离计算标签间距倍数
   * @param {number} cameraDistance - 相机距离
   * @returns {number} 间距倍数
   */
  getDistanceMultiplier(cameraDistance) {
    // 距离越远，倍数越大，标签越少
    if (cameraDistance >= 8) {
      return 8.0 // 极远距离：标签极少
    } else if (cameraDistance >= 2) {
      return 2.0 // 中距离：标签适中
    } else if (cameraDistance >= 1) {
      return 1.5 // 近距离：标签较多
    } else {
      return 1.0 // 极近距离：标签最多
    }
  }

  /**
   * 对路段进行排序和连接，尽量形成连续的道路
   */
  sortAndConnectSegments(segments) {
    if (segments.length <= 1) {
      return segments
    }

    const result = []
    const remaining = [...segments]

    // 从第一个路段开始
    result.push(remaining.shift())

    // 尝试连接剩余路段
    while (remaining.length > 0) {
      let connected = false
      const lastSegment = result[result.length - 1]
      const lastCoords = lastSegment.coordinates
      const lastPoint = lastCoords[lastCoords.length - 1]

      // 寻找能连接到当前路段末端的路段
      for (let i = 0; i < remaining.length; i++) {
        const segment = remaining[i]
        const coords = segment.coordinates
        const startPoint = coords[0]
        const endPoint = coords[coords.length - 1]

        // 检查是否能连接（起点或终点与当前路段末端接近）
        const distanceToStart = this.calculateDistance(lastPoint, startPoint)
        const distanceToEnd = this.calculateDistance(lastPoint, endPoint)

        if (distanceToStart < 0.1 || distanceToEnd < 0.1) {
          // 100米内认为可连接
          // 如果需要反向连接，翻转坐标
          if (distanceToEnd < distanceToStart) {
            segment.coordinates = [...coords].reverse()
          }

          result.push(remaining.splice(i, 1)[0])
          connected = true
          break
        }
      }

      // 如果没有找到连接的路段，添加剩余的第一个
      if (!connected) {
        result.push(remaining.shift())
      }
    }

    return result
  }

  /**
   * 按名称合并道路特征
   */
  mergeRoadsByName(roadFeatures, roadNetworkScale) {
    const roadsByName = new Map()

    // 按道路名称分组
    roadFeatures.forEach(feature => {
      const roadName = feature.properties?.name
      if (!roadName) {
        return // 跳过没有名称的道路
      }

      const roadType = feature.properties?.highway || 'unclassified'

      if (!roadsByName.has(roadName)) {
        roadsByName.set(roadName, {
          name: roadName,
          segments: [],
          roadType: roadType,
          allPoints: [],
          totalLength: 0,
        })
      }

      const roadData = roadsByName.get(roadName)
      roadData.segments.push({
        feature,
        coordinates: feature.geometry.coordinates,
      })

      // 更新道路类型（使用第一个遇到的类型）
      if (!roadData.roadType) {
        roadData.roadType = roadType
      }
    })

    // 计算每组道路的总长度和合并所有点
    roadsByName.forEach(roadData => {
      // 对路段进行排序，尝试连接相邻的路段
      const sortedSegments = this.sortAndConnectSegments(roadData.segments)

      sortedSegments.forEach(segment => {
        const transformedCoords = transformGeoJsonCoordinates(
          segment.coordinates,
          0,
          roadNetworkScale
        )

        // 避免重复添加连接点
        if (roadData.allPoints.length > 0) {
          const lastPoint = roadData.allPoints[roadData.allPoints.length - 1]
          const firstNewPoint = transformedCoords[0]

          // 如果新路段的起点与上一路段的终点很接近，跳过起点
          const distance = this.calculateDistance(lastPoint, firstNewPoint)
          if (distance < 0.01) {
            // 10米以内认为是连接点
            roadData.allPoints.push(...transformedCoords.slice(1))
          } else {
            roadData.allPoints.push(...transformedCoords)
          }
        } else {
          roadData.allPoints.push(...transformedCoords)
        }

        // 计算段长度
        for (let i = 1; i < transformedCoords.length; i++) {
          roadData.totalLength += this.calculateDistance(
            transformedCoords[i - 1],
            transformedCoords[i]
          )
        }
      })
    })

    return roadsByName
  }

  /**
   * 生成优化的标签位置
   */
  generateOptimizedLabels(mergedRoads, cameraDistance, roadLevel = 'all') {
    const labels = []
    const existingLabels = new Map() // 用于跟踪已存在的标签位置

    // 直接遍历所有道路，不需要按重要性排序
    const sortedRoads = Array.from(mergedRoads.values())

    for (const roadData of sortedRoads) {
      // 道路级别过滤已经在renderLabels方法中完成，这里不需要再次检查

      // 根据道路总长度和道路级别动态计算标签数量
      let labelCount = 1 // 默认至少1个标签

      // 根据道路类型调整标签密度
      const roadType = roadData.roadType
      let spacing = 0.5 // 默认间距500米

      // 根据相机距离动态调整标签间距
      const distanceMultiplier = this.getDistanceMultiplier(cameraDistance)

      // 根据道路类型设置基础间距
      let baseSpacing
      if (['motorway', 'trunk', 'primary'].includes(roadType)) {
        baseSpacing = roadLevel === 'primary' ? 2.0 : 1.0
      } else if (roadType === 'secondary') {
        baseSpacing = roadLevel === 'primarySecondary' ? 1.5 : 0.8
      } else if (roadType === 'tertiary') {
        baseSpacing = 1.0
      } else if (['unclassified', 'residential'].includes(roadType)) {
        baseSpacing = 0.6
      } else {
        baseSpacing = 0.8
      }

      // 应用距离倍数
      spacing = baseSpacing * distanceMultiplier

      if (roadData.totalLength > spacing) {
        labelCount = Math.ceil(roadData.totalLength / spacing)
      }

      // 确保至少有1个标签，但不设置上限
      labelCount = Math.max(1, labelCount)

      // 生成标签位置
      if (roadData.allPoints.length > 0) {
        const scaleMultiplier = this.getRoadTypeScaleMultiplier(
          roadData.roadType,
          roadLevel
        )

        for (let i = 0; i < labelCount; i++) {
          let labelPos

          if (labelCount === 1) {
            // 单个标签时，计算真正的几何中心位置
            labelPos = this.calculateGeometricCenter(roadData.allPoints)
          } else {
            // 多个标签时，使用改进的位置计算
            labelPos = this.calculateOptimalLabelPosition(
              roadData.allPoints,
              i,
              labelCount
            )
          }

          if (labelPos) {
            // 检查是否与现有标签重叠
            const isOverlapping = this.checkLabelOverlap(
              roadData.name,
              labelPos,
              existingLabels
            )

            // 如果不重叠，添加标签
            if (!isOverlapping) {
              labels.push({
                text: roadData.name,
                position: labelPos,
                roadType: roadData.roadType,
                scaleMultiplier: scaleMultiplier,
              })

              // 记录已添加的标签位置（使用唯一键避免同名道路冲突）
              const uniqueKey = `${roadData.name}_${labelPos[0].toFixed(
                3
              )}_${labelPos[1].toFixed(3)}`
              existingLabels.set(uniqueKey, labelPos)
            } else {
              // 标签重叠被过滤，静默处理
            }
          } else {
            // 无法计算标签位置，静默处理
          }
        }
      }
    }

    return labels
  }

  /**
   * 检查标签是否与现有标签重叠（简化版：只检查道路标签间重叠，忽略区域标签）
   * @param {string} roadName - 道路名称
   * @param {Array} position - 标签位置
   * @param {Map} existingLabels - 已存在的道路标签
   * @returns {boolean} 是否重叠
   */
  checkLabelOverlap(roadName, position, existingLabels) {
    const roadOverlapThreshold = 0.08 // 道路标签间80米内认为重叠

    // 只检查与其他道路标签的重叠
    // 由于existingLabels只包含道路标签，所以不需要类型判断
    for (const [existingKey, existingPos] of existingLabels) {
      const distance = this.calculateDistance(position, existingPos)

      // 如果是同名道路，允许更近的距离
      if (existingKey.startsWith(roadName + '_')) {
        if (distance < 0.04) {
          // 同名道路40米内认为重叠
          return true
        }
      } else {
        // 不同名道路使用正常阈值
        if (distance < roadOverlapThreshold) {
          return true
        }
      }
    }

    return false
  }

  /**
   * 创建3D文字标签对象（增强版，支持缩放）
   */
  createLabel(text, position, options = {}) {
    // 创建高分辨率画布来绘制文字
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')

    // 设置高分辨率画布大小和文字样式
    const fontSize = 64 // 增大字体大小，提高清晰度
    canvas.width = 1280 // 进一步提高分辨率
    canvas.height = 320

    context.fillStyle = 'rgba(0, 0, 0, 0)' // 透明背景
    context.fillRect(0, 0, canvas.width, canvas.height)

    // 启用抗锯齿和高质量渲染
    context.imageSmoothingEnabled = true
    context.imageSmoothingQuality = 'high'
    context.textRenderingOptimization = 'optimizeQuality'

    context.font = `${fontSize}px Microsoft YaHei, Arial, sans-serif`
    // 将十六进制颜色转换为CSS颜色字符串
    const colorHex = options.color || this.styleConfig.color
    const colorString = `#${colorHex.toString(16).padStart(6, '0')}`
    context.fillStyle = colorString
    context.textAlign = 'center'
    context.textBaseline = 'middle'

    // 绘制文字
    context.fillText(text, canvas.width / 2, canvas.height / 2)

    // 创建纹理
    const texture = new THREE.CanvasTexture(canvas)
    texture.needsUpdate = true

    // 创建材质
    const material = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      alphaTest: 0.1,
      side: THREE.DoubleSide,
    })

    // 计算最终大小（应用缩放倍数）
    const baseSize = this.visibilityConfig.baseFontSize
    const scaleMultiplier = options.scaleMultiplier || 1.0
    const finalWidth = baseSize * scaleMultiplier
    const finalHeight = finalWidth * 0.25

    // 创建几何体（平面）- 应用缩放
    const geometry = new THREE.PlaneGeometry(finalWidth, finalHeight)

    // 创建网格
    const mesh = new THREE.Mesh(geometry, material)

    // 设置位置
    if (Array.isArray(position)) {
      mesh.position.set(position[0], position[1], position[2] || position[1])
    } else if (position instanceof THREE.Vector3) {
      mesh.position.copy(position)
    }

    // 让平面水平放置（贴在地面上）
    mesh.rotation.x = -Math.PI / 2

    // 设置用户数据
    mesh.userData = {
      roadName: text,
      roadType: options.roadType || 'default',
      source: options.source || 'smart-system',
      isRoadLabel: true,
      isScalableLabel: true, // 新增：标记为可缩放标签
      scaleMultiplier: scaleMultiplier,
      baseScale: finalWidth, // 新增：保存基础缩放大小
      ...options.userData,
    }

    return mesh
  }

  /**
   * 主要渲染方法：智能渲染道路标签
   * @param {Object} geojson - 路网GeoJSON数据
   * @param {number} roadNetworkScale - 路网缩放比例
   * @param {THREE.Group} targetGroup - 目标标签组
   * @param {number} roadYLevel - 路面Y坐标
   * @param {THREE.Camera} camera - 相机对象
   * @param {string} roadLevel - 当前显示的道路级别 ('primary', 'primarySecondary', 'all', 'hide')
   */
  renderLabels(
    geojson,
    roadNetworkScale,
    targetGroup,
    roadYLevel,
    camera,
    roadLevel = 'all'
  ) {
    if (!geojson?.features || !targetGroup || !camera) {
      this.hideAllLabels(targetGroup)
      return
    }

    const cameraDistance = camera.position.y

    // 距离检查 - 超出范围时隐藏标签
    if (cameraDistance >= this.visibilityConfig.maxDistance) {
      this.hideAllLabels(targetGroup)
      return
    }

    // 过滤道路特征（只处理有名称的道路，并根据道路级别过滤）
    const roadFeatures = geojson.features.filter(
      feature =>
        feature.geometry &&
        (feature.geometry.type === 'LineString' ||
          feature.geometry.type === 'MultiLineString') &&
        feature.properties?.name &&
        this.shouldShowRoadAtLevel(feature, roadLevel)
    )

    // 如果没有符合条件的道路特征，隐藏所有标签
    if (!roadFeatures.length) {
      this.hideAllLabels(targetGroup)
      return
    }

    // 合并同名道路
    const mergedRoads = this.mergeRoadsByName(roadFeatures, roadNetworkScale)

    // 生成优化的标签数据
    const labelsData = this.generateOptimizedLabels(
      mergedRoads,
      cameraDistance,
      roadLevel
    )

    // 标签生成完成，静默处理

    // 检查是否需要重建标签对象
    const needsRebuild = this.shouldRebuildLabels(labelsData, cameraDistance)

    if (needsRebuild) {
      this.rebuildLabels(labelsData, targetGroup, roadYLevel)
    }

    // 更新标签可见性和缩放
    this.updateLabelsVisibility(labelsData, targetGroup, cameraDistance, camera)

    // 缓存当前状态
    this.lastLabelsData = labelsData
    this.lastCameraDistance = cameraDistance
  }

  /**
   * 检查是否需要重建标签
   */
  shouldRebuildLabels(labelsData, cameraDistance) {
    // 如果相机距离变化较大，需要重建
    if (Math.abs(cameraDistance - this.lastCameraDistance) > 2.0) {
      return true
    }

    // 如果标签数量变化，需要重建
    if (
      !this.lastLabelsData ||
      this.lastLabelsData.length !== labelsData.length
    ) {
      return true
    }

    // 如果标签内容变化，需要重建
    for (let i = 0; i < labelsData.length; i++) {
      const current = labelsData[i]
      const last = this.lastLabelsData[i]
      if (current.text !== last.text || current.roadType !== last.roadType) {
        return true
      }
    }

    return false
  }

  /**
   * 重建标签对象
   */
  rebuildLabels(labelsData, targetGroup, roadYLevel) {
    // 清除现有标签
    this.clearGroup(targetGroup)

    // 创建新标签
    labelsData.forEach(labelData => {
      const label = this.createLabel(
        labelData.text,
        [labelData.position[0], roadYLevel + 0.005, labelData.position[1]], // 使用传入的roadYLevel + 路网标签偏移
        {
          roadType: labelData.roadType,
          scaleMultiplier: labelData.scaleMultiplier,
        }
      )

      targetGroup.add(label)
    })
  }

  /**
   * 更新标签可见性和缩放
   */
  updateLabelsVisibility(labelsData, targetGroup, _cameraDistance, camera) {
    targetGroup.children.forEach((label, index) => {
      if (index < labelsData.length) {
        // 标签可见性由道路级别控制，这里只需要应用动态缩放
        label.visible = true

        // 应用动态缩放
        if (camera) {
          this._applyScaleToLabel(label, camera)
        }
      } else {
        label.visible = false
      }
    })
  }

  /**
   * 应用缩放到单个标签（完全按照算法执行）
   */
  _applyScaleToLabel(label, camera) {
    if (!camera || !label.userData.isScalableLabel) {
      return
    }

    const distance = camera.position.distanceTo(label.position)
    // 完全按照算法执行，不限制最大和最小缩放
    const baseDistance = 5
    const rawScale = distance / baseDistance
    // 使用平方根让远距离缩放更激进
    const dynamicScale = Math.sqrt(rawScale) * 0.6

    // 结合道路类型的缩放倍数
    const roadTypeScale = label.userData.scaleMultiplier || 1.0
    const finalScale = dynamicScale * roadTypeScale

    // 添加缩放下限和上限
    const minScale = 0.2 // 最小缩放比例
    const maxScale = 4.0 // 最大缩放比例
    const clampedScale = Math.max(minScale, Math.min(maxScale, finalScale))

    label.scale.set(clampedScale, clampedScale, 1)
  }

  /**
   * 隐藏所有标签
   */
  hideAllLabels(targetGroup) {
    if (targetGroup) {
      targetGroup.children.forEach(label => {
        label.visible = false
      })
    }
  }

  /**
   * 清除组中的所有对象
   */
  clearGroup(group) {
    if (!group) {
      return
    }

    while (group.children.length > 0) {
      const child = group.children[0]
      group.remove(child)

      // 清理材质和纹理
      if (child.material) {
        if (child.material.map) {
          child.material.map.dispose()
        }
        child.material.dispose()
      }

      // 清理几何体
      if (child.geometry) {
        child.geometry.dispose()
      }
    }
  }

  /**
   * 获取标签数据（用于兼容现有接口）
   */
  getLabelsData(geojson, roadNetworkScale) {
    if (!geojson?.features) {
      return []
    }

    const roadFeatures = geojson.features.filter(
      feature =>
        feature.geometry &&
        (feature.geometry.type === 'LineString' ||
          feature.geometry.type === 'MultiLineString') &&
        feature.properties?.name
    )

    const mergedRoads = this.mergeRoadsByName(roadFeatures, roadNetworkScale)
    const labelsData = this.generateOptimizedLabels(mergedRoads, 10.0) // 使用中等距离

    return labelsData
  }

  /**
   * 更新所有标签的缩放（用于相机移动时调用）
   */
  updateLabelsScale(targetGroup, camera) {
    if (!targetGroup || !camera || targetGroup.children.length === 0) {
      return
    }

    // 批量处理，减少函数调用开销
    const cameraPosition = camera.position

    targetGroup.children.forEach(label => {
      if (label.visible && label.userData.isScalableLabel) {
        // 内联缩放计算，避免额外的函数调用
        const distance = cameraPosition.distanceTo(label.position)
        const baseDistance = 5
        const rawScale = distance / baseDistance
        const dynamicScale = Math.sqrt(rawScale) * 0.6
        const roadTypeScale = label.userData.scaleMultiplier || 1.0
        const finalScale = dynamicScale * roadTypeScale

        label.scale.set(finalScale, finalScale, 1)
      }
    })
  }

  /**
   * 计算多个标签的最优位置
   * @param {Array} points - 道路点数组
   * @param {number} labelIndex - 当前标签索引
   * @param {number} totalLabels - 总标签数量
   * @returns {Array} 标签位置坐标
   */
  calculateOptimalLabelPosition(points, labelIndex, totalLabels) {
    if (!points || points.length === 0) {
      return null
    }

    if (points.length === 1) {
      return points[0]
    }

    // 计算道路的总长度和累积长度
    const segmentLengths = []
    let totalLength = 0

    for (let i = 1; i < points.length; i++) {
      const segmentLength = this.calculateDistance(points[i - 1], points[i])
      segmentLengths.push(segmentLength)
      totalLength += segmentLength
    }

    // 改进的缓冲区计算：根据标签数量动态调整
    let bufferRatio = this.visibilityConfig.bufferRatio

    // 如果标签数量较多，减少缓冲区以充分利用道路长度
    if (totalLabels > 3) {
      bufferRatio = Math.max(0.05, bufferRatio * 0.5)
    }

    // 计算可用范围和标签位置
    const availableRange = 1 - 2 * bufferRatio
    const position =
      bufferRatio + (labelIndex / (totalLabels - 1)) * availableRange

    // 将位置转换为实际的道路长度位置
    const targetLength = position * totalLength
    let accumulatedLength = 0

    for (let i = 0; i < segmentLengths.length; i++) {
      const nextAccumulatedLength = accumulatedLength + segmentLengths[i]

      if (nextAccumulatedLength >= targetLength) {
        // 目标位置在这个线段上
        const remainingLength = targetLength - accumulatedLength
        const ratio = remainingLength / segmentLengths[i]

        const startPoint = points[i]
        const endPoint = points[i + 1]

        // 在线段上插值计算精确位置
        return [
          startPoint[0] + (endPoint[0] - startPoint[0]) * ratio,
          startPoint[1] + (endPoint[1] - startPoint[1]) * ratio,
        ]
      }

      accumulatedLength = nextAccumulatedLength
    }

    // 如果计算出现问题，使用简单的索引方法
    const fallbackIndex = Math.floor(position * (points.length - 1))
    return points[Math.min(fallbackIndex, points.length - 1)]
  }

  /**
   * 计算道路点的几何中心位置（简化版：更可靠的中心计算）
   */
  calculateGeometricCenter(points) {
    if (!points || points.length === 0) {
      return null
    }

    if (points.length === 1) {
      return [...points[0]] // 返回副本避免引用问题
    }

    if (points.length === 2) {
      // 两个点的情况，直接计算中点
      return [
        (points[0][0] + points[1][0]) / 2,
        (points[0][1] + points[1][1]) / 2,
      ]
    }

    // 多个点的情况：使用基于路径长度的中点计算
    const segmentLengths = []
    let totalLength = 0

    // 计算每段的长度，添加错误检查
    for (let i = 1; i < points.length; i++) {
      const point1 = points[i - 1]
      const point2 = points[i]

      // 检查点的有效性
      if (!point1 || !point2 || point1.length < 2 || point2.length < 2) {
        continue
      }

      const segmentLength = this.calculateDistance(point1, point2)
      segmentLengths.push(segmentLength)
      totalLength += segmentLength
    }

    // 如果总长度太短或没有有效段，使用简单的中点
    if (totalLength < 0.001 || segmentLengths.length === 0) {
      const middleIndex = Math.floor(points.length / 2)
      return [...points[middleIndex]] // 返回副本
    }

    // 找到路径长度的中点
    const halfLength = totalLength / 2
    let accumulatedLength = 0

    for (let i = 0; i < segmentLengths.length; i++) {
      const nextAccumulatedLength = accumulatedLength + segmentLengths[i]

      if (nextAccumulatedLength >= halfLength) {
        // 中点在这个线段上
        const remainingLength = halfLength - accumulatedLength
        const ratio =
          segmentLengths[i] > 0 ? remainingLength / segmentLengths[i] : 0

        const startPoint = points[i]
        const endPoint = points[i + 1]

        // 在线段上插值计算精确位置
        return [
          startPoint[0] + (endPoint[0] - startPoint[0]) * ratio,
          startPoint[1] + (endPoint[1] - startPoint[1]) * ratio,
        ]
      }

      accumulatedLength = nextAccumulatedLength
    }

    // 如果计算失败，使用中间索引的点作为后备
    const middleIndex = Math.floor(points.length / 2)
    return [...points[middleIndex]] // 返回副本
  }

  /**
   * 计算点到线段的最近点
   * @param {Array} point - 目标点 [x, y]
   * @param {Array} segmentStart - 线段起点 [x, y]
   * @param {Array} segmentEnd - 线段终点 [x, y]
   * @returns {Array} 线段上最近的点 [x, y]
   */
  getClosestPointOnSegment(point, segmentStart, segmentEnd) {
    const [px, py] = point
    const [sx, sy] = segmentStart
    const [ex, ey] = segmentEnd

    // 计算线段向量
    const segmentX = ex - sx
    const segmentY = ey - sy

    // 计算线段长度的平方
    const segmentLengthSquared = segmentX * segmentX + segmentY * segmentY

    // 如果线段长度为0（起点和终点相同），返回起点
    if (segmentLengthSquared === 0) {
      return [sx, sy]
    }

    // 计算投影参数 t
    const t = Math.max(
      0,
      Math.min(
        1,
        ((px - sx) * segmentX + (py - sy) * segmentY) / segmentLengthSquared
      )
    )

    // 计算最近点
    const closestX = sx + t * segmentX
    const closestY = sy + t * segmentY

    return [closestX, closestY]
  }

  /**
   * 计算道路的跨度（用于确定合理的偏离距离）
   * @param {Array} points - 道路点数组
   * @returns {number} 道路跨度
   */
  calculateRoadSpan(points) {
    if (!points || points.length === 0) {
      return 0
    }

    let minX = Infinity,
      maxX = -Infinity
    let minY = Infinity,
      maxY = -Infinity

    points.forEach(point => {
      minX = Math.min(minX, point[0])
      maxX = Math.max(maxX, point[0])
      minY = Math.min(minY, point[1])
      maxY = Math.max(maxY, point[1])
    })

    return Math.max(maxX - minX, maxY - minY)
  }
}

export default SmartRoadLabelManager
