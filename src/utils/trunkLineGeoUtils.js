/**
 * 干线地理数据处理工具函数
 * 精简版：移除过度防御性编程，专注核心功能
 */

/**
 * 从路网数据中提取干线地理数据
 * 精简版：移除过度防御性检查，使用统一数据源处理
 */
export function extractTrunkLineGeoData(
  selectedRoadIds,
  roadNetworkData,
  dynamicRoadLoader = null
) {
  const features = []
  const processedIds = new Set()

  // 统一数据源处理，避免重复逻辑
  const dataSources = [
    roadNetworkData?.features,
    dynamicRoadLoader?.allRoadFeatures,
  ].filter(Boolean)

  selectedRoadIds.forEach(roadId => {
    if (processedIds.has(roadId)) {
      return
    }

    for (const source of dataSources) {
      const feature = findRoadFeatureById(roadId, source)
      if (feature) {
        const copiedFeature = createIndependentFeature(feature, roadId)
        if (copiedFeature) {
          features.push(copiedFeature)
          processedIds.add(roadId)
          break
        }
      }
    }
  })

  const geoData = {
    type: 'FeatureCollection',
    features,
    metadata: {
      extractedAt: new Date().toISOString(),
      sourceRoadIds: [...selectedRoadIds],
      foundCount: features.length,
      notFoundCount: selectedRoadIds.length - features.length,
    },
  }

  return geoData
}

/**
 * 根据ID查找路段特征
 * @param {string} roadId - 路段ID
 * @param {Array} features - 特征数组
 * @returns {Object|null} 找到的特征或null
 */
function findRoadFeatureById(roadId, features) {
  return features.find(feature => {
    // 统一的ID匹配函数，避免重复代码
    const matchId = targetId => {
      return (
        feature.id === targetId ||
        feature.properties?.['@id'] === targetId ||
        feature.properties?.id === targetId ||
        feature.properties?.osm_id === targetId
      )
    }

    // 直接匹配
    if (matchId(roadId)) {
      return true
    }

    // 处理 way/数字 格式的ID
    if (typeof roadId === 'string' && roadId.includes('/')) {
      const numericId = roadId.split('/')[1]
      const wayId = `way/${numericId}`

      if (matchId(numericId) || matchId(wayId)) {
        return true
      }
    }

    // 反向匹配：如果feature的ID是way/数字格式，而roadId是纯数字
    if (typeof feature.id === 'string' && feature.id.includes('/')) {
      const featureNumericId = feature.id.split('/')[1]
      if (featureNumericId === roadId) {
        return true
      }
    }

    // 对于临时ID的匹配
    if (feature.userData && feature.userData.featureId === roadId) {
      return true
    }
    if (feature.userData && feature.userData.tempId === roadId) {
      return true
    }

    return false
  })
}

/**
 * 创建独立的特征副本
 * @param {Object} originalFeature - 原始特征
 * @param {string} roadId - 路段ID
 * @returns {Object} 独立的特征副本
 */
function createIndependentFeature(originalFeature, roadId) {
  // 深度复制原始特征，确保完全独立
  const copiedFeature = JSON.parse(JSON.stringify(originalFeature))

  // 创建完全独立的干线属性，清除原始路网标识
  const trunkFeatureId = `trunk_feature_${Date.now()}_${Math.random()
    .toString(36)
    .substring(2, 11)}`

  copiedFeature.properties = {
    // 干线特征标识
    source: 'trunk_line',
    originalId: roadId,
    copiedAt: new Date().toISOString(),
    // 保留基本道路信息用于显示
    highway: originalFeature.properties?.highway || 'trunk_line',
    name: `干线路段_${roadId}`,
    // 生成新的干线特征ID
    trunkFeatureId: trunkFeatureId,
    // 干线特有属性
    isTrunkLineSegment: true,
    trunkLineCreated: true,
    // 移除所有原始路网相关的ID和标识
    // 不保留原始的 @id, osm_id 等字段
  }

  // 更新特征ID为干线特征ID
  copiedFeature.id = trunkFeatureId

  // 确保几何数据完整性
  if (!copiedFeature.geometry || !copiedFeature.geometry.coordinates) {
    console.warn(`路段 ${roadId} 缺少几何数据`)
    return null
  }

  // 验证坐标数据
  if (!validateGeometryCoordinates(copiedFeature.geometry)) {
    console.warn(`路段 ${roadId} 的几何数据无效`)
    return null
  }

  return copiedFeature
}

/**
 * 验证几何坐标数据
 * 精简版：移除过度验证，只检查基本结构
 */
function validateGeometryCoordinates(geometry) {
  return (
    geometry?.coordinates &&
    (geometry.type === 'LineString' || geometry.type === 'MultiLineString')
  )
}

/**
 * 优化干线地理数据
 * 精简版：只保留去重功能，移除未实现的功能
 */
export function optimizeTrunkLineGeoData(geoData, options = {}) {
  const { removeDuplicates = true } = options

  let features = [...geoData.features]

  if (removeDuplicates) {
    features = removeDuplicateFeatures(features)
  }

  return {
    ...geoData,
    features,
    metadata: {
      ...geoData.metadata,
      optimizedAt: new Date().toISOString(),
      originalFeatureCount: geoData.features.length,
      optimizedFeatureCount: features.length,
    },
  }
}

/**
 * 移除重复的特征
 * @param {Array} features - 特征数组
 * @returns {Array} 去重后的特征数组
 */
function removeDuplicateFeatures(features) {
  const seen = new Set()
  return features.filter(feature => {
    const key = JSON.stringify(feature.geometry.coordinates)
    if (seen.has(key)) {
      return false
    }
    seen.add(key)
    return true
  })
}

/**
 * 验证提取的地理数据
 * 精简版：只检查基本结构，移除过度验证
 */
export function validateExtractedGeoData(geoData) {
  const errors = []

  if (!geoData?.features?.length) {
    errors.push('缺少地理数据')
  }

  return { isValid: errors.length === 0, errors, warnings: [] }
}
