import axios from 'axios'
import { getBaseUrl } from '@/config/server.config'
import { message } from 'ant-design-vue'

/**
 * 道路删除服务
 * 提供统一的道路删除功能，包括前端清理、服务器同步和数据持久化
 */
class RoadDeleteService {
  constructor() {
    this.isOperationInProgress = false
  }

  /**
   * 删除道路的完整流程
   * @param {Object} options - 删除选项
   * @param {string} options.roadId - 道路ID
   * @param {string} options.roadName - 道路名称
   * @param {Object} options.object3D - 3D对象引用
   * @param {Object} options.roadNetworkGroup - 路网组引用
   * @param {Object} options.dynamicRoadLoader - 动态道路加载器引用
   * @param {Object} options.roadNetworkData - 路网数据引用
   * @param {Object} options.sceneManager - 场景管理器引用
   * @param {Function} options.saveToIndexedDB - 保存到IndexedDB的函数
   * @param {Function} options.eventBus - 事件总线
   * @param {Function} options.onProgress - 进度回调函数
   * @returns {Promise<Object>} 删除结果
   */
  async deleteRoad(options) {
    const {
      roadId,
      roadName,
      object3D,
      roadNetworkGroup,
      dynamicRoadLoader,
      roadNetworkData,
      sceneManager,
      saveToIndexedDB,
      eventBus,
      onProgress,
    } = options

    try {
      // 防止并发操作
      if (this.isOperationInProgress) {
        throw new Error('道路操作正在进行中，请稍候...')
      }

      this.isOperationInProgress = true
      onProgress('开始删除道路...')

      // 步骤1：从3D场景中移除道路对象
      await this.removeFrom3DScene(object3D, roadName)
      onProgress('已从3D场景中移除道路')

      // 步骤2：从路网组中移除道路
      await this.removeFromRoadNetworkGroup(roadNetworkGroup, roadId, roadName)
      onProgress('已从路网组中移除道路')

      // 步骤3：从动态路网加载器中移除道路
      await this.removeFromDynamicRoadLoader(
        dynamicRoadLoader,
        roadId,
        roadName
      )
      onProgress('已从动态路网加载器中移除道路')

      // 步骤4：从路网数据中移除道路并更新服务器
      const deleteResult = await this.removeFromRoadNetworkData(
        roadNetworkData,
        roadId,
        roadName,
        saveToIndexedDB,
        eventBus,
        onProgress
      )

      // 步骤5：尝试触发渲染（不再使用setTimeout分片）
      if (sceneManager) {
        try {
          sceneManager.startAnimationLoop()
        } catch (error) {
          // 渲染失败不阻断流程
        }
      }

      onProgress('道路删除完成')

      return {
        success: true,
        deletedCount: deleteResult.deletedCount,
        serverUpdateSuccess: deleteResult.serverUpdateSuccess,
        message: deleteResult.message,
      }
    } catch (error) {
      const errorMessage = error.message || '删除道路失败'
      message.error(errorMessage)
      return {
        success: false,
        error: errorMessage,
      }
    } finally {
      this.isOperationInProgress = false
    }
  }

  /**
   * 从3D场景中移除道路对象
   */
  async removeFrom3DScene(object3D, roadName) {
    if (object3D && object3D.parent) {
      object3D.parent.remove(object3D)
    }
  }

  /**
   * 从路网组中移除道路
   */
  async removeFromRoadNetworkGroup(roadNetworkGroup, roadId, roadName) {
    if (!roadNetworkGroup) return

    const roadsToRemove = []
    roadNetworkGroup.traverse(child => {
      if (
        child.userData &&
        this.isRoadMatch(child.userData, roadId, roadName)
      ) {
        roadsToRemove.push(child)
      }
    })

    roadsToRemove.forEach(road => {
      roadNetworkGroup.remove(road)
    })
  }

  /**
   * 从动态路网加载器中移除道路
   */
  async removeFromDynamicRoadLoader(dynamicRoadLoader, roadId, roadName) {
    if (!dynamicRoadLoader || !dynamicRoadLoader.renderedRoadGroups) return

    Object.values(dynamicRoadLoader.renderedRoadGroups).forEach(group => {
      if (group && group.children.length > 0) {
        const roadsToRemove = []
        group.traverse(child => {
          if (
            child.userData &&
            this.isRoadMatch(child.userData, roadId, roadName)
          ) {
            roadsToRemove.push(child)
          }
        })

        roadsToRemove.forEach(road => {
          group.remove(road)
        })
      }
    })
  }

  /**
   * 从路网数据中移除道路并更新服务器
   */
  async removeFromRoadNetworkData(
    roadNetworkData,
    roadId,
    roadName,
    saveToIndexedDB,
    eventBus,
    onProgress
  ) {
    if (!roadNetworkData.features) {
      throw new Error('路网数据格式错误')
    }

    const originalLength = roadNetworkData.features.length
    roadNetworkData.features = roadNetworkData.features.filter(feature => {
      const featureName = feature.properties.name
      const featureId = feature.id || feature.properties.id
      return !(featureName === roadName || featureId === roadId)
    })

    const deletedCount = originalLength - roadNetworkData.features.length
    if (deletedCount === 0) {
      throw new Error('在路网中找不到对应的道路')
    }

    onProgress(`已从路网数据中移除 ${deletedCount} 个道路要素`)

    // 更新服务器
    const serverUpdateResult = await this.updateServer(
      roadNetworkData,
      onProgress
    )

    // 更新本地数据
    await this.updateLocalData(
      roadNetworkData,
      serverUpdateResult.finalData,
      saveToIndexedDB,
      eventBus,
      onProgress
    )

    return {
      deletedCount,
      serverUpdateSuccess: serverUpdateResult.success,
      message: serverUpdateResult.message,
    }
  }

  /**
   * 更新服务器数据
   */
  async updateServer(roadNetworkData, onProgress) {
    const API_BASE_URL = getBaseUrl('geoData')
    let serverUpdateSuccess = true
    let finalData = roadNetworkData

    try {
      onProgress('正在同步到服务器...')
      const serverNetworkId = 1

      await axios.post(`${API_BASE_URL}/road_networks`, {
        network_id: serverNetworkId,
        road_network: roadNetworkData,
      })

      onProgress('服务器同步成功')
    } catch (serverError) {
      serverUpdateSuccess = false
      onProgress('服务器同步失败，将使用本地数据')
    }

    // 重新获取服务器数据
    try {
      onProgress('正在获取服务器最新数据...')
      const serverNetworkId = 1
      const refreshResponse = await axios.get(
        `${API_BASE_URL}/road_networks/${serverNetworkId}`
      )

      let refreshedData = null
      if (refreshResponse.data && refreshResponse.data.road_network) {
        refreshedData = refreshResponse.data.road_network
      } else if (
        refreshResponse.data &&
        refreshResponse.data.type === 'FeatureCollection'
      ) {
        refreshedData = refreshResponse.data
      } else if (
        refreshResponse.data &&
        typeof refreshResponse.data === 'object'
      ) {
        refreshedData = refreshResponse.data
      }

      if (refreshedData) {
        finalData = refreshedData
        onProgress('已获取服务器最新数据')
      }
    } catch (refreshError) {
      // 获取失败则维持本地删除后的数据
    }

    return {
      success: serverUpdateSuccess,
      finalData,
    }
  }

  /**
   * 更新本地数据
   */
  async updateLocalData(
    roadNetworkData,
    finalData,
    saveToIndexedDB,
    eventBus,
    onProgress
  ) {
    try {
      onProgress('正在更新本地数据...')
      await saveToIndexedDB('roadNetworks', 'default', finalData)

      // 更新原始数据引用
      Object.assign(roadNetworkData, finalData)

      // 触发路网更新事件
      eventBus.emit('roadNetworkUpdated')
      onProgress('本地数据更新完成')
    } catch (updateError) {
      throw new Error('删除道路后更新本地数据失败')
    }
  }

  /**
   * 检查道路是否匹配
   */
  isRoadMatch(userData, roadId, roadName) {
    return (
      userData.featureId === roadId ||
      userData.featureName === roadName ||
      userData.id === roadId ||
      userData.tempId === roadId
    )
  }

  /**
   * 检查是否正在操作中
   */
  get isInProgress() {
    return this.isOperationInProgress
  }
}

// 创建单例实例
const roadDeleteService = new RoadDeleteService()

export default roadDeleteService
