/**
 * 预览线管理器
 * 负责在绘制模式下显示已绘制点之间的连接线
 */

import * as THREE from 'three'
import { COORDINATE_CONFIG } from './coordinateTransform.js'

// 预览线样式配置
const PREVIEW_STYLES = {
  road: {
    color: 0xff0000, // 红色
    opacity: 0.8,
  },
  area: {
    color: 0x00ff00, // 绿色
    opacity: 0.8,
  },
}

export class PreviewLineManager {
  constructor(sceneManager) {
    this.sceneManager = sceneManager
    this.scene = sceneManager.getScene()

    // 预览线状态
    this.isActive = false
    this.drawingType = null
    this.drawingPoints = []

    // Three.js 对象
    this.previewLine = null
    this.previewMaterial = null
    this.previewGeometry = null
  }

  /**
   * 开始预览模式 - 显示已绘制点的连接线
   * @param {Array<THREE.Vector3>} drawingPoints - 已绘制的点数组
   * @param {string} drawingType - 绘制类型：'road' 或 'area'
   */
  startPreview(drawingPoints, drawingType = 'road') {
    if (!drawingPoints || drawingPoints.length === 0 || !this.scene) {
      return false
    }

    // 清理之前的预览线
    this.endPreview()

    this.isActive = true
    this.drawingType = drawingType
    this.drawingPoints = drawingPoints.map(point => point.clone())

    try {
      this._createPreviewLine()
      return true
    } catch (error) {
      console.error('PreviewLineManager: 创建预览线失败', error)
      return false
    }
  }

  /**
   * 结束预览模式并清理资源
   */
  endPreview() {
    if (!this.isActive) {
      return
    }

    if (this.previewLine && this.scene) {
      this.scene.remove(this.previewLine)
    }

    this._cleanupPreviewLine()

    this.isActive = false
    this.drawingType = null
    this.drawingPoints = []
  }

  /**
   * 检查预览线是否处于活动状态
   */
  isPreviewActive() {
    return this.isActive
  }

  /**
   * 创建预览线对象
   * @private
   */
  _createPreviewLine() {
    const style = PREVIEW_STYLES[this.drawingType] || PREVIEW_STYLES.road

    this.previewMaterial = this._createPreviewMaterial(style)
    this.previewGeometry = new THREE.BufferGeometry()
    this.previewLine = new THREE.Line(
      this.previewGeometry,
      this.previewMaterial
    )

    this.previewLine.userData = {
      isPreviewLine: true,
      drawingType: this.drawingType,
      createdAt: Date.now(),
    }

    this._updatePreviewLineGeometry()
    this.scene.add(this.previewLine)
    this.sceneManager.markNeedsRender()
  }

  /**
   * 创建预览线材质
   * @private
   */
  _createPreviewMaterial(style) {
    return new THREE.LineBasicMaterial({
      color: new THREE.Color(style.color),
      opacity: style.opacity,
      transparent: true,
      fog: false,
    })
  }

  /**
   * 更新预览线几何体
   * @private
   */
  _updatePreviewLineGeometry() {
    if (
      !this.previewGeometry ||
      !this.drawingPoints ||
      this.drawingPoints.length === 0
    ) {
      return
    }

    const yLevel =
      this.drawingType === 'road'
        ? COORDINATE_CONFIG.Y_LEVELS.ROADS + 0.001
        : COORDINATE_CONFIG.Y_LEVELS.AREAS + 0.001

    const points = this.drawingPoints.map(
      point => new THREE.Vector3(point.x, yLevel, point.z)
    )

    this.previewGeometry.setFromPoints(points)
    this.sceneManager.markNeedsRender()
  }

  /**
   * 清理预览线资源
   * @private
   */
  _cleanupPreviewLine() {
    if (this.previewGeometry) {
      this.previewGeometry.dispose()
      this.previewGeometry = null
    }

    if (this.previewMaterial) {
      this.previewMaterial.dispose()
      this.previewMaterial = null
    }

    this.previewLine = null
  }

  /**
   * 销毁管理器并清理所有资源
   */
  dispose() {
    this.endPreview()
    this.sceneManager = null
    this.scene = null
  }
}

export default PreviewLineManager
