/**
 * 干线渲染器
 * 创建独立的干线渲染组件，支持自定义颜色、线宽、样式等
 * 在路网上层进行渲染，避免与路网渲染冲突
 */

import * as THREE from 'three'
import {
  transformGeoJsonCoordinates,
  COORDINATE_CONFIG,
} from './coordinateTransform.js'
import { DEFAULT_TRUNK_LINE_STYLE } from './trunkLineTypes.js'

/**
 * 干线渲染器类
 */
export class TrunkLineRenderer {
  constructor(sceneManager, options = {}) {
    this.sceneManager = sceneManager
    this.scene = sceneManager.getScene()

    // 配置选项
    this.options = {
      roadNetworkScale: options.roadNetworkScale || 1,
      yLevel: options.yLevel || COORDINATE_CONFIG.Y_LEVELS.TRUNK_LINES, // 使用配置的干线层级
      defaultLineWidth:
        options.defaultLineWidth || DEFAULT_TRUNK_LINE_STYLE.lineWidth,
      defaultOpacity:
        options.defaultOpacity || DEFAULT_TRUNK_LINE_STYLE.opacity,
      ...options,
    }

    // 动态宽度相关配置
    this.dynamicWidth = {
      enabled: true, // 启用动态宽度
      baseLineWidthFactor: 0.05, // 基础线宽转换因子，与路网保持一致
      distanceScaleFactor: 0.3, // 距离缩放因子，控制宽度变化幅度
      minWidthMultiplier: 0.4, // 最小宽度倍数（远距离时），从0.3提高到0.4确保可见性
      maxWidthMultiplier: 1.2, // 最大宽度倍数（近距离时），从1.5降到1.2避免过宽
      lastCameraDistance: null, // 缓存上次的相机距离
      updateThreshold: 0.5, // 距离变化阈值，避免频繁更新
    }

    // 干线组，用于管理所有干线对象
    this.trunkLinesGroup = new THREE.Group()
    this.trunkLinesGroup.name = 'TrunkLines'
    this.scene.add(this.trunkLinesGroup)

    // 存储渲染的干线对象
    this.renderedTrunkLines = new Map() // key: trunkLineId, value: { group, lines, materials }

    // 材质缓存
    this.materialCache = new Map()
  }

  /**
   * 根据相机距离计算动态宽度倍数
   * @param {number} cameraDistance - 相机距离
   * @returns {number} 宽度倍数
   */
  calculateDynamicWidthMultiplier(cameraDistance) {
    if (!this.dynamicWidth.enabled) return 1.0

    // 距离范围：0.5-20米，对应宽度倍数从最大到最小
    const minDistance = 0.5
    const maxDistance = 20

    // 限制距离范围
    const clampedDistance = Math.max(
      minDistance,
      Math.min(maxDistance, cameraDistance)
    )

    // 计算归一化距离 (0-1)
    const normalizedDistance =
      (clampedDistance - minDistance) / (maxDistance - minDistance)

    // 使用平滑曲线计算宽度倍数（距离越近宽度越大）
    const widthMultiplier =
      this.dynamicWidth.maxWidthMultiplier -
      normalizedDistance *
        (this.dynamicWidth.maxWidthMultiplier -
          this.dynamicWidth.minWidthMultiplier)

    return widthMultiplier
  }

  /**
   * 检查是否需要更新宽度
   * @param {number} cameraDistance - 当前相机距离
   * @returns {boolean} 是否需要更新
   */
  shouldUpdateWidth(cameraDistance) {
    if (!this.dynamicWidth.lastCameraDistance) return true

    const distanceChange = Math.abs(
      cameraDistance - this.dynamicWidth.lastCameraDistance
    )
    return distanceChange > this.dynamicWidth.updateThreshold
  }

  /**
   * 渲染单条干线
   * 简化版本：减少嵌套判断，使用辅助函数
   * @param {Object} trunkLine - 干线数据
   * @param {number} [cameraDistance] - 相机距离，用于动态宽度计算
   */
  renderTrunkLine(trunkLine, cameraDistance) {
    const trunkLineId = trunkLine.route_id || trunkLine.id

    // 移除已存在的渲染
    if (this.renderedTrunkLines.has(trunkLineId)) {
      this.removeTrunkLine(trunkLineId)
    }

    const geoData = trunkLine.route || trunkLine.geoData
    if (!this.isValidGeoData(geoData)) {
      return false
    }

    try {
      const trunkLineGroup = this.createTrunkLineGroup(
        trunkLineId,
        trunkLine.route_name
      )
      const { lines, materials } = this.renderFeatures(
        geoData.features,
        trunkLine,
        trunkLineGroup,
        cameraDistance
      )

      this.trunkLinesGroup.add(trunkLineGroup)
      this.renderedTrunkLines.set(trunkLineId, {
        group: trunkLineGroup,
        lines,
        materials,
        trunkLine: { ...trunkLine },
      })

      return true
    } catch (error) {
      console.error(`渲染干线失败:`, error)
      return false
    }
  }

  /**
   * 验证地理数据有效性
   */
  isValidGeoData(geoData) {
    return geoData?.features?.length > 0
  }

  /**
   * 创建干线组
   */
  createTrunkLineGroup(trunkLineId, routeName) {
    const group = new THREE.Group()
    group.name = `TrunkLine_${trunkLineId}`
    group.userData = {
      trunkLineId,
      trunkLineName: routeName,
      type: 'trunk_line',
    }
    return group
  }

  /**
   * 渲染所有特征
   * @param {Array} features - GeoJSON特征数组
   * @param {Object} trunkLine - 干线数据
   * @param {THREE.Group} trunkLineGroup - 干线组
   * @param {number} [cameraDistance] - 相机距离
   */
  renderFeatures(features, trunkLine, trunkLineGroup, cameraDistance) {
    const lines = []
    const materials = []

    features.forEach((feature, index) => {
      const lineObjects = this.renderFeature(
        feature,
        trunkLine,
        index,
        cameraDistance
      )
      lineObjects.forEach(lineObj => {
        if (lineObj) {
          lines.push(lineObj.line)
          materials.push(lineObj.material)
          trunkLineGroup.add(lineObj.line)
        }
      })
    })

    return { lines, materials }
  }

  /**
   * 渲染单个特征
   * @param {Object} feature - GeoJSON特征
   * @param {Object} trunkLine - 干线数据
   * @param {number} index - 特征索引
   * @param {number} [cameraDistance] - 相机距离
   * @returns {Array|null} 线对象数组
   */
  renderFeature(feature, trunkLine, index, cameraDistance) {
    const { geometry } = feature
    const { type: geometryType, coordinates } = geometry
    const lineObjects = []

    if (geometryType === 'LineString') {
      const lineObj = this.createLineFromCoordinates(
        coordinates,
        trunkLine,
        feature,
        index,
        cameraDistance
      )
      if (lineObj) lineObjects.push(lineObj)
    } else if (geometryType === 'MultiLineString') {
      coordinates.forEach((lineCoords, lineIndex) => {
        const lineObj = this.createLineFromCoordinates(
          lineCoords,
          trunkLine,
          feature,
          `${index}_${lineIndex}`,
          cameraDistance
        )
        if (lineObj) lineObjects.push(lineObj)
      })
    }

    return lineObjects
  }

  /**
   * 从坐标创建面对象（替代线条）
   * 使用面条带（ribbon）的方式渲染干线
   * @param {Array} coordinates - 坐标数组
   * @param {Object} trunkLine - 干线数据
   * @param {Object} feature - GeoJSON特征
   * @param {string|number} identifier - 标识符
   * @param {number} [cameraDistance] - 相机距离
   */
  createLineFromCoordinates(
    coordinates,
    trunkLine,
    feature,
    identifier,
    cameraDistance
  ) {
    if (coordinates.length < 2) return null

    const meshGeometry = this.createRibbonGeometry(
      coordinates,
      trunkLine,
      cameraDistance
    )
    if (!meshGeometry) return null

    const material = this.getMaterial(trunkLine)
    const mesh = new THREE.Mesh(meshGeometry, material)

    mesh.renderOrder = trunkLine.style.zIndex || 1000
    mesh.userData = this.createLineUserData(trunkLine, feature, identifier)

    return { line: mesh, material }
  }

  /**
   * 创建面条带几何体
   * 将线条转换为有宽度的面条带
   */
  createRibbonGeometry(coordinates, trunkLine, cameraDistance) {
    const transformedCoords = transformGeoJsonCoordinates(
      coordinates,
      0,
      this.options.roadNetworkScale
    )

    // 计算面条带的宽度（支持动态宽度调整）
    const baseLineWidth =
      trunkLine.style.lineWidth || this.options.defaultLineWidth

    // 获取动态宽度倍数（如果提供了相机距离）
    let dynamicWidthMultiplier = 1.0
    if (cameraDistance !== undefined) {
      dynamicWidthMultiplier =
        this.calculateDynamicWidthMultiplier(cameraDistance)
    }

    // 应用动态宽度倍数
    const adjustedLineWidth = baseLineWidth * dynamicWidthMultiplier
    const ribbonWidth =
      adjustedLineWidth * this.dynamicWidth.baseLineWidthFactor * 0.001 // 转换为世界单位

    const vertices = []
    const indices = []
    const uvs = []

    // 为每个线段创建四边形面
    for (let i = 0; i < transformedCoords.length - 1; i++) {
      const current = transformedCoords[i]
      const next = transformedCoords[i + 1]

      // 计算线段方向向量
      const direction = new THREE.Vector2(
        next[0] - current[0],
        next[1] - current[1]
      ).normalize()

      // 计算垂直向量（用于宽度）
      const perpendicular = new THREE.Vector2(-direction.y, direction.x)
      const halfWidth = ribbonWidth / 2

      // 创建四个顶点（当前线段的四个角）
      const v1 = [
        current[0] - perpendicular.x * halfWidth,
        this.options.yLevel,
        current[1] - perpendicular.y * halfWidth,
      ]
      const v2 = [
        current[0] + perpendicular.x * halfWidth,
        this.options.yLevel,
        current[1] + perpendicular.y * halfWidth,
      ]
      const v3 = [
        next[0] - perpendicular.x * halfWidth,
        this.options.yLevel,
        next[1] - perpendicular.y * halfWidth,
      ]
      const v4 = [
        next[0] + perpendicular.x * halfWidth,
        this.options.yLevel,
        next[1] + perpendicular.y * halfWidth,
      ]

      // 添加顶点
      const baseIndex = vertices.length / 3
      vertices.push(...v1, ...v2, ...v3, ...v4)

      // 添加面索引（两个三角形组成四边形）
      indices.push(
        baseIndex,
        baseIndex + 1,
        baseIndex + 2,
        baseIndex + 1,
        baseIndex + 3,
        baseIndex + 2
      )

      // 添加UV坐标
      const u = i / (transformedCoords.length - 1)
      const nextU = (i + 1) / (transformedCoords.length - 1)
      uvs.push(u, 0, u, 1, nextU, 0, nextU, 1)
    }

    // 创建几何体
    const geometry = new THREE.BufferGeometry()
    geometry.setAttribute(
      'position',
      new THREE.Float32BufferAttribute(vertices, 3)
    )
    geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uvs, 2))
    geometry.setIndex(indices)
    geometry.computeVertexNormals()

    return geometry
  }

  /**
   * 创建线对象用户数据
   */
  createLineUserData(trunkLine, feature, identifier) {
    const trunkLineId = trunkLine.route_id || trunkLine.id

    return {
      type: 'trunk_line',
      trunkLineId,
      trunkLineName: trunkLine.route_name,
      featureId:
        feature.properties?.trunkFeatureId || `${trunkLineId}_${identifier}`,
      originalId: feature.properties?.originalId,
      identifier,
    }
  }

  /**
   * 获取或创建材质
   */
  getMaterial(trunkLine) {
    const materialKey = this.generateMaterialKey(trunkLine)

    if (this.materialCache.has(materialKey)) {
      return this.materialCache.get(materialKey).clone()
    }

    const material = this.createMeshMaterial(trunkLine)
    this.materialCache.set(materialKey, material)
    return material.clone()
  }

  /**
   * 创建面材质
   */
  createMeshMaterial(trunkLine) {
    const style = trunkLine.style

    const config = {
      color: new THREE.Color(0x4a90e2), // 使用统一的默认蓝色，将由交通指标动态覆盖
      opacity: style.opacity || this.options.defaultOpacity,
      transparent: (style.opacity || this.options.defaultOpacity) < 1,
      side: THREE.DoubleSide, // 双面渲染
      fog: true,
    }

    // 面材质不支持虚线，如果需要虚线效果可以考虑使用纹理
    const material = new THREE.MeshBasicMaterial(config)

    return material
  }

  /**
   * 生成材质缓存键
   */
  generateMaterialKey(trunkLine) {
    const { style } = trunkLine
    return `default_${style.opacity}`
  }

  /**
   * 移除干线渲染
   * @param {string} trunkLineId - 干线ID
   * @returns {boolean} 是否移除成功
   */
  removeTrunkLine(trunkLineId) {
    const renderInfo = this.renderedTrunkLines.get(trunkLineId)
    if (!renderInfo) return false

    // 移除组
    this.trunkLinesGroup.remove(renderInfo.group)

    // 清理几何体和材质
    renderInfo.lines.forEach(mesh => mesh.geometry?.dispose())
    renderInfo.materials.forEach(material => material?.dispose())

    // 从缓存中移除
    this.renderedTrunkLines.delete(trunkLineId)
    return true
  }

  /**
   * 更新干线渲染
   * @param {Object} trunkLine - 更新后的干线数据
   * @returns {boolean} 是否更新成功
   */
  updateTrunkLine(trunkLine) {
    // 简单的实现：先移除再重新渲染
    const trunkLineId = trunkLine.route_id || trunkLine.id
    this.removeTrunkLine(trunkLineId)
    return this.renderTrunkLine(trunkLine)
  }

  /**
   * 设置所有干线的整体可见性（用于与路网显示逻辑绑定）
   * @param {boolean} visible - 是否可见
   */
  setAllTrunkLinesVisibility(visible) {
    if (this.trunkLinesGroup) {
      this.trunkLinesGroup.visible = visible
    }
  }

  /**
   * 获取所有干线的整体可见性状态
   * @returns {boolean} 是否可见
   */
  getAllTrunkLinesVisibility() {
    return this.trunkLinesGroup ? this.trunkLinesGroup.visible : false
  }

  /**
   * 渲染多条干线
   * @param {Array} trunkLines - 干线数组
   * @returns {Object} 渲染结果统计
   */
  renderTrunkLines(trunkLines) {
    let successCount = 0
    let failCount = 0

    trunkLines.forEach(trunkLine => {
      if (this.renderTrunkLine(trunkLine)) {
        successCount++
      } else {
        failCount++
      }
    })

    return { successCount, failCount, total: trunkLines.length }
  }

  /**
   * 清除所有干线渲染
   */
  clearAllTrunkLines() {
    const trunkLineIds = Array.from(this.renderedTrunkLines.keys())
    trunkLineIds.forEach(id => this.removeTrunkLine(id))
  }

  /**
   * 更新渲染器配置
   * @param {Object} newOptions - 新的配置选项
   */
  updateOptions(newOptions) {
    this.options = { ...this.options, ...newOptions }

    // 如果分辨率改变，需要更新所有材质
    if (newOptions.resolution) {
      this.materialCache.clear()
      // 重新渲染所有干线以应用新配置
      const allTrunkLines = Array.from(this.renderedTrunkLines.values()).map(
        info => info.trunkLine
      )
      this.clearAllTrunkLines()
      this.renderTrunkLines(allTrunkLines)
    }
  }

  /**
   * 获取渲染统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    let totalLines = 0
    let totalMaterials = 0

    this.renderedTrunkLines.forEach(renderInfo => {
      totalLines += renderInfo.lines.length
      totalMaterials += renderInfo.materials.length
    })

    return {
      trunkLineCount: this.renderedTrunkLines.size,
      totalLines,
      totalMaterials,
      cachedMaterials: this.materialCache.size,
    }
  }

  /**
   * 根据相机距离更新所有干线的宽度
   * @param {number} cameraDistance - 相机距离
   */
  updateDynamicWidth(cameraDistance) {
    if (!this.dynamicWidth.enabled) return

    // 检查是否需要更新
    if (!this.shouldUpdateWidth(cameraDistance)) return

    // 更新缓存的距离
    this.dynamicWidth.lastCameraDistance = cameraDistance

    // 重新渲染所有干线
    const trunkLinesToUpdate = Array.from(this.renderedTrunkLines.values())

    trunkLinesToUpdate.forEach(({ trunkLine }) => {
      this.renderTrunkLine(trunkLine, cameraDistance)
    })
  }

  /**
   * 启用或禁用动态宽度
   * @param {boolean} enabled - 是否启用
   */
  setDynamicWidthEnabled(enabled) {
    this.dynamicWidth.enabled = enabled
  }

  /**
   * 设置动态宽度参数
   * @param {Object} params - 参数对象
   */
  setDynamicWidthParams(params) {
    Object.assign(this.dynamicWidth, params)
  }

  /**
   * 销毁渲染器
   */
  dispose() {
    this.clearAllTrunkLines()

    // 清理材质缓存
    this.materialCache.forEach(material => material.dispose())
    this.materialCache.clear()

    // 移除干线组
    if (this.trunkLinesGroup.parent) {
      this.trunkLinesGroup.parent.remove(this.trunkLinesGroup)
    }
  }
}
