/**
 * 坐标转换工具函数
 * 统一管理项目中的坐标转换逻辑
 */

// 坐标转换常量
export const COORDINATE_CONFIG = {
  // 中心点坐标（济南市中心）
  CENTER_LON: 117, // 经度
  CENTER_LAT: 36, // 纬度

  // 比例因子 - 调整为更合适的比例（减小让路网更紧凑）
  SCALE_FACTOR: 30,

  // 高度比例因子（用于将真实高度转换为3D场景高度）
  HEIGHT_SCALE_FACTOR: 2220,

  // Y坐标层级常量（确保不同元素的正确渲染顺序）
  Y_LEVELS: {
    GROUND: 0, // 地面层
    CITY_OUTLINE: 0, // 轮廓层
    AREAS: 0.002, // 区域层
    AREA_LABELS: 0.005, // 区域标签层
    ROADS: 0.003, // 路网层
    TRUNK_LINES: 0.004, // 干线层（在路网之上）
    DEVICES: 0.004, // 设备图标层
  },
}

/**
 * 将经纬度坐标转换为3D场景坐标
 * @param {number} lon - 经度
 * @param {number} lat - 纬度
 * @param {number} height - 高度（可选，默认为0）
 * @returns {Array} [x, y, z] 3D坐标
 */
export function transformCoordinate(lon, lat, height = 0) {
  const x =
    (lon - COORDINATE_CONFIG.CENTER_LON) * COORDINATE_CONFIG.SCALE_FACTOR
  const z =
    -(lat - COORDINATE_CONFIG.CENTER_LAT) * COORDINATE_CONFIG.SCALE_FACTOR // 反转Z轴方向
  const y = height / COORDINATE_CONFIG.HEIGHT_SCALE_FACTOR

  return [x, y, z]
}

/**
 * 将3D场景坐标转换回经纬度坐标
 * @param {number} x - 场景X坐标
 * @param {number} z - 场景Z坐标
 * @returns {Object} {lng, lat} 经纬度坐标
 */
export function transformSceneToGeo(x, z) {
  // 注意Z轴是反向的，所以需要对z取反
  const lon = x / COORDINATE_CONFIG.SCALE_FACTOR + COORDINATE_CONFIG.CENTER_LON
  const lat = -z / COORDINATE_CONFIG.SCALE_FACTOR + COORDINATE_CONFIG.CENTER_LAT

  return { lng: lon, lat: lat }
}

/**
 * 将真实世界的距离转换为3D场景中的距离
 * @param {number} realDistance - 真实距离（米）
 * @returns {number} 3D场景中的距离
 */
export function transformDistance(realDistance) {
  return realDistance / COORDINATE_CONFIG.HEIGHT_SCALE_FACTOR
}

/**
 * 将真实世界的高度转换为3D场景中的高度
 * @param {number} realHeight - 真实高度（米）
 * @returns {number} 3D场景中的高度
 */
export function transformHeight(realHeight) {
  return realHeight / COORDINATE_CONFIG.HEIGHT_SCALE_FACTOR
}

/**
 * 批量转换坐标数组
 * @param {Array} coordinates - 坐标数组 [[lon, lat], [lon, lat], ...]
 * @param {number} height - 统一高度（可选，默认为0）
 * @returns {Array} 转换后的3D坐标数组
 */
export function transformCoordinateArray(coordinates, height = 0) {
  return coordinates.map(coord =>
    transformCoordinate(coord[0], coord[1], height)
  )
}

/**
 * 为GeoJSON渲染器转换坐标（用于Shape和Line几何体）
 * @param {Array} coordinates - GeoJSON坐标数组
 * @param {number} _height - 高度（保留参数以兼容现有调用，但未使用）
 * @param {number} [scale] - 可选的缩放因子，如果提供则覆盖全局配置
 * @returns {Array} 转换后的坐标数组
 */
export function transformGeoJsonCoordinates(
  coordinates,
  _height = 0,
  scale = null
) {
  const scaleToUse = scale !== null ? scale : COORDINATE_CONFIG.SCALE_FACTOR
  return coordinates.map(coord => [
    (coord[0] - COORDINATE_CONFIG.CENTER_LON) * scaleToUse,
    -(coord[1] - COORDINATE_CONFIG.CENTER_LAT) * scaleToUse,
  ]) // 反转Z轴方向
}

/**
 * 计算两个经纬度点之间的距离（使用简化的平面距离计算）
 * @param {number} lon1 - 第一个点的经度
 * @param {number} lat1 - 第一个点的纬度
 * @param {number} lon2 - 第二个点的经度
 * @param {number} lat2 - 第二个点的纬度
 * @returns {number} 3D场景中的距离
 */
export function calculateDistance(lon1, lat1, lon2, lat2) {
  const x1 =
    (lon1 - COORDINATE_CONFIG.CENTER_LON) * COORDINATE_CONFIG.SCALE_FACTOR
  const z1 =
    -(lat1 - COORDINATE_CONFIG.CENTER_LAT) * COORDINATE_CONFIG.SCALE_FACTOR // 反转Z轴方向
  const x2 =
    (lon2 - COORDINATE_CONFIG.CENTER_LON) * COORDINATE_CONFIG.SCALE_FACTOR
  const z2 =
    -(lat2 - COORDINATE_CONFIG.CENTER_LAT) * COORDINATE_CONFIG.SCALE_FACTOR // 反转Z轴方向

  return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(z2 - z1, 2))
}
