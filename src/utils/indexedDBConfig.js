/**
 * IndexedDB 统一配置模块
 * 用于统一应用中的数据库操作
 */

// 数据库基础配置
export const DB_CONFIG = {
  DB_NAME: 'geoDataDB',
  DB_VERSION: 2, // 升级为2，修复版本冲突
  STORES: {
    // MainToolbar.vue中使用的存储
    BOUNDARY: 'boundaries',
    ROAD_NETWORK: 'roadNetworks',

    // CityModel.vue中使用的存储
    CITY_DATA: 'cityData',
    ROAD_DATA: 'roadNetworks',
    USER_DRAWN_ROADS: 'userDrawnRoads',
    USER_POINTS: 'userPoints',
    USER_POLYGONS: 'userPolygons',
    DEVICE_INFO: 'deviceInfo',
    SETTINGS: 'settings',

    // 干线管理相关存储
    TRUNK_LINES: 'trunkLines',

    // 区域管理相关存储
    REGIONS: 'regions',

    // 道路管理相关存储
    ROADS: 'roads',
  },
}

/**
 * 初始化IndexedDB数据库
 */
export function initIndexedDB() {
  return new Promise((resolve, reject) => {
    if (!window.indexedDB) {
      reject(new Error('浏览器不支持 IndexedDB'))
      return
    }

    const request = indexedDB.open(DB_CONFIG.DB_NAME, DB_CONFIG.DB_VERSION)

    request.onerror = _event => {
      reject(_event)
    }

    request.onsuccess = _event => {
      resolve(_event.target.result)
    }

    request.onupgradeneeded = _event => {
      const db = _event.target.result
      const stores = DB_CONFIG.STORES

      // 创建所有需要的存储对象
      Object.values(stores).forEach(storeName => {
        if (!db.objectStoreNames.contains(storeName)) {
          db.createObjectStore(storeName, { keyPath: 'id' })
        }
      })
    }
  })
}

/**
 * 保存数据到IndexedDB
 */
export function saveToIndexedDB(db, storeName, id, data) {
  return new Promise((resolve, reject) => {
    if (!db) {
      reject(new Error('数据库未初始化'))
      return
    }

    if (!data) {
      reject(new Error('数据为空'))
      return
    }

    try {
      // 检查存储是否存在
      if (!db.objectStoreNames.contains(storeName)) {
        const error = new Error(
          `存储 '${storeName}' 不存在，可用存储: ${Array.from(
            db.objectStoreNames
          ).join(', ')}`
        )
        reject(error)
        return
      }

      const transaction = db.transaction([storeName], 'readwrite')
      const store = transaction.objectStore(storeName)

      // 确保数据可以被IndexedDB克隆
      let clonableData
      try {
        // 使用JSON序列化/反序列化来确保数据可克隆
        clonableData = JSON.parse(JSON.stringify(data))
      } catch (error) {
        reject(new Error(`数据无法序列化: ${error.message}`))
        return
      }

      const request = store.put({
        id: id,
        data: clonableData,
        timestamp: Date.now(),
      })

      request.onsuccess = () => {
        resolve()
      }

      request.onerror = event => {
        const error = new Error(
          `保存失败: ${event.target.error?.message || '未知错误'}`
        )
        reject(error)
      }

      transaction.onerror = event => {
        const error = new Error(
          `事务失败: ${event.target.error?.message || '未知错误'}`
        )
        reject(error)
      }

      transaction.onabort = event => {
        const error = new Error(
          `事务被中止: ${event.target.error?.message || '未知原因'}`
        )
        reject(error)
      }
    } catch (error) {
      reject(error)
    }
  })
}

/**
 * 从IndexedDB获取数据
 */
export function getFromIndexedDB(db, storeName, id) {
  return new Promise((resolve, _reject) => {
    if (!db) {
      resolve(null)
      return
    }

    try {
      const transaction = db.transaction([storeName], 'readonly')
      const store = transaction.objectStore(storeName)
      const request = store.get(id)

      request.onsuccess = _event => {
        const result = _event.target.result
        resolve(result ? result.data : null)
      }

      request.onerror = _event => {
        resolve(null)
      }
    } catch {
      resolve(null)
    }
  })
}

/**
 * 获取存储中的所有数据
 */
export function getAllFromIndexedDB(db, storeName) {
  return new Promise((resolve, _reject) => {
    if (!db) {
      resolve([])
      return
    }

    try {
      const transaction = db.transaction([storeName], 'readonly')
      const store = transaction.objectStore(storeName)
      const request = store.getAll()

      request.onsuccess = _event => {
        const results = _event.target.result || []
        resolve(results.map(item => item.data))
      }

      request.onerror = _event => {
        resolve([])
      }
    } catch {
      resolve([])
    }
  })
}

/**
 * 从IndexedDB删除数据
 */
export function deleteFromIndexedDB(db, storeName, id) {
  return new Promise((resolve, _reject) => {
    if (!db) {
      resolve()
      return
    }

    try {
      const transaction = db.transaction([storeName], 'readwrite')
      const store = transaction.objectStore(storeName)
      const request = store.delete(id)

      request.onsuccess = () => {
        resolve()
      }

      request.onerror = _event => {
        resolve()
      }
    } catch {
      resolve()
    }
  })
}
