import * as THREE from 'three'
import { transformGeoJsonCoordinates } from '@/utils/coordinateTransform'

/**
 * 道路标签管理器
 * 负责道路标签的创建、渲染、缓存和样式管理
 */
class RoadLabelManager {
  constructor() {
    this.labelCache = new Map() // 标签数据缓存
    this.styleConfig = this.getDefaultStyleConfig()
    this.visibilityConfig = this.getDefaultVisibilityConfig()
  }

  /**
   * 获取默认样式配置
   */
  getDefaultStyleConfig() {
    return {
      color: 0x1a2b3d, // 使用更暗的深蓝色，降低亮度
    }
  }

  /**
   * 获取默认可见性配置
   */
  getDefaultVisibilityConfig() {
    return {
      maxDistance: 10.0, // 路名标签最大显示距离与道路同步
      minFontSize: 0.004, // 进一步减小最小字体大小
      baseFontSize: 0.008, // 进一步减小基础字体大小
      // 移除 maxLabelsPerRoad 限制，让标签数量由道路长度自然决定
    }
  }

  /**
   * 计算两点间距离
   */
  calculateDistance(point1, point2) {
    const dx = point2[0] - point1[0]
    const dy = point2[1] - point1[1]
    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * 获取标签颜色（统一颜色）
   */
  getLabelColor(_roadType) {
    return this.styleConfig.color
  }

  /**
   * 创建3D文字标签对象（贴在地面上）
   */
  createLabel(text, position, options = {}) {
    // 创建高分辨率画布来绘制文字
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')

    // 设置高分辨率画布大小和文字样式
    const fontSize = 48 // 固定字体大小，提高清晰度
    canvas.width = 1024 // 提高分辨率
    canvas.height = 256

    context.fillStyle = 'rgba(0, 0, 0, 0)' // 透明背景
    context.fillRect(0, 0, canvas.width, canvas.height)

    // 启用抗锯齿和高质量渲染
    context.imageSmoothingEnabled = true
    context.imageSmoothingQuality = 'high'
    context.textRenderingOptimization = 'optimizeQuality'

    context.font = `${fontSize}px Microsoft YaHei, Arial, sans-serif`
    // 将十六进制颜色转换为CSS颜色字符串
    const colorHex = options.color || this.styleConfig.color
    const colorString = `#${colorHex.toString(16).padStart(6, '0')}`
    context.fillStyle = colorString
    context.textAlign = 'center'
    context.textBaseline = 'middle'

    // 绘制文字
    context.fillText(text, canvas.width / 2, canvas.height / 2)

    // 创建纹理
    const texture = new THREE.CanvasTexture(canvas)
    texture.needsUpdate = true

    // 创建材质
    const material = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      alphaTest: 0.1,
      side: THREE.DoubleSide,
    })

    // 创建几何体（平面）- 显著缩小文字大小
    const geometry = new THREE.PlaneGeometry(0.08, 0.02) // 显著缩小文字大小

    // 创建网格
    const mesh = new THREE.Mesh(geometry, material)

    // 设置位置
    if (Array.isArray(position)) {
      mesh.position.set(position[0], position[1], position[2] || position[1])
    } else if (position instanceof THREE.Vector3) {
      mesh.position.copy(position)
    }

    // 让平面水平放置（贴在地面上）
    mesh.rotation.x = -Math.PI / 2

    // 设置用户数据
    mesh.userData = {
      roadName: text,
      roadType: options.roadType || 'default',
      source: options.source || 'system',
      isRoadLabel: true,
      ...options.userData,
    }

    return mesh
  }

  /**
   * 处理道路特征，按名称分组并计算最佳标签位置
   */
  processRoadFeatures(roadFeatures, roadNetworkScale) {
    const roadsByName = new Map()
    let _totalFeatures = 0
    let _featuresWithNames = 0

    // 按道路名称分组
    roadFeatures.forEach(feature => {
      _totalFeatures++
      const roadName = feature.properties?.name
      if (!roadName) {
        // 调试：记录没有名称的道路
        if (Math.random() < 0.01) {
          // 1%的概率输出日志，避免日志过多
          console.log('道路没有名称:', feature.properties)
        }
        return
      }
      _featuresWithNames++

      if (!roadsByName.has(roadName)) {
        roadsByName.set(roadName, {
          segments: [],
          roadType: feature.properties?.highway || 'unclassified',
          constructionType: feature.properties?.construction,
          allPoints: [],
          totalLength: 0,
        })
      }

      roadsByName.get(roadName).segments.push({
        feature,
        geometryType: feature.geometry.type,
        coordinates: feature.geometry.coordinates,
      })
    })

    // 计算每组道路的最佳标签位置
    const optimizedLabels = []

    roadsByName.forEach((roadData, roadName) => {
      // 转换坐标并计算总长度
      roadData.segments.forEach(segment => {
        const transformedCoords = transformGeoJsonCoordinates(
          segment.coordinates,
          roadNetworkScale
        )

        roadData.allPoints.push(...transformedCoords)

        // 计算段长度
        for (let i = 1; i < transformedCoords.length; i++) {
          roadData.totalLength += this.calculateDistance(
            transformedCoords[i - 1],
            transformedCoords[i]
          )
        }
      })

      // 根据道路长度决定标签数量 - 移除人为限制
      let labelCount = 1
      if (roadData.totalLength > 3) {
        // 让标签数量完全由道路长度决定，每3公里一个标签
        labelCount = Math.ceil(roadData.totalLength / 3)
      }

      // 生成标签位置
      if (roadData.allPoints.length > 0) {
        for (let i = 0; i < labelCount; i++) {
          const position = labelCount === 1 ? 0.5 : i / (labelCount - 1)
          const labelIndex = Math.floor(
            position * (roadData.allPoints.length - 1)
          )

          if (labelIndex >= 0 && labelIndex < roadData.allPoints.length) {
            const labelPos = roadData.allPoints[labelIndex]

            // 不计算方向，保持文字水平
            optimizedLabels.push({
              name: roadName,
              position: labelPos,
              direction: 0, // 固定为0，保持水平
              roadType: roadData.roadType,
              constructionType: roadData.constructionType,
            })
          }
        }
      }
    })

    // 调试输出
    if (Math.random() < 0.1) {
      // 10%的概率输出统计信息
      console.log(
        `道路标签统计: 总道路${_totalFeatures}条, 有名称${_featuresWithNames}条, 生成标签${optimizedLabels.length}个`
      )
    }

    return optimizedLabels
  }

  /**
   * 根据摄像机距离计算字体大小
   */
  calculateFontSize(cameraDistance) {
    const config = this.visibilityConfig
    return Math.max(
      config.minFontSize,
      config.baseFontSize - cameraDistance * 0.005
    )
  }

  /**
   * 渲染道路标签（优化版本，避免频繁重新创建）
   */
  renderLabels(
    geojson,
    roadNetworkScale,
    targetGroup,
    roadYLevel,
    camera,
    options = {}
  ) {
    if (!geojson?.features || !targetGroup || !camera) {
      this.hideAllLabels(targetGroup)
      return
    }

    const cameraDistance = camera.position.y

    // 距离检查 - 超出范围时隐藏标签
    if (cameraDistance >= this.visibilityConfig.maxDistance) {
      this.hideAllLabels(targetGroup)
      return
    }

    // 获取标签数据
    const labelsData = this.getLabelsData(
      geojson,
      roadNetworkScale,
      options.maxLabels
    )
    if (!labelsData.length) {
      return
    }

    // 检查是否需要重建标签对象
    const needsRebuild = this.shouldRebuildLabels(labelsData, targetGroup)

    if (needsRebuild) {
      this.rebuildLabels(labelsData, targetGroup, roadYLevel, cameraDistance)
    }

    // 更新标签可见性
    this.updateLabelsVisibility(labelsData, targetGroup)
  }

  /**
   * 隐藏所有标签
   */
  hideAllLabels(targetGroup) {
    targetGroup.children.forEach(child => {
      child.visible = false
    })
  }

  /**
   * 获取标签数据
   */
  getLabelsData(geojson, roadNetworkScale, maxLabels) {
    const cacheKey = `${geojson.features.length}_${roadNetworkScale}`

    if (!this.labelCache.has(cacheKey)) {
      const roadFeatures = geojson.features.filter(
        f =>
          f.geometry &&
          (f.geometry.type === 'LineString' ||
            f.geometry.type === 'MultiLineString')
      )
      this.labelCache.set(
        cacheKey,
        this.processRoadFeatures(roadFeatures, roadNetworkScale)
      )
    }

    let labelsData = this.labelCache.get(cacheKey)

    // 移除人为数量限制，返回所有智能过滤后的标签
    return labelsData
  }

  /**
   * 判断是否需要重建标签
   */
  shouldRebuildLabels(labelsData, targetGroup) {
    return targetGroup.children.length !== labelsData.length
  }

  /**
   * 重建标签对象
   */
  rebuildLabels(labelsData, targetGroup, roadYLevel, cameraDistance) {
    // 清除现有标签
    this.clearGroup(targetGroup)

    const fontSize = this.calculateFontSize(cameraDistance)

    labelsData.forEach(labelInfo => {
      const label = this.createLabel(
        labelInfo.name,
        [labelInfo.position[0], roadYLevel + 0.005, labelInfo.position[1]], // 使用传入的roadYLevel + 路网标签偏移
        {
          fontSize,
          color: this.getLabelColor(labelInfo.roadType),
          roadType: labelInfo.roadType,
          userData: { roadName: labelInfo.name, roadType: labelInfo.roadType },
        }
      )

      targetGroup.add(label)
    })
  }

  /**
   * 更新标签可见性
   */
  updateLabelsVisibility(labelsData, targetGroup) {
    // 直接遍历目标组中的标签对象
    targetGroup.children.forEach((label, index) => {
      // 由于 isLabelVisible 总是返回 true，直接设置可见性
      label.visible = index < labelsData.length
    })
  }

  /**
   * 清除组中的所有标签
   */
  clearGroup(group) {
    while (group.children.length > 0) {
      const child = group.children[0]
      group.remove(child)

      // 清理3D网格对象
      if (child.material) {
        if (child.material.map) {
          child.material.map.dispose()
        }
        child.material.dispose()
      }
      if (child.geometry) {
        child.geometry.dispose()
      }
    }
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.labelCache.clear()
  }
}

export default RoadLabelManager
