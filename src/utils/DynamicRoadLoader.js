import * as THREE from 'three'

/**
 * 动态道路加载器
 * 根据摄像机距离和视口范围动态加载适当的道路数据
 */
class DynamicRoadLoader {
  constructor(
    geoJsonRenderer,
    roadNetworkGroup,
    roadLabelsGroup,
    options = {}
  ) {
    this.geoJsonRenderer = geoJsonRenderer
    this.roadNetworkGroup = roadNetworkGroup
    this.roadLabelsGroup = roadLabelsGroup

    // 默认选项
    this.options = {
      roadYLevel: options.roadYLevel || 0,
      roadNetworkScale: options.roadNetworkScale || 1,
      getMaterialForRoadType:
        options.getMaterialForRoadType || this._defaultGetMaterial.bind(this),
    }

    // 新增：标记道路数据是否已预处理
    this.allRoadDataProcessed = false

    // 缓存已加载的道路数据
    this.roadDataCache = {
      primary: null,
      secondary: null,
      tertiary: null,
      primarySecondary: null,
      all: null, // 新增：缓存所有道路合并数据
    }

    // 新增：初始化groupedRoadsCache
    this.groupedRoadsCache = new Map()

    // 新增：存储所有道路特征
    this.allRoadFeatures = []

    // 距离阈值，精确控制各等级道路显示
    this.distanceThresholds = {
      hide: 10, // 一级道路在10以下显示
      primary: 5, // 二级道路在5以下显示
      secondary: 2, // 三级道路在2以下显示
    }

    // 记录当前加载的道路级别
    this.currentLoadedLevel = null

    // 加载路径
    this.basePath = '/data/roads/'

    // 材质回调函数
    this.getMaterialForRoadType =
      options.getMaterialForRoadType || this._defaultGetMaterial

    // 记录最后一次日志输出时间，用于限制日志频率（按消息类型分别记录）
    this._lastLogTimes = {}

    // 新增：用于存储已渲染道路的Group对象
    this.renderedRoadGroups = {
      primary: new THREE.Group(),
      primarySecondary: new THREE.Group(),
      all: new THREE.Group(),
    }

    // 将所有道路Group添加到主道路网络组
    this.roadNetworkGroup.add(this.renderedRoadGroups.primary)
    this.roadNetworkGroup.add(this.renderedRoadGroups.primarySecondary)
    this.roadNetworkGroup.add(this.renderedRoadGroups.all)

    // 默认所有道路组都不可见
    this.renderedRoadGroups.primary.visible = false
    this.renderedRoadGroups.primarySecondary.visible = false
    this.renderedRoadGroups.all.visible = false

    // 记录哪些级别已经渲染过
    this.renderedLevels = {
      primary: false,
      primarySecondary: false,
      all: false,
    }

    // 新增：用于存储已渲染道路标签的Group对象
    this.renderedRoadLabelGroups = {
      primary: new THREE.Group(),
      primarySecondary: new THREE.Group(),
      all: new THREE.Group(),
    }

    // 将所有道路标签Group添加到主标签组
    this.roadLabelsGroup.add(this.renderedRoadLabelGroups.primary)
    this.roadLabelsGroup.add(this.renderedRoadLabelGroups.primarySecondary)
    this.roadLabelsGroup.add(this.renderedRoadLabelGroups.all)

    // 默认所有道路标签组都不可见
    this.renderedRoadLabelGroups.primary.visible = false
    this.renderedRoadLabelGroups.primarySecondary.visible = false
    this.renderedRoadLabelGroups.all.visible = false
  }

  /**
   * 默认材质获取函数（如果未提供）
   */
  _defaultGetMaterial() {
    return new THREE.LineBasicMaterial({ color: 0xffffff })
  }

  /**
   * 加载并预处理所有道路数据（确保同名道路的连续性）
   * @private
   */
  async _loadAndProcessAllRoadData() {
    if (this.allRoadDataProcessed) {
      return true // 已经处理过了
    }

    try {
      // 开始加载和预处理所有道路数据

      // 防止默认加载行为
      // 如果用户没有上传路网数据，就不尝试加载任何数据
      if (this.allRoadFeatures && this.allRoadFeatures.length > 0) {
        // 使用已上传的路网数据进行处理

        // 直接使用用户上传的数据处理
        // 按道路类型分类
        const primaryFeatures = []
        const secondaryFeatures = []
        const tertiaryFeatures = []

        // 按名称分组，确保同名道路的连续性
        const roadsByName = {}

        // 分析所有特征
        this.allRoadFeatures.forEach(feature => {
          const name = feature.properties?.name
          const roadType = feature.properties?.highway

          // 按名称分组
          if (name) {
            if (!roadsByName[name]) {
              roadsByName[name] = []
            }
            roadsByName[name].push(feature)
          }

          // 按类型分类（初步）
          if (
            roadType === 'trunk' ||
            roadType === 'primary' ||
            roadType === 'motorway'
          ) {
            primaryFeatures.push(feature)
          } else if (roadType === 'secondary') {
            secondaryFeatures.push(feature)
          } else {
            tertiaryFeatures.push(feature)
          }
        })

        // 处理所有同名道路，确保它们都在相同级别
        Object.keys(roadsByName).forEach(name => {
          const segments = roadsByName[name]
          // 确定这条路的级别（取最高级别）
          const hasPrimary = segments.some(
            f =>
              f.properties?.highway === 'trunk' ||
              f.properties?.highway === 'primary' ||
              f.properties?.highway === 'motorway'
          )

          const hasSecondary = segments.some(
            f => f.properties?.highway === 'secondary'
          )

          // 根据识别的级别重新分配所有分段
          if (hasPrimary) {
            // 从次级和三级中移除，并添加到一级
            segments.forEach(segment => {
              // 如果在次级或三级中，移除它
              const secIndex = secondaryFeatures.indexOf(segment)
              if (secIndex > -1) secondaryFeatures.splice(secIndex, 1)

              const terIndex = tertiaryFeatures.indexOf(segment)
              if (terIndex > -1) tertiaryFeatures.splice(terIndex, 1)

              // 确保在一级中
              if (!primaryFeatures.includes(segment)) {
                primaryFeatures.push(segment)
              }
            })
          } else if (hasSecondary) {
            // 从三级中移除，并添加到二级
            segments.forEach(segment => {
              const terIndex = tertiaryFeatures.indexOf(segment)
              if (terIndex > -1) tertiaryFeatures.splice(terIndex, 1)

              // 确保在二级中
              if (!secondaryFeatures.includes(segment)) {
                secondaryFeatures.push(segment)
              }
            })
          }
        })

        // 创建缓存
        this.roadDataCache.primary = {
          type: 'FeatureCollection',
          features: primaryFeatures,
        }

        this.roadDataCache.secondary = {
          type: 'FeatureCollection',
          features: secondaryFeatures,
        }

        this.roadDataCache.tertiary = {
          type: 'FeatureCollection',
          features: tertiaryFeatures,
        }

        this.roadDataCache.primarySecondary = {
          type: 'FeatureCollection',
          features: [...primaryFeatures, ...secondaryFeatures],
        }

        this.roadDataCache.all = {
          type: 'FeatureCollection',
          features: [
            ...primaryFeatures,
            ...secondaryFeatures,
            ...tertiaryFeatures,
          ],
        }

        this.allRoadDataProcessed = true
        // 已处理用户上传的道路数据
        return true
      }

      // 如果用户没有上传数据，不尝试加载默认数据
      // 没有可用的路网数据
      return false

      // 原硬编码加载代码已被移除，避免尝试加载不存在的文件
      // const [primaryData, secondaryData, tertiaryData] = await Promise.all([
      //     fetch(`${this.basePath}primary_roads.geojson`).then((res) => res.json()),
      //     fetch(`${this.basePath}secondary_roads.geojson`).then((res) => res.json()),
      //     fetch(`${this.basePath}tertiary_roads.geojson`).then((res) => res.json()),
      // ]);
      // ...
    } catch (error) {
      // 清空所有缓存和状态，确保系统处于干净状态
      this.roadDataCache = {
        primary: null,
        secondary: null,
        tertiary: null,
        primarySecondary: null,
        all: null,
      }
      this.allRoadDataProcessed = false
      return false
    }
  }

  /**
   * 预加载所有道路数据到缓存
   */
  async preloadAllRoadData() {
    try {
      console.log('路网数据加载策略: 仅在用户上传数据后加载')

      // 检查当前是否有用户上传的数据
      if (this.allRoadFeatures && this.allRoadFeatures.length > 0) {
        // 使用新的加载和处理方法
        const success = await this._loadAndProcessAllRoadData()

        if (success) {
          console.log(
            `路网数据预处理完成，一级道路: ${
              this.roadDataCache.primary?.features?.length || 0
            }，二级道路: ${
              this.roadDataCache.secondary?.features?.length || 0
            }，三级道路: ${this.roadDataCache.tertiary?.features?.length || 0}`
          )
        }

        return success
      } else {
        // 没有用户数据，不自动加载
        console.log('没有用户上传的路网数据，不执行预加载')
        return false
      }
    } catch (error) {
      console.error('预加载路网数据时发生错误:', error)
      return false
    }
  }

  /**
   * 加载指定类型的道路数据
   * @param {string} type - 道路类型: 'primary', 'secondary', 'tertiary', 'primarySecondary', 'all'
   */
  async loadRoadData(type) {
    // 如果没有用户上传的数据，直接返回null
    if (!this.allRoadFeatures || this.allRoadFeatures.length === 0) {
      // 没有可用的路网数据
      return null
    }

    // 如果已缓存，直接返回
    if (this.roadDataCache[type]) {
      return this.roadDataCache[type]
    }

    try {
      // 确保所有数据已加载和处理
      const processed = await this._loadAndProcessAllRoadData()
      if (!processed) {
        // 加载道路数据失败: 预处理未成功完成
        return null
      }

      // 因为_loadAndProcessAllRoadData已经创建了所有类型的缓存，这里应该可以直接返回
      return this.roadDataCache[type] || null
    } catch (error) {
      return null
    }
  }

  /**
   * 根据摄像机距离更新道路显示
   * @param {number} cameraDistance - 摄像机到目标的距离
   * @param {THREE.Camera} camera - Three.js 摄像机对象，用于视锥体裁剪
   */
  async updateRoadsByDistance(cameraDistance, camera) {
    // 根据相机距离更新道路显示

    if (!this.allRoadFeatures || this.allRoadFeatures.length === 0) {
      // console.log('没有可用的路网数据，请上传路网文件');
      return
    }

    let targetLevel

    // 根据距离确定要加载的道路级别
    if (cameraDistance > this.distanceThresholds.hide) {
      targetLevel = 'hide' // 超过最远距离，不显示任何道路
    } else if (cameraDistance > this.distanceThresholds.primary) {
      targetLevel = 'primary' // 在 hide 和 primary 阈值之间，显示一级道路
    } else if (cameraDistance > this.distanceThresholds.secondary) {
      targetLevel = 'primarySecondary' // 在 primary 和 secondary 阈值之间，显示一、二级道路
    } else {
      targetLevel = 'all' // 小于 secondary 阈值，显示所有道路
    }

    // 如果目标是隐藏所有道路
    if (targetLevel === 'hide') {
      if (this.currentLoadedLevel !== 'hide') {
        Object.values(this.renderedRoadGroups).forEach(
          group => (group.visible = false)
        )
        Object.values(this.renderedRoadLabelGroups).forEach(
          group => (group.visible = false)
        )
        this.currentLoadedLevel = 'hide'
      }
      return // 结束执行
    }

    // 如果级别没变，不重新加载（除非强制更新标志为true）
    if (
      this.currentLoadedLevel === targetLevel &&
      this.renderedLevels[targetLevel]
    ) {
      return
    }

    // 切换到新的道路级别

    // 隐藏所有道路组和标签组
    Object.values(this.renderedRoadGroups).forEach(group => {
      group.visible = false
    })
    Object.values(this.renderedRoadLabelGroups).forEach(group => {
      group.visible = false
    })

    // 如果当前级别尚未渲染，则加载并渲染
    if (!this.renderedLevels[targetLevel]) {
      try {
        const roadData = await this.loadRoadData(targetLevel)

        if (!roadData) {
          return // 如果无法加载数据，直接返回
        }

        // 应用视锥体裁剪（如果camera可用且为all级别）
        let processedRoadData = roadData
        /*
				if (targetLevel === 'all' && camera && cameraDistance < 800) {
					try {
						const beforeCount = roadData.features.length;
						processedRoadData = this.filterRoadsByViewport(roadData, camera);
						const afterCount = processedRoadData.features.length;

						if (beforeCount !== afterCount) {
							console.log(`视锥体裁剪: 从${beforeCount}条道路过滤到${afterCount}条道路`);
						}
					} catch (error) {
						console.error('视锥体裁剪失败，使用原始数据:', error);
						processedRoadData = roadData; // 出错时使用原始数据
					}
				}
				*/

        // 使用自定义方法渲染到特定组
        try {
          // 如果geoJsonRenderer不可用，记录错误并返回
          if (!this.geoJsonRenderer) {
            console.error('缺少geoJsonRenderer，无法渲染道路')
            return
          }

          // 检查是否提供了有效的渲染组
          if (
            !this.renderedRoadGroups[targetLevel] ||
            !this.renderedRoadLabelGroups[targetLevel]
          ) {
            console.error(`无效的渲染目标组: ${targetLevel}`)
            return
          }

          // 确保有需要渲染的数据
          if (
            !processedRoadData.features ||
            processedRoadData.features.length === 0
          ) {
            // 即使没有数据也标记为已渲染，避免反复尝试
            this.renderedLevels[targetLevel] = true
            return
          }

          // 适应无camera参数的情况
          const hasCameraAndDistance =
            camera && typeof cameraDistance === 'number'
          const effectiveDistance = hasCameraAndDistance ? cameraDistance : 0

          // 渲染道路(暂不渲染标签)
          this.geoJsonRenderer.processRoadNetworkToGroup(
            processedRoadData,
            this.options.roadNetworkScale,
            this.renderedRoadGroups[targetLevel],
            this.renderedRoadLabelGroups[targetLevel],
            this.getMaterialForRoadType,
            this.options.roadYLevel,
            effectiveDistance
          )

          this.renderedLevels[targetLevel] = true

          // 异步延迟加载标签
          // const labelThreshold = this.options.roadLabelsVisibilityThreshold;
          // console.log(`[DynamicRoadLoader] Checking to schedule labels. effectiveDistance: ${effectiveDistance.toFixed(2)}, threshold: ${labelThreshold}`);

          // 强制总是调用标签渲染，不依赖于距离检查
          if (hasCameraAndDistance) {
            this._scheduleLabelsRendering(
              targetLevel,
              processedRoadData,
              camera
            )
          }
        } catch (renderError) {
          // 渲染失败，静默处理
        }
      } catch (error) {
        console.error('加载道路级别失败:', error)
      }
    }

    // 显示当前级别的道路
    if (this.renderedRoadGroups[targetLevel]) {
      this.renderedRoadGroups[targetLevel].visible = true
      this.currentLoadedLevel = targetLevel

      // 延迟显示标签（已经渲染的情况）
      if (camera) {
        // 使用setTimeout确保道路先渲染完成
        setTimeout(() => {
          // 当targetLevel为primary时不显示标签
          if (targetLevel === 'primary') {
            this.renderedRoadLabelGroups[targetLevel].visible = false
          }
          // 其他情况下显示标签
          else if (targetLevel === 'all') {
            this._updateVisibleLabels(targetLevel, camera)
          } else {
            this.renderedRoadLabelGroups[targetLevel].visible = true
          }
        }, 100)
      }
    } else {
      // 未找到对应级别的渲染组
    }
  }

  /**
   * 渲染道路标签（精简版本）
   * @private
   */
  _scheduleLabelsRendering(targetLevel, roadData, camera) {
    // 允许所有级别显示标签，但primary级别限制数量
    // if (targetLevel === 'primary') {
    //   return
    // }

    // 基础检查
    if (
      !this.renderedRoadLabelGroups[targetLevel] ||
      !roadData?.features?.length ||
      !camera
    ) {
      return
    }

    try {
      // 根据级别设置标签数量限制
      const maxLabels =
        targetLevel === 'primary' ? 100 : targetLevel === 'all' ? 300 : 250

      // 直接渲染标签，移除延迟
      this.geoJsonRenderer.renderRoadLabels(
        roadData,
        this.options.roadNetworkScale,
        this.renderedRoadLabelGroups[targetLevel],
        this.options.roadYLevel,
        camera,
        maxLabels
      )

      // 确保标签组可见
      this.renderedRoadLabelGroups[targetLevel].visible = true
    } catch (error) {
      // 渲染标签时出错，静默处理
    }
  }

  /**
   * 更新视口内可见的标签
   * @private
   */
  _updateVisibleLabels(targetLevel, camera) {
    if (!camera || !this.renderedRoadLabelGroups[targetLevel]) {
      return
    }

    // 重新计算当前相机视锥体
    const frustum = new THREE.Frustum()
    const projScreenMatrix = new THREE.Matrix4()

    // 确保我们使用最新的相机矩阵
    camera.updateMatrixWorld()
    projScreenMatrix.multiplyMatrices(
      camera.projectionMatrix,
      camera.matrixWorldInverse
    )
    frustum.setFromProjectionMatrix(projScreenMatrix)

    let visibleCount = 0
    const maxVisibleLabels = 1500 // 进一步增加同屏最多显示标签数量

    // 获取标签组中的所有标签
    const labels = this.renderedRoadLabelGroups[targetLevel].children

    // 首先标记所有标签为不可见
    for (let i = 0; i < labels.length; i++) {
      labels[i].visible = false
    }

    // 优化：按距离相机的距离排序标签
    const labelsWithDistances = []
    const cameraPosition = camera.position

    for (let i = 0; i < labels.length; i++) {
      const label = labels[i]
      const distance = label.position.distanceTo(cameraPosition)
      labelsWithDistances.push({ label, distance })
    }

    // 按距离排序（近的先显示）
    labelsWithDistances.sort((a, b) => a.distance - b.distance)

    // 计算字体大小调整
    // 基础字体大小 - 减小基础大小
    const baseFontSize = 0.02
    // 相机距离
    const cameraDistance = camera.position.y
    // 根据距离计算字体大小比例，距离越远字体越大但整体更小
    let fontSizeScale = 1.0

    if (cameraDistance < 5) {
      fontSizeScale = 0.5 + (cameraDistance / 5) * 0.3 // 0.5 到 0.8
    } else if (cameraDistance < 20) {
      fontSizeScale = 0.8 + ((cameraDistance - 5) / 15) * 0.3 // 0.8 到 1.1
    } else {
      fontSizeScale = 1.1 + Math.min((cameraDistance - 20) / 40, 1) * 0.7 // 1.1 到 1.8，最大1.8
    }

    // 最终字体大小
    const adjustedFontSize = baseFontSize * fontSizeScale

    // 更新标签可见性

    // 检查标签是否在视锥体内并更新可见性
    for (
      let i = 0;
      i < labelsWithDistances.length && visibleCount < maxVisibleLabels;
      i++
    ) {
      const label = labelsWithDistances[i].label

      // 检查标签是否在视锥体内
      if (frustum.containsPoint(label.position)) {
        label.visible = true

        // 更新标签大小，添加优化：只在大小变化明显时才同步
        if (Math.abs(label.fontSize - adjustedFontSize) > 0.001) {
          label.fontSize = adjustedFontSize
          label.sync() // 重新同步文本渲染
        }

        visibleCount++
      }
    }

    // 确保标签组整体是可见的
    this.renderedRoadLabelGroups[targetLevel].visible = true

    // 标签可见性更新完成
  }

  /**
   * 设置距离阈值
   * @param {Object} thresholds - 包含 hide, primary, secondary 属性的对象
   */
  setDistanceThresholds(thresholds) {
    this.distanceThresholds = {
      ...this.distanceThresholds,
      ...thresholds,
    }

    // 重置当前加载级别，以便下次调用updateRoadsByDistance时强制刷新
    this.currentLoadedLevel = null
  }

  // 新增：更新道路数据源的方法
  updateRoadDataSource(geojson) {
    // 1. 清除所有现有的道路数据和缓存
    this.allRoadFeatures = []
    this.roadDataCache = {
      primary: null,
      secondary: null,
      tertiary: null,
      primarySecondary: null,
      all: null,
    }
    this.groupedRoadsCache.clear()
    this.currentLoadedLevel = null // 重置当前加载的级别，以强制重新评估

    // 重置已渲染标记，确保下次调用updateRoadsByDistance时会重新渲染
    this.renderedLevels = {
      primary: false,
      primarySecondary: false,
      all: false,
    }

    // 2. 处理新的 GeoJSON 数据
    if (!geojson || !Array.isArray(geojson.features)) {
      // 即使新数据无效，也要确保清除场景中的旧路网
      this.geoJsonRenderer.processRoadNetwork(
        null,
        this.options.roadNetworkScale,
        this.roadNetworkGroup,
        this.roadLabelsGroup,
        this.options.getMaterialForRoadType,
        this.options.roadYLevel,
        0
      )
      return
    }

    // 3. 使用新数据填充 allRoadFeatures
    this.allRoadFeatures = geojson.features

    // 4. 根据道路类型预处理分类数据
    this.allRoadDataProcessed = false // 重置预处理标记

    // 按道路类型和名称进行数据预处理
    try {
      console.log('开始按道路类型和名称分类处理数据...')

      // 创建按级别分类的特征
      const primaryFeatures = []
      const secondaryFeatures = []
      const tertiaryFeatures = []

      // 按道路名称分组，用于确保同一条道路的所有分段被放在一起
      const roadsByName = {}

      // 第一步：按名称收集所有道路分段
      this.allRoadFeatures.forEach(feature => {
        const name = feature.properties?.name
        if (name) {
          if (!roadsByName[name]) {
            roadsByName[name] = []
          }
          roadsByName[name].push(feature)
        }
      })

      console.log(`共发现${Object.keys(roadsByName).length}条有名称的道路`)

      // 第二步：确定每条道路的级别（以最高级别为准）
      // 道路级别字典（key：道路名称，value：道路级别）
      const roadLevels = {}

      Object.keys(roadsByName).forEach(name => {
        const segments = roadsByName[name]
        let highestLevel = 'tertiary' // 默认最低级别

        // 检查该路中是否有高级别道路（按顺序：primary > secondary > tertiary）
        if (
          segments.some(
            f =>
              f.properties?.highway === 'trunk' ||
              f.properties?.highway === 'motorway' ||
              f.properties?.highway === 'primary'
          )
        ) {
          highestLevel = 'primary'
        } else if (segments.some(f => f.properties?.highway === 'secondary')) {
          highestLevel = 'secondary'
        }

        // 记录这条路的级别
        roadLevels[name] = highestLevel
      })

      // 第三步：将所有道路分段根据道路名称的级别进行分类
      Object.keys(roadsByName).forEach(name => {
        const segments = roadsByName[name]
        const level = roadLevels[name]

        // 根据level将所有分段放入相应的列表
        if (level === 'primary') {
          primaryFeatures.push(...segments)
        } else if (level === 'secondary') {
          secondaryFeatures.push(...segments)
        } else {
          tertiaryFeatures.push(...segments)
        }
      })

      // 第四步：处理没有名称的道路分段
      this.allRoadFeatures.forEach(feature => {
        if (!feature.properties?.name) {
          // 根据highway属性来判断级别
          const highway = feature.properties?.highway
          if (
            highway === 'trunk' ||
            highway === 'motorway' ||
            highway === 'primary'
          ) {
            primaryFeatures.push(feature)
          } else if (highway === 'secondary') {
            secondaryFeatures.push(feature)
          } else {
            tertiaryFeatures.push(feature)
          }
        }
      })

      // 创建各级别的GeoJSON
      this.roadDataCache.primary = {
        type: 'FeatureCollection',
        features: primaryFeatures,
      }

      this.roadDataCache.secondary = {
        type: 'FeatureCollection',
        features: secondaryFeatures,
      }

      this.roadDataCache.tertiary = {
        type: 'FeatureCollection',
        features: tertiaryFeatures,
      }

      // 创建组合数据
      this.roadDataCache.primarySecondary = {
        type: 'FeatureCollection',
        features: [...primaryFeatures, ...secondaryFeatures],
      }

      this.roadDataCache.all = {
        type: 'FeatureCollection',
        features: [
          ...primaryFeatures,
          ...secondaryFeatures,
          ...tertiaryFeatures,
        ],
      }

      this.allRoadDataProcessed = true

      // 道路数据预处理完成
    } catch (error) {
      // 即使预处理失败，也确保all级别有数据
      this.roadDataCache.all = {
        type: 'FeatureCollection',
        features: [
          ...primaryFeatures,
          ...secondaryFeatures,
          ...tertiaryFeatures,
        ],
      }

      this.allRoadDataProcessed = true
    }

    // 清理道路组
    Object.values(this.renderedRoadGroups).forEach(group => {
      while (group.children.length > 0) {
        const child = group.children[0]
        group.remove(child)
        if (child.geometry) child.geometry.dispose()
        // 材质通常是共享的，不在这里处理
      }
    })

    // 清理标签组
    Object.values(this.renderedRoadLabelGroups).forEach(group => {
      while (group.children.length > 0) {
        const child = group.children[0]
        group.remove(child)
        if (child.material && child.material.map) {
          child.material.map.dispose()
          child.material.dispose()
        }
      }
    })

    // 新增：强制触发一次路网显示刷新
    // console.log('强制刷新路网显示...');
    // this.updateRoadsByDistance(0, null); // 注释掉此行，因为它不含相机信息，会导致初始渲染不正确
  }

  /**
   * 实时更新视锥体剔除 - 在相机移动/旋转时调用
   * @param {THREE.Camera} camera - Three.js 相机
   */
  updateVisibleLabelsByCamera(camera) {
    if (!camera) return

    // 遍历所有级别的标签组并更新它们的可见性
    Object.keys(this.renderedRoadLabelGroups).forEach(level => {
      if (this.renderedRoadLabelGroups[level]?.visible) {
        this._updateVisibleLabels(level, camera)
      }
    })
  }

  /**
   * 相机变化事件的回调函数 - 连接到Three.js的控制器更新事件
   * @param {THREE.Camera} camera - Three.js 相机
   */
  onCameraUpdate(camera) {
    // 根据相机更新标签可见性
    this.updateVisibleLabelsByCamera(camera)
  }
}

export default DynamicRoadLoader
