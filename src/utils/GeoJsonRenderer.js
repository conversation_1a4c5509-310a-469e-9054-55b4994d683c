import * as THREE from 'three'
import { Line2 } from 'three/examples/jsm/lines/Line2.js'
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial.js'
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry.js'

import {
  transformGeoJsonCoordinates,
  COORDINATE_CONFIG,
} from '@/utils/coordinateTransform'
import iconsApi from '@/api/modules/icons.js'
import { ICON_CONFIG } from '@/config/icon.config'
import RoadLabelManager from './RoadLabelManager.js'

class GeoJsonRenderer {
  constructor(sceneManager, materialsCache) {
    // 场景管理器
    this.sceneManager = sceneManager
    // 材料缓存
    this.materialsCache = materialsCache
    // 道路标签管理器
    this.roadLabelManager = new RoadLabelManager()
    // 保留旧的缓存属性以兼容现有代码
    this.roadLabelsCache = null
  }

  // 通用3D文字标签的函数（贴在地面上）
  createTextLabel(text, position, options = {}) {
    // 默认配置
    const defaultOptions = {
      fontSize: 0.01, // 进一步减小默认字体大小
      color: 0x1a2b3d, // 使用更暗的深蓝色，降低亮度
      anchorX: 'center', // 默认水平居中
      anchorY: 'middle', // 默认垂直居中
      yOffset: 0, // 默认无偏移，高度在位置中直接设置
      userData: {}, // 用户数据
    }

    // 合并选项
    const effectiveOptions = { ...defaultOptions, ...options }

    // 创建高分辨率画布来绘制文字
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')

    // 设置高分辨率画布大小和文字样式
    // 根据传入的fontSize参数动态调整画布字体大小
    const baseFontSize = 64 // 增大基础字体大小
    // 简化计算：如果是区域标签，使用更大的字体
    const actualFontSize = effectiveOptions.isAreaLabel
      ? baseFontSize * 2.0 // 区域标签使用2倍大小
      : baseFontSize *
        Math.max(0.8, Math.min(2.0, effectiveOptions.fontSize * 1.0))

    // 先设置字体以测量文字尺寸
    context.font = `${actualFontSize}px Microsoft YaHei, Arial, sans-serif`
    const textMetrics = context.measureText(text)

    // 动态计算画布大小，根据文字内容调整
    const padding = actualFontSize * 0.1 // 减小内边距
    canvas.width = Math.max(textMetrics.width + padding * 2, 64) // 减小最小宽度
    canvas.height = Math.max(actualFontSize + padding * 2, 32) // 减小最小高度

    context.fillStyle = 'rgba(0, 0, 0, 0)' // 透明背景
    context.fillRect(0, 0, canvas.width, canvas.height)

    // 启用抗锯齿和高质量渲染
    context.imageSmoothingEnabled = true
    context.imageSmoothingQuality = 'high'
    context.textRenderingOptimization = 'optimizeQuality'

    // 重新设置字体（画布大小改变后需要重新设置）
    context.font = `${actualFontSize}px Microsoft YaHei, Arial, sans-serif`
    // 将十六进制颜色转换为CSS颜色字符串
    const colorHex = effectiveOptions.color
    const colorString = `#${colorHex.toString(16).padStart(6, '0')}`
    context.fillStyle = colorString
    context.textAlign = 'center'
    context.textBaseline = 'middle'

    // 绘制文字
    context.fillText(text, canvas.width / 2, canvas.height / 2)

    // 创建纹理
    const texture = new THREE.CanvasTexture(canvas)
    texture.needsUpdate = true

    // 创建材质
    const material = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      alphaTest: 0.1,
      side: THREE.DoubleSide,
    })

    // 根据文字内容动态调整几何体大小
    const isAreaLabel = effectiveOptions.isAreaLabel || false

    // 计算合适的几何体尺寸 - 增大整体尺寸
    let geometryWidth, geometryHeight
    if (isAreaLabel) {
      // 区域标签：增大尺寸，保持合适的边距
      geometryWidth = 0.15 // 增大宽度
      geometryHeight = 0.04 // 增大高度
    } else {
      // 道路标签：保持原有大小
      geometryWidth = 0.08
      geometryHeight = 0.02
    }

    const geometry = new THREE.PlaneGeometry(geometryWidth, geometryHeight)

    // 创建网格
    const mesh = new THREE.Mesh(geometry, material)

    // 设置位置
    if (Array.isArray(position)) {
      const yPos = position[1] + effectiveOptions.yOffset
      // 修复位置设置：position[2]是Z坐标，不是Y坐标
      mesh.position.set(position[0], yPos, position[2] || 0)
    } else if (position instanceof THREE.Vector3) {
      mesh.position.copy(position)
      mesh.position.y = position.y + effectiveOptions.yOffset
    }

    // 简化逻辑：所有标签都水平放置在地面上
    mesh.rotation.x = -Math.PI / 2

    // 设置用户数据
    mesh.userData = {
      ...effectiveOptions.userData,
      isRoadLabel: true,
      isScalableLabel: effectiveOptions.isAreaLabel || false, // 区域标签标记为可缩放
    }

    // 设置可见性
    mesh.visible =
      effectiveOptions.visible !== undefined ? effectiveOptions.visible : true

    return mesh
  }

  // 获取渲染器
  _getRenderer() {
    return this.sceneManager ? this.sceneManager.getRenderer() : null
  }

  // 获取相机
  _getCamera() {
    return this.sceneManager ? this.sceneManager.getCamera() : null
  }

  processCityOutline(
    geojson,
    highlightName,
    scale,
    group,
    cityOutlineYLevel,
    defaultLineWidth,
    highlightLineWidth,
    defaultColor,
    highlightColor
  ) {
    const renderer = this._getRenderer()
    if (!geojson || !group || !renderer) {
      console.warn(
        'GeoJsonRenderer: processCityOutline - Missing geojson, group, or renderer.'
      )
      return null
    }

    // 性能优化：检查是否需要重新处理
    const cacheKey = `${highlightName}_${scale}_${defaultLineWidth}_${highlightLineWidth}`
    if (this._lastCityOutlineCache === cacheKey && group.children.length > 0) {
      return this._lastHighlightedCenter
    }
    this._lastCityOutlineCache = cacheKey

    while (group.children.length > 0) {
      const oldChild = group.children[0]
      group.remove(oldChild)
      if (oldChild.geometry) {
        oldChild.geometry.dispose()
      }
    }

    const getOutlineMaterial = isHighlightFeature => {
      const targetLineWidth = isHighlightFeature
        ? highlightLineWidth
        : defaultLineWidth
      const cacheKey = `city_outline_${targetLineWidth}px`

      // 获取渲染器尺寸（只获取一次）
      const rendererSize = new THREE.Vector2()
      renderer.getSize(rendererSize)

      if (!this.materialsCache[cacheKey]) {
        this.materialsCache[cacheKey] = new LineMaterial({
          color: defaultColor,
          linewidth: targetLineWidth,
          vertexColors: false,
          dashed: false,
          alphaToCoverage: true,
          transparent: true, // 启用透明度
          opacity: 0.6, // 设置透明度，使轮廓更柔和
          resolution: rendererSize,
        })
      } else {
        // 更新分辨率
        this.materialsCache[cacheKey].resolution.set(
          rendererSize.width,
          rendererSize.height
        )
      }
      return this.materialsCache[cacheKey]
    }

    let highlightedCityCenter = null
    const allTransformedCoordsForHighlight = []

    geojson.features.forEach(feature => {
      const featureName = feature.properties?.name
      const isHighlightFeature = featureName === highlightName
      const determinedMaterial = getOutlineMaterial(isHighlightFeature)
      // 克隆材质实例
      const materialInstance = isHighlightFeature
        ? determinedMaterial.clone()
        : determinedMaterial
      if (isHighlightFeature) {
        materialInstance.color.set(highlightColor)
      } else {
        materialInstance.color.set(defaultColor) // 确保默认颜色
      }

      const processCoordinates = coordsArray => {
        if (!coordsArray || coordsArray.length === 0) return
        coordsArray.forEach(singlePolygonCoords => {
          let outlineCoords
          if (feature.geometry.type === 'Polygon') {
            outlineCoords = singlePolygonCoords
          } else {
            outlineCoords = singlePolygonCoords[0]
          }
          if (!outlineCoords || outlineCoords.length < 2) return

          const transformedCoords = transformGeoJsonCoordinates(
            outlineCoords,
            0,
            scale
          )

          if (isHighlightFeature && !highlightedCityCenter) {
            transformedCoords.forEach(coord =>
              allTransformedCoordsForHighlight.push(coord[0], coord[1])
            )
          }

          const positions = []
          transformedCoords.forEach(coord => {
            positions.push(coord[0], cityOutlineYLevel, coord[1])
          })

          if (positions.length >= 6) {
            const firstX = positions[0]
            const firstZ = positions[2]
            const lastX = positions[positions.length - 3]
            const lastZ = positions[positions.length - 1]
            if (firstX !== lastX || firstZ !== lastZ) {
              positions.push(firstX, cityOutlineYLevel, firstZ)
            }
          }
          if (positions.length < 6) return

          const lineGeom = new LineGeometry()
          lineGeom.setPositions(positions)
          const line = new Line2(lineGeom, materialInstance) // 使用材质实例
          line.computeLineDistances()
          group.add(line)
        })
      }
      const geometryType = feature.geometry.type
      const coordinates = feature.geometry.coordinates
      if (geometryType === 'Polygon') {
        processCoordinates([coordinates[0]])
      } else if (geometryType === 'MultiPolygon') {
        processCoordinates(coordinates)
      }
    })

    if (allTransformedCoordsForHighlight.length > 0) {
      let minX = Infinity,
        maxX = -Infinity,
        minZ = Infinity,
        maxZ = -Infinity
      for (let i = 0; i < allTransformedCoordsForHighlight.length; i += 2) {
        const x = allTransformedCoordsForHighlight[i]
        const z = allTransformedCoordsForHighlight[i + 1]
        minX = Math.min(minX, x)
        maxX = Math.max(maxX, x)
        minZ = Math.min(minZ, z)
        maxZ = Math.max(maxZ, z)
      }
      if (minX !== Infinity) {
        highlightedCityCenter = new THREE.Vector3(
          (minX + maxX) / 2,
          cityOutlineYLevel,
          (minZ + maxZ) / 2
        )
      }
    }

    // 缓存结果以供下次比较
    this._lastHighlightedCenter = highlightedCityCenter

    return highlightedCityCenter
  }

  // 处理道路网络
  processRoadNetwork(
    geojson,
    roadNetworkScale,
    roadNetworkGroup,
    roadLabelsGroup,
    getMaterialForRoadType,
    roadYLevel,
    _displayedCameraDistance
  ) {
    // 获取渲染器
    const renderer = this._getRenderer()
    // 检查参数
    if (!roadNetworkGroup || !renderer || !roadLabelsGroup) {
      return
    }

    // 清空现有的道路和标签
    this.clearGroup(roadNetworkGroup)
    this.clearGroup(roadLabelsGroup)
    // 清除标签缓存，因为正在处理新的路网数据
    this.roadLabelsCache = null

    // 检查geojson数据
    if (!geojson || !geojson.features || geojson.features.length === 0) {
      return
    }

    // 第一步：渲染所有道路线段
    geojson.features.forEach(feature => {
      const geometryType = feature.geometry.type
      const coordinates = feature.geometry.coordinates
      const highwayType = feature.properties?.highway
      const constructionType = feature.properties?.construction
      const determinedMaterial = getMaterialForRoadType(
        highwayType,
        constructionType
      )
      const roadName = feature.properties?.name

      const processSingleLineString = lineCoords => {
        if (lineCoords.length < 2 || !determinedMaterial) return
        const transformedCoords = transformGeoJsonCoordinates(
          lineCoords,
          0,
          roadNetworkScale
        )
        const positions = []
        transformedCoords.forEach(coord => {
          positions.push(coord[0], roadYLevel, coord[1])
        })
        if (positions.length < 6) return

        // 直接创建新的线条对象
        const lineGeom = new LineGeometry()
        lineGeom.setPositions(positions)
        const line = new Line2(lineGeom, determinedMaterial)
        line.computeLineDistances()

        // 存储数据用于交互/选择
        line.userData.featureName =
          roadName || `Unnamed Road ID ${feature.id || Date.now()}`
        line.userData.dataSource = 'uploaded' // 或 'osm'
        line.userData.featureId = feature.id
        line.userData.originalHighwayType = highwayType
        line.userData.originalConstructionType = constructionType

        roadNetworkGroup.add(line)
      }

      if (geometryType === 'LineString') {
        processSingleLineString(coordinates)
      } else if (geometryType === 'MultiLineString') {
        coordinates.forEach(singleLineCoords =>
          processSingleLineString(singleLineCoords)
        )
      }
    })

    // 第二步：标签渲染已移至 renderRoadLabels 函数中进行动态处理，此处不再执行
  }

  async processDeviceLocations(
    geojson,
    scale,
    markersGroup,
    yLevel = 0.01,
    _markerColorHex = null, // 不使用静态颜色，由交通指标动态控制
    _markerRadius = 0.05
  ) {
    // 清理现有标记
    this.clearGroup(markersGroup)

    if (!geojson || !geojson.features || geojson.features.length === 0) {
      return
    }

    const deviceTextureLoader = new THREE.TextureLoader()
    // 创建SVG解析器
    const parser = new DOMParser()

    // 缓存已加载的分类图标，避免重复请求
    const classificationIconCache = {}

    // 处理每个设备
    for (const feature of geojson.features) {
      if (feature.geometry && feature.geometry.type === 'Point') {
        const coords = feature.geometry.coordinates
        const transformedCoords = transformGeoJsonCoordinates(
          [coords],
          0,
          scale
        )

        if (transformedCoords.length > 0) {
          const deviceId =
            feature.properties?.id || feature.properties?.edge_id || ''
          const deviceName =
            feature.properties?.name ||
            feature.properties?.edge_name ||
            '未命名设备'
          let deviceModel

          // 检查设备是否有分类字段，并从图标库获取对应的SVG图标
          const deviceClassification = feature.properties?.classification
          let svgIconData = null

          if (deviceClassification) {
            // 检查缓存中是否已有该分类的图标
            if (!classificationIconCache[deviceClassification]) {
              try {
                // 从图标库获取该分类的图标
                const iconData = await iconsApi.getIconByClassification(
                  deviceClassification
                )
                if (iconData && iconData.icon && iconData.icon.data) {
                  classificationIconCache[deviceClassification] =
                    iconData.icon.data
                }
              } catch (error) {
                console.error(
                  `获取分类[${deviceClassification}]图标失败:`,
                  error
                )
              }
            }

            // 从缓存获取SVG数据
            svgIconData = classificationIconCache[deviceClassification]
          }

          // 如果找到分类图标，使用SVG渲染设备图标
          if (svgIconData) {
            try {
              // 解析SVG
              const svgDoc = parser.parseFromString(
                svgIconData,
                'image/svg+xml'
              )
              const svgElement = svgDoc.documentElement

              // 创建SVG数据URL
              const serializer = new XMLSerializer()
              const svgString = serializer.serializeToString(svgElement)
              const svgUrl =
                'data:image/svg+xml;charset=utf-8,' +
                encodeURIComponent(svgString)

              // 加载SVG作为纹理
              const texture = deviceTextureLoader.load(svgUrl)
              const spriteMaterial = new THREE.SpriteMaterial({
                map: texture,
                color: 0xffffff,
                transparent: true,
                alphaTest: ICON_CONFIG.sprite.alphaTest,
                depthWrite: ICON_CONFIG.sprite.depthWrite,
                depthTest: ICON_CONFIG.sprite.depthTest,
                sizeAttenuation: ICON_CONFIG.sprite.sizeAttenuation,
              })

              deviceModel = new THREE.Sprite(spriteMaterial)
              // 使用统一ICON_CONFIG
              deviceModel.scale.set(
                ICON_CONFIG.baseScale,
                ICON_CONFIG.baseScale,
                1
              )
              deviceModel.renderOrder = ICON_CONFIG.renderOrder

              // 设置精灵的中心点在底部中央
              deviceModel.center.set(ICON_CONFIG.center.x, ICON_CONFIG.center.y)

              // 由于现在使用固定大小，不再需要维护设备图标数组

              // 图标位置设置为精确的地面位置
              deviceModel.position.set(
                transformedCoords[0][0],
                yLevel,
                transformedCoords[0][1]
              )
            } catch (error) {
              console.error(`渲染SVG图标失败:`, error)
              // 加载失败时继续检查其他图标来源
              svgIconData = null
            }
          }

          // 如果没有找到分类图标，跳过该设备
          if (!svgIconData) {
            continue
          }

          // 添加用户数据，保存原始feature信息
          deviceModel.userData = {
            feature: feature,
            featureType: 'api-device',
            deviceId: deviceId,
            deviceName: deviceName,
          }
          markersGroup.add(deviceModel)

          // 设备标签功能已移至专门的标签管理器处理
        }
      }
    }
  }

  renderFinalizedRoad(
    roadFeature,
    group,
    labelsGroup,
    getMaterialForUserDrawnRoad,
    roadYLevel,
    labelFontSize,
    labelColorHex
  ) {
    if (!roadFeature?.geometry || !group || !labelsGroup) {
      return null
    }

    // 获取线条要素的属性
    const properties = roadFeature.properties || {}
    const name = properties.name || properties.road_name || '未命名道路'
    const highwayType = properties.highway || 'unclassified'
    const constructionType = properties.construction

    try {
      // 处理LineString几何
      if (roadFeature.geometry.type === 'LineString') {
        const coords = roadFeature.geometry.coordinates
        if (coords.length < 2) return null // 需要至少两点才能形成线

        // 转换为场景坐标系统
        const transformedCoords = transformGeoJsonCoordinates(coords, 0, 1)
        const material = getMaterialForUserDrawnRoad(
          highwayType,
          constructionType
        )

        // 创建线几何体
        const positions = []
        transformedCoords.forEach(coord => {
          positions.push(coord[0], roadYLevel, coord[1])
        })

        if (positions.length < 6) {
          return null
        }

        const lineGeom = new LineGeometry()
        lineGeom.setPositions(positions)

        const line = new Line2(lineGeom, material)
        line.computeLineDistances()
        line.userData = {
          isUserDrawnRoad: true,
          featureName: name,
          originalHighwayType: highwayType,
        }
        group.add(line)

        // 在线的中点创建标签
        if (name && transformedCoords.length > 1) {
          const midPointIndex = Math.floor(transformedCoords.length / 2)
          const labelPos = transformedCoords[midPointIndex]

          // 使用通用的文字标签创建函数
          const roadNameText = this.createTextLabel(
            name,
            [labelPos[0], roadYLevel + 0.005, labelPos[1]], // 使用传入的roadYLevel + 小偏移避免重叠
            {
              fontSize: labelFontSize > 0 ? labelFontSize : 0.08,
              color: labelColorHex || 0xffffff,
              yOffset: 0, // 不需要额外偏移，已在位置中设置
              userData: {
                isUserDrawnRoadLabel: true,
                featureName: name,
              },
              usePool: false,
            }
          )

          labelsGroup.add(roadNameText)
        }

        return line
      } else {
        return null
      }
    } catch (error) {
      console.error('GeoJsonRenderer: renderFinalizedRoad - 错误:', error)
      return null
    }
  }

  // Method to clear groups if needed, e.g. when data source changes - 增强内存管理版本
  clearGroup(group) {
    if (!group) return

    // 立即清理所有子对象，避免内存泄露
    const childrenToRemove = [...group.children]

    // 先从场景中移除所有子对象
    group.clear()

    // 立即释放资源，不再延迟处理
    this._disposeObjects(childrenToRemove)

    // 强制垃圾回收提示
    if (childrenToRemove.length > 50 && window.gc) {
      // 只在清理大量对象时才提示垃圾回收
      setTimeout(() => window.gc(), 10)
    }
  }

  // 增强的批量释放对象资源方法
  _disposeObjects(objects) {
    objects.forEach(object => {
      try {
        // 递归处理子对象
        if (object.children && object.children.length > 0) {
          const children = [...object.children]
          children.forEach(child => {
            object.remove(child)
            this._disposeObjects([child])
          })
        }

        // 释放几何体
        if (object.geometry) {
          object.geometry.dispose()
        }

        // 增强的材质处理
        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach(material => {
              this._disposeMaterial(material)
            })
          } else {
            this._disposeMaterial(object.material)
          }
        }

        // 释放纹理
        if (object.texture) {
          object.texture.dispose()
        }

        // 清理用户数据
        if (object.userData) {
          object.userData = {}
        }

        // 清理事件监听器
        if (object.removeEventListener) {
          // 移除常见的事件监听器
          const commonEvents = ['click', 'mouseover', 'mouseout', 'change']
          commonEvents.forEach(event => {
            try {
              object.removeEventListener(event, () => {})
            } catch (e) {
              // 忽略移除不存在的监听器的错误
            }
          })
        }
      } catch (error) {
        console.warn('释放Three.js对象时出错:', error, object)
      }
    })
  }

  // 增强的材质释放方法
  _disposeMaterial(material) {
    if (!material || !material.dispose) return

    try {
      // 释放所有可能的纹理属性
      const textureProperties = [
        'map',
        'normalMap',
        'bumpMap',
        'specularMap',
        'envMap',
        'lightMap',
        'aoMap',
        'emissiveMap',
        'metalnessMap',
        'roughnessMap',
        'alphaMap',
        'displacementMap',
        'gradientMap',
        'clearcoatMap',
        'clearcoatNormalMap',
        'clearcoatRoughnessMap',
        'iridescenceMap',
        'iridescenceThicknessMap',
        'sheenColorMap',
        'sheenRoughnessMap',
        'specularIntensityMap',
        'specularColorMap',
        'thicknessMap',
        'transmissionMap',
      ]

      textureProperties.forEach(prop => {
        if (material[prop] && material[prop].dispose) {
          material[prop].dispose()
        }
      })

      material.dispose()
    } catch (error) {
      console.warn('释放材质时出错:', error, material)
    }
  }

  processUserPoints(geojsonFeatures, group) {
    const renderer = this._getRenderer()
    if (!group || !renderer) {
      console.warn(
        'GeoJsonRenderer: processUserPoints - Missing group or renderer.'
      )
      return
    }

    // 清理已有内容
    this.clearGroup(group)

    if (!geojsonFeatures?.length) return

    // 不创建任何用户点位图标，直接返回
    return
  }

  processUserPolygons(geojsonFeatures, group, options = {}) {
    const renderer = this._getRenderer()
    if (!group || !renderer) {
      console.warn(
        'GeoJsonRenderer: processUserPolygons - Missing group or renderer.'
      )
      return
    }

    // 清理组
    this.clearGroup(group)

    if (!geojsonFeatures?.length) return

    // 设置默认选项 - 使用配置文件中的值
    const effectiveOptions = {
      outlineColor: 0x0000ff,
      outlineLinewidth: 0.05,
      fillColor: null, // 不设置静态颜色，由交通指标API动态控制
      yLevelOffset: COORDINATE_CONFIG.Y_LEVELS.AREAS,
      showLabels: true,
      labelColor: 0xffffff,
      labelFontSize: 0.1,
      labelYOffset: COORDINATE_CONFIG.Y_LEVELS.AREA_LABELS,
      ...options,
    }

    const rendererSize = new THREE.Vector2()
    renderer.getSize(rendererSize)

    geojsonFeatures.forEach(feature => {
      // 确保是多边形且有至少3个点
      if (
        feature.geometry?.type === 'Polygon' &&
        feature.geometry.coordinates?.[0]?.length >= 3
      ) {
        const exteriorRingCoords = feature.geometry.coordinates[0]

        // 转换GeoJSON坐标到3D场景坐标
        const transformedCoords = transformGeoJsonCoordinates(
          exteriorRingCoords,
          0, // height
          COORDINATE_CONFIG.SCALE_FACTOR // 使用配置的缩放因子
        )

        // 创建3D顶点数组，所有元素都使用相同的坐标系统
        const vertices3D = transformedCoords.map(
          coord =>
            new THREE.Vector3(coord[0], effectiveOptions.yLevelOffset, coord[1])
        )

        // 1. 渲染轮廓 - 使用BufferGeometry创建线条
        if (vertices3D.length >= 2) {
          const points = [...vertices3D]
          // 闭合多边形：如果第一个点和最后一个点不同，添加第一个点到末尾
          if (!vertices3D[0].equals(vertices3D[vertices3D.length - 1])) {
            points.push(vertices3D[0])
          }

          const outlineGeometry = new THREE.BufferGeometry().setFromPoints(
            points
          )
          const outlineMaterial = new THREE.LineBasicMaterial({
            color: effectiveOptions.outlineColor,
            linewidth: effectiveOptions.outlineLinewidth,
          })
          const outlineLine = new THREE.Line(outlineGeometry, outlineMaterial)
          outlineLine.userData = {
            feature: feature,
            featureType: 'user-polygon-outline',
          }
          group.add(outlineLine)
        }

        // 2. 渲染填充 - 直接使用3D三角化创建填充
        if (vertices3D.length >= 3) {
          // 使用Earcut库进行三角化（如果可用），否则使用简单的扇形三角化
          const fillVertices = []
          const fillIndices = []

          // 简单的扇形三角化：从第一个顶点连接所有其他顶点
          for (let i = 1; i < vertices3D.length - 1; i++) {
            // 添加三角形的三个顶点
            fillVertices.push(
              vertices3D[0].x,
              vertices3D[0].y,
              vertices3D[0].z,
              vertices3D[i].x,
              vertices3D[i].y,
              vertices3D[i].z,
              vertices3D[i + 1].x,
              vertices3D[i + 1].y,
              vertices3D[i + 1].z
            )

            // 添加三角形的索引
            const baseIndex = (i - 1) * 3
            fillIndices.push(baseIndex, baseIndex + 1, baseIndex + 2)
          }

          const fillGeometry = new THREE.BufferGeometry()
          fillGeometry.setAttribute(
            'position',
            new THREE.Float32BufferAttribute(fillVertices, 3)
          )
          fillGeometry.setIndex(fillIndices)
          fillGeometry.computeVertexNormals()

          const fillMaterial = new THREE.MeshBasicMaterial({
            color: effectiveOptions.fillColor || 0x000000, // 默认黑色，将由交通指标动态覆盖
            side: THREE.DoubleSide,
          })
          const fillMesh = new THREE.Mesh(fillGeometry, fillMaterial)
          // 不需要额外的位置和旋转，因为顶点已经在正确的3D位置
          fillMesh.position.set(0, 0, 0)
          fillMesh.userData = {
            feature: feature,
            featureType: 'user-polygon-fill',
          }

          group.add(fillMesh)
        }

        // 3. 渲染标签
        const areaName = feature.properties?.name
        if (
          effectiveOptions.showLabels &&
          areaName &&
          transformedCoords.length > 0
        ) {
          // 计算转换后坐标的中心点
          let centerX = 0,
            centerZ = 0
          transformedCoords.forEach(coord => {
            centerX += coord[0]
            centerZ += coord[1]
          })

          const numPoints = transformedCoords.length
          if (numPoints > 0) {
            centerX /= numPoints
            centerZ /= numPoints

            // 使用通用的文字标签创建函数
            const labelText = this.createTextLabel(
              areaName,
              [centerX, 0, centerZ], // 直接放在地面上 (Y=0)
              {
                fontSize: effectiveOptions.labelFontSize,
                color: effectiveOptions.labelColor,
                anchorY: 'bottom',
                yOffset: effectiveOptions.labelYOffset, // 使用传入的Y轴偏移参数
                userData: {
                  feature: feature,
                  featureType: 'user-polygon-label',
                  parentFeatureName: areaName,
                },
                usePool: false,
                isAreaLabel: true, // 标记为区域标签
              }
            )

            if (labelText) {
              group.add(labelText)
            }
          }
        }
      }
    })
  }

  // 修改现有方法，添加renderLabels可选参数
  processRoadNetworkToGroup(
    geojson,
    roadNetworkScale,
    targetRoadGroup,
    targetLabelsGroup,
    getMaterialForRoadType,
    roadYLevel,
    _displayedCameraDistance,
    renderLabels = true
  ) {
    const renderer = this._getRenderer()

    if (!targetRoadGroup || !renderer) {
      console.warn(
        'GeoJsonRenderer: processRoadNetworkToGroup - Missing target groups or renderer.'
      )
      return
    }

    // 清空目标道路组
    this.clearGroup(targetRoadGroup)
    // 标签渲染由此函数解耦，但根据参数决定是否清理标签组和缓存
    if (renderLabels && targetLabelsGroup) {
      this.clearGroup(targetLabelsGroup)
    }
    this.roadLabelsCache = null

    if (!geojson?.features?.length) {
      return
    }

    const materialsMap = new Map() // 为不同道路类型材质创建缓存

    const highwayFeatures = geojson.features.filter(
      feature =>
        feature.geometry &&
        (feature.geometry.type === 'LineString' ||
          feature.geometry.type === 'MultiLineString')
    )

    // 处理道路绘制
    highwayFeatures.forEach(feature => {
      const geometryType = feature.geometry.type
      const coordinates = feature.geometry.coordinates
      const highwayType = feature.properties?.highway || 'unclassified'
      const constructionType = feature.properties?.construction

      // 获取或创建材质
      const materialKey = `${highwayType}_${constructionType || 'normal'}`
      let material = materialsMap.get(materialKey)
      if (!material) {
        material = getMaterialForRoadType(highwayType, constructionType)
        materialsMap.set(materialKey, material)
      }

      const processSingleLineString = lineCoords => {
        if (!lineCoords || lineCoords.length < 2) {
          return
        }

        // 转换坐标
        const transformedCoords = transformGeoJsonCoordinates(
          lineCoords,
          0,
          roadNetworkScale
        )

        // 创建线几何体
        const positions = []
        transformedCoords.forEach(coord => {
          positions.push(coord[0], roadYLevel, coord[1])
        })

        if (positions.length < 6) {
          return // 需要至少2个点形成线
        }

        const lineGeom = new LineGeometry()
        lineGeom.setPositions(positions)

        const line = new Line2(lineGeom, material)
        line.computeLineDistances()
        line.userData = {
          featureName: feature.properties?.name,
          originalHighwayType: highwayType,
          featureId: feature.properties?.id || feature.id,
        }

        targetRoadGroup.add(line)
      }

      if (geometryType === 'LineString') {
        processSingleLineString(coordinates)
      } else if (geometryType === 'MultiLineString') {
        coordinates.forEach(processSingleLineString)
      }
    })

    // 标签渲染逻辑已完全移至 renderRoadLabels 函数
  }

  /**
   * 动态渲染路网标签，具备LOD（细节层次）和缓存功能
   * @param {object} geojson 包含道路特征的GeoJSON对象
   * @param {number} roadNetworkScale 道路网络缩放比例
   * @param {THREE.Group} targetLabelsGroup 用于容纳标签的组
   * @param {number} roadYLevel 道路的Y轴高度
   * @param {THREE.Camera} camera 场景相机
   * @param {number} maxLabels 最大标签数量限制
   */
  renderRoadLabels(
    geojson,
    roadNetworkScale,
    targetLabelsGroup,
    roadYLevel,
    camera = null,
    maxLabels = null
  ) {
    // 使用新的道路标签管理器
    this.roadLabelManager.renderLabels(
      geojson,
      roadNetworkScale,
      targetLabelsGroup,
      roadYLevel,
      camera,
      { maxLabels }
    )
  }
}

export default GeoJsonRenderer
