import { ref } from 'vue';

/**
 * 简单的事件总线工具，用于组件之间的通信
 */
const bus = {};
const listeners = ref({});

/**
 * 监听事件
 * @param {string} event - 事件名称
 * @param {Function} callback - 回调函数
 */
bus.on = (event, callback) => {
	if (!listeners.value[event]) {
		listeners.value[event] = [];
	}
	listeners.value[event].push(callback);
};

/**
 * 移除监听
 * @param {string} event - 事件名称
 * @param {Function} callback - 要移除的回调函数，如不指定则移除该事件所有监听
 */
bus.off = (event, callback) => {
	if (!listeners.value[event]) return;
	if (!callback) {
		listeners.value[event] = [];
		return;
	}
	listeners.value[event] = listeners.value[event].filter((cb) => cb !== callback);
};

/**
 * 触发事件
 * @param {string} event - 事件名称
 * @param {any} args - 传递给监听器的参数
 */
bus.emit = (event, ...args) => {
	if (!listeners.value[event]) return;
	listeners.value[event].forEach((callback) => callback(...args));
};

export default bus;
