/**
 * 干线数据类型定义和工具函数
 * 定义完整的干线数据结构，包含地理数据复制、样式配置、元数据等
 */

/**
 * 干线样式配置
 * @typedef {Object} TrunkLineStyle
 * @property {number} lineWidth - 线宽（像素）
 * @property {number} opacity - 透明度 (0-1)
 * @property {Array|null} dashArray - 虚线样式，null为实线，[5, 5]为虚线
 * @property {number} zIndex - 渲染层级
 * @property {string} lineCap - 线端样式：'round' | 'square' | 'butt'
 * @property {string} lineJoin - 线连接样式：'round' | 'bevel' | 'miter'
 * @property {boolean} animated - 是否启用动画效果
 * @property {number} animationSpeed - 动画速度（仅当animated为true时有效）
 */

/**
 * 干线元数据
 * @typedef {Object} TrunkLineMetadata
 * @property {number} roadCount - 包含的路段数量
 * @property {number} totalLength - 总长度（米）
 * @property {Array} roadTypes - 包含的道路类型列表
 * @property {Object} bounds - 边界框 {minLng, maxLng, minLat, maxLat}
 * @property {Object} center - 中心点 {lng, lat}
 * @property {string} description - 描述信息
 * @property {Array} tags - 标签列表
 */

/**
 * 干线数据结构 (适配新API接口)
 * @typedef {Object} TrunkLine
 * @property {string} route_id - 路线ID (对应API的route_id)
 * @property {string} route_name - 路线名称 (对应API的route_name)
 * @property {string} classification - 路线分类 (对应API的classification)
 * @property {Object} route - 路线地理数据 (对应API的route，GeoJSON格式)
 * @property {string} color - 干线颜色（十六进制，本地扩展字段）
 * @property {TrunkLineStyle} style - 样式配置（本地扩展字段）
 * @property {Array} selectedRoadIds - 选中的路段ID列表（本地扩展字段，用于追溯）
 * @property {string} createdAt - 创建时间（本地扩展字段）
 * @property {string} updatedAt - 更新时间（本地扩展字段）
 * @property {TrunkLineMetadata} metadata - 元数据（本地扩展字段）
 * @property {Object} settings - 其他设置（本地扩展字段）
 */

/**
 * 根据干线分类获取线宽（再增加一倍，总共4倍）
 */
export function getLineWidthByClassification(classification) {
  const lineWidthMap = {
    主要干线: 96, // 再增加一倍，从48增加到96
    次要干线: 72, // 再增加一倍，从36增加到72
    支线: 48, // 再增加一倍，从24增加到48
    快速路: 96, // 再增加一倍，从48增加到96
    环线: 72, // 再增加一倍，从36增加到72
    专用道: 48, // 再增加一倍，从24增加到48
    辅助线: 36, // 再增加一倍，从18增加到36
    临时线: 44, // 再增加一倍，从22增加到44
  }
  return lineWidthMap[classification] || 72 // 默认宽度也再增加一倍，从36增加到72
}

/**
 * 默认干线样式配置
 */
export const DEFAULT_TRUNK_LINE_STYLE = {
  lineWidth: 72, // 再增加一倍，从36增加到72
  opacity: 0.9, // 保持不透明度
  dashArray: null,
  zIndex: 1000,
  lineCap: 'round',
  lineJoin: 'round',
  animated: false,
  animationSpeed: 1.0,
}

/**
 * 默认干线元数据
 */
export const DEFAULT_TRUNK_LINE_METADATA = {
  roadCount: 0,
  totalLength: 0,
  roadTypes: [],
  bounds: { minLng: 0, maxLng: 0, minLat: 0, maxLat: 0 },
  center: { lng: 0, lat: 0 },
  description: '',
  tags: [],
}

/**
 * 创建新的干线对象 (适配新API接口)
 * @param {Object} options - 创建选项
 * @param {string} options.route_name - 路线名称
 * @param {string} options.classification - 路线分类
 * @param {Object} options.route - 路线地理数据 (GeoJSON格式)
 * @param {Array} options.selectedRoadIds - 选中的路段ID列表（可选）
 * @param {TrunkLineStyle} options.style - 样式配置（可选）
 * @param {TrunkLineMetadata} options.metadata - 元数据（可选）
 * @returns {TrunkLine} 新的干线对象
 */
export function createTrunkLine(options) {
  const now = new Date().toISOString()

  return {
    // API必需字段
    route_name: options.route_name || '未命名干线',
    classification: options.classification || '主要干线',
    route: options.route || { type: 'FeatureCollection', features: [] },

    // 本地扩展字段 - 颜色由交通指标动态控制
    style: {
      ...DEFAULT_TRUNK_LINE_STYLE,
      lineWidth: getLineWidthByClassification(
        options.classification || '主要干线'
      ),
      ...(options.style || {}),
    },
    selectedRoadIds: options.selectedRoadIds || [],
    createdAt: now,
    updatedAt: now,
    metadata: { ...DEFAULT_TRUNK_LINE_METADATA, ...(options.metadata || {}) },
    settings: options.settings || {},
  }
}

/**
 * 将本地干线数据转换为API格式
 * 简化版本：直接使用route数据
 */
export function convertTrunkLineToApiFormat(trunkLine) {
  return {
    route_name: trunkLine.route_name,
    classification: trunkLine.classification,
    route: trunkLine.route, // 直接使用原始route数据
  }
}

/**
 * 将API格式数据转换为本地干线数据
 * 简化版本：减少嵌套判断，使用辅助函数
 */
export function convertApiDataToTrunkLine(apiData, localExtensions = {}) {
  const now = new Date().toISOString()
  const standardizedRoute = standardizeRouteFormat(
    apiData.route,
    apiData.route_id
  )

  return {
    // API字段
    route_id: apiData.route_id,
    route_name: apiData.route_name,
    classification: apiData.classification,
    route: standardizedRoute,

    // 本地扩展字段 - 颜色由交通指标动态控制
    style: {
      ...DEFAULT_TRUNK_LINE_STYLE,
      lineWidth: getLineWidthByClassification(apiData.classification),
      ...localExtensions.style,
    },
    selectedRoadIds: localExtensions.selectedRoadIds || [],
    createdAt: localExtensions.createdAt || now,
    updatedAt: now,
    metadata: { ...DEFAULT_TRUNK_LINE_METADATA, ...localExtensions.metadata },
    settings: localExtensions.settings || {},
  }
}

/**
 * 标准化路线格式为FeatureCollection
 * @param {Object} route - 路线数据
 * @param {string} routeId - 路线ID
 * @returns {Object} 标准化的FeatureCollection
 */
function standardizeRouteFormat(route, routeId) {
  if (!route) {
    return { type: 'FeatureCollection', features: [] }
  }

  if (route.type === 'FeatureCollection') {
    return route
  }

  if (route.type === 'LineString') {
    return {
      type: 'FeatureCollection',
      features: [
        {
          type: 'Feature',
          geometry: {
            type: 'LineString',
            coordinates: route.coordinates,
          },
          properties: {
            source: 'trunk_line',
            name: `干线路段_${routeId}`,
            highway: 'trunk_line',
            isTrunkLineSegment: true,
          },
        },
      ],
    }
  }

  return route
}

/**
 * 验证干线数据结构
 * 精简版：只检查必要字段，移除过度验证
 */
export function validateTrunkLine(trunkLine) {
  const errors = []

  if (!trunkLine.route_name) {
    errors.push('缺少干线名称')
  }
  if (!trunkLine.classification) {
    errors.push('缺少干线分类')
  }
  if (!trunkLine.route?.features?.length) {
    errors.push('缺少地理数据')
  }

  return { isValid: errors.length === 0, errors }
}

/**
 * 计算GeoJSON数据的边界框
 * 简化版本：使用reduce和辅助函数
 */
export function calculateBounds(geoData) {
  if (!geoData?.features?.length) {
    return { minLng: 0, maxLng: 0, minLat: 0, maxLat: 0 }
  }

  const bounds = {
    minLng: Infinity,
    maxLng: -Infinity,
    minLat: Infinity,
    maxLat: -Infinity,
  }

  geoData.features.forEach(feature => {
    if (feature.geometry?.coordinates) {
      updateBoundsFromGeometry(bounds, feature.geometry)
    }
  })

  return bounds
}

/**
 * 根据几何体更新边界框
 */
function updateBoundsFromGeometry(bounds, geometry) {
  const { type, coordinates } = geometry

  if (type === 'LineString') {
    updateBoundsFromCoordinates(bounds, coordinates)
  } else if (type === 'MultiLineString') {
    coordinates.forEach(lineCoords => {
      updateBoundsFromCoordinates(bounds, lineCoords)
    })
  }
}

/**
 * 根据坐标数组更新边界框
 */
function updateBoundsFromCoordinates(bounds, coordinates) {
  coordinates.forEach(([lng, lat]) => {
    bounds.minLng = Math.min(bounds.minLng, lng)
    bounds.maxLng = Math.max(bounds.maxLng, lng)
    bounds.minLat = Math.min(bounds.minLat, lat)
    bounds.maxLat = Math.max(bounds.maxLat, lat)
  })
}

/**
 * 计算边界框的中心点
 * @param {Object} bounds - 边界框
 * @returns {Object} 中心点 {lng, lat}
 */
export function calculateCenter(bounds) {
  return {
    lng: (bounds.minLng + bounds.maxLng) / 2,
    lat: (bounds.minLat + bounds.maxLat) / 2,
  }
}

/**
 * 计算GeoJSON数据的总长度
 * 简化版本：使用reduce和统一的处理逻辑
 */
export function calculateTotalLength(geoData) {
  if (!geoData?.features?.length) {
    return 0
  }

  return geoData.features.reduce((total, feature) => {
    if (!feature.geometry?.coordinates) {
      return total
    }

    const { type, coordinates } = feature.geometry

    if (type === 'LineString') {
      return total + calculateLineStringLength(coordinates)
    }

    if (type === 'MultiLineString') {
      return (
        total +
        coordinates.reduce(
          (sum, lineCoords) => sum + calculateLineStringLength(lineCoords),
          0
        )
      )
    }

    return total
  }, 0)
}

/**
 * 计算LineString的长度（使用简化的球面距离公式）
 * @param {Array} coordinates - 坐标数组
 * @returns {number} 长度（米）
 */
function calculateLineStringLength(coordinates) {
  if (coordinates.length < 2) {
    return 0
  }

  let length = 0
  for (let i = 1; i < coordinates.length; i++) {
    const [lng1, lat1] = coordinates[i - 1]
    const [lng2, lat2] = coordinates[i]
    length += haversineDistance(lat1, lng1, lat2, lng2)
  }

  return length
}

/**
 * 使用Haversine公式计算两点间距离
 * @param {number} lat1 - 第一个点的纬度
 * @param {number} lng1 - 第一个点的经度
 * @param {number} lat2 - 第二个点的纬度
 * @param {number} lng2 - 第二个点的经度
 * @returns {number} 距离（米）
 */
function haversineDistance(lat1, lng1, lat2, lng2) {
  const R = 6371000 // 地球半径（米）
  const dLat = ((lat2 - lat1) * Math.PI) / 180
  const dLng = ((lng2 - lng1) * Math.PI) / 180
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}

/**
 * 提取道路类型列表
 * 简化版本：使用filter和map的链式调用
 */
export function extractRoadTypes(geoData) {
  if (!geoData?.features) {
    return []
  }

  const roadTypes = geoData.features
    .filter(feature => feature.properties?.highway)
    .map(feature => feature.properties.highway)

  return [...new Set(roadTypes)]
}

/**
 * 更新干线元数据
 * @param {TrunkLine} trunkLine - 干线对象
 * @returns {TrunkLine} 更新后的干线对象
 */
export function updateTrunkLineMetadata(trunkLine) {
  // 适配新API格式，使用route字段
  const geoData = trunkLine.route || trunkLine.geoData
  const bounds = calculateBounds(geoData)
  const center = calculateCenter(bounds)
  const totalLength = calculateTotalLength(geoData)
  const roadTypes = extractRoadTypes(geoData)

  trunkLine.metadata = {
    ...trunkLine.metadata,
    roadCount: trunkLine.selectedRoadIds?.length || 0,
    totalLength,
    roadTypes,
    bounds,
    center,
  }

  trunkLine.updatedAt = new Date().toISOString()

  return trunkLine
}
