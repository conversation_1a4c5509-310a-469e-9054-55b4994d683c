/**
 * 交通指标颜色管理工具
 * 基于真实API数据的服务水平分级
 */

// 服务水平颜色映射
export const SERVICE_LEVEL_COLORS = {
  A: '#00FF00', // 绿色 - 非常顺畅
  B: '#7FFF00', // 浅绿色 - 较顺畅
  C: '#FFFF00', // 黄色 - 基本顺畅
  D: '#FFA500', // 橙色 - 一般拥堵
  E: '#FF0000', // 红色 - 拥堵
  F: '#8B0000', // 深红色 - 严重拥堵
}

// 服务水平颜色映射（RGB格式，用于Three.js）
export const SERVICE_LEVEL_COLORS_RGB = {
  A: { r: 0, g: 255, b: 0 }, // 绿色
  B: { r: 127, g: 255, b: 0 }, // 浅绿色
  C: { r: 255, g: 255, b: 0 }, // 黄色
  D: { r: 255, g: 165, b: 0 }, // 橙色
  E: { r: 255, g: 0, b: 0 }, // 红色
  F: { r: 139, g: 0, b: 0 }, // 深红色
}

// 服务水平描述
export const SERVICE_LEVEL_DESCRIPTIONS = {
  A: '非常顺畅',
  B: '较顺畅',
  C: '基本顺畅',
  D: '一般拥堵',
  E: '拥堵',
  F: '严重拥堵',
}

/**
 * 根据服务水平获取颜色
 * @param {string} serviceLevel - 服务水平 (A-F)
 * @returns {string} 十六进制颜色值
 */
export const getServiceLevelColor = serviceLevel => {
  return SERVICE_LEVEL_COLORS[serviceLevel] || '#CCCCCC'
}

/**
 * 根据服务水平获取RGB颜色（用于Three.js）
 * @param {string} serviceLevel - 服务水平 (A-F)
 * @returns {Object} RGB颜色对象 {r, g, b}
 */
export const getServiceLevelColorRGB = serviceLevel => {
  return SERVICE_LEVEL_COLORS_RGB[serviceLevel] || { r: 204, g: 204, b: 204 }
}

/**
 * 根据服务水平获取Three.js归一化颜色
 * @param {string} serviceLevel - 服务水平 (A-F)
 * @returns {Object} 归一化RGB值 {r, g, b}
 */
export const getServiceLevelThreeColor = serviceLevel => {
  const rgb = getServiceLevelColorRGB(serviceLevel)
  return {
    r: rgb.r / 255,
    g: rgb.g / 255,
    b: rgb.b / 255,
  }
}

/**
 * 根据服务水平获取描述
 * @param {string} serviceLevel - 服务水平 (A-F)
 * @returns {string} 服务水平描述
 */
export const getServiceLevelDescription = serviceLevel => {
  return SERVICE_LEVEL_DESCRIPTIONS[serviceLevel] || '未知'
}
