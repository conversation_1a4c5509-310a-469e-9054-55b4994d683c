// stsecs2项目独立的身份验证工具
// 使用不同的Token键名，避免与sigopt-box冲突
const TokenKey = 'STSECS2-Token';

/**
 * 获取登录Token
 * @returns {String|null} 存储的token或null
 */
export function getToken() {
  try {
    return localStorage.getItem(TokenKey);
  } catch (e) {
    console.error('读取Token失败:', e);
    return null;
  }
}

/**
 * 设置登录Token
 * @param {String} token token字符串
 * @returns {Boolean} 操作是否成功
 */
export function setToken(token) {
  try {
    localStorage.setItem(TokenKey, token);
    return true;
  } catch (e) {
    console.error('设置Token失败:', e);
    return false;
  }
}

/**
 * 移除Token（登出时使用）
 * @returns {Boolean} 操作是否成功
 */
export function removeToken() {
  try {
    localStorage.removeItem(TokenKey);
    return true;
  } catch (e) {
    console.error('移除Token失败:', e);
    return false;
  }
}

/**
 * 检查用户是否已登录
 * @returns {Boolean} 是否已登录
 */
export function isLoggedIn() {
  return !!getToken();
}
