/**
 * sigoptBoxResolver.js
 * 用于处理sigopt-box项目的模块解析问题
 */
import { isDev } from '@/config/env.config'

const moduleCache = new Map()

// 简化的模块映射，只保留核心模块
const CORE_MODULES = {
  '@/utils/configManager': () =>
    import('../../../sigopt-box/src/utils/configManager.js'),
  '@/utils/request': () => import('../api/service.js'),
  '@s2/utils/auth': () => import('../utils/auth/index.js'),

  components: {
    '@/components/PageHeader.vue': () =>
      import('../../../sigopt-box/src/components/PageHeader.vue'),
    '@/components/ErrorMessageAlert.vue': () =>
      import('../../../sigopt-box/src/components/ErrorMessageAlert.vue'),
    '@/components/DataTable.vue': () =>
      import('../../../sigopt-box/src/components/DataTable.vue'),
    '@/components/SearchForm.vue': () =>
      import('../../../sigopt-box/src/components/SearchForm.vue'),
    '@/components/ActionColumn.vue': () =>
      import('../../../sigopt-box/src/components/ActionColumn.vue'),
    '@/components/EditableRow.vue': () =>
      import('../../../sigopt-box/src/components/EditableRow.vue'),
    '@/components/DateRangePicker.vue': () =>
      import('../../../sigopt-box/src/components/DateRangePicker.vue'),
    '@/components/ConfigPreview.vue': () =>
      import('../../../sigopt-box/src/components/ConfigPreview.vue'),
    '@/components/FlowDisplay.vue': () =>
      import('../../../sigopt-box/src/components/FlowDisplay.vue'),
    '@/components/StageSelector.vue': () =>
      import('../../../sigopt-box/src/components/StageSelector.vue'),
    '@/components/ConnectionLine.vue': () =>
      import('../../../sigopt-box/src/components/ConnectionLine.vue'),
  },

  icons: {
    '@/components/icons/IconLinks.vue': () =>
      import('../../../sigopt-box/src/components/icons/IconLinks.vue'),
    '@/components/icons/IconWatch.vue': () =>
      import('../../../sigopt-box/src/components/icons/IconWatch.vue'),
    '@/components/icons/IconAction.vue': () =>
      import('../../../sigopt-box/src/components/icons/IconAction.vue'),
    '@/components/icons/IconMonth.vue': () =>
      import('../../../sigopt-box/src/components/icons/IconMonth.vue'),
    '@/components/icons/IconWeek.vue': () =>
      import('../../../sigopt-box/src/components/icons/IconWeek.vue'),
    '@/components/icons/IconYear.vue': () =>
      import('../../../sigopt-box/src/components/icons/IconYear.vue'),
  },

  views: {
    '@/views/index/antTable.vue': () =>
      import('../../../sigopt-box/src/views/index/antTable.vue'),
    '@/views/index/LogConsole.vue': () =>
      import('../../../sigopt-box/src/views/index/LogConsole.vue'),
    '@/views/miniMap/ThreeDMap.vue': () =>
      import('../../../sigopt-box/src/views/miniMap/ThreeDMap.vue'),
    '@/views/miniMap/mini-map.vue': () =>
      import('../../../sigopt-box/src/views/miniMap/mini-map.vue'),
    '@/views/miniMap/js/lane.js': () =>
      import('../../../sigopt-box/src/views/miniMap/js/lane.js'),
    '@/views/miniMap/js/baseDI.js': () =>
      import('../../../sigopt-box/src/views/miniMap/js/baseDI.js'),
  },

  commandForms: {
    '@/components/command-forms/ManualCommandForm.vue': () =>
      import(
        '../../../sigopt-box/src/components/command-forms/ManualCommandForm.vue'
      ),
    '@/components/command-forms/PhaseCommandForm.vue': () =>
      import(
        '../../../sigopt-box/src/components/command-forms/PhaseCommandForm.vue'
      ),
    '@/components/command-forms/PlanCommandForm.vue': () =>
      import(
        '../../../sigopt-box/src/components/command-forms/PlanCommandForm.vue'
      ),
  },
}

// 直接使用核心模块映射

/**
 * 从缓存中获取模块
 */
function getModuleFromCache(path) {
  if (
    typeof window !== 'undefined' &&
    window.__SIGOPT_BOX_MODULE_CACHE__ &&
    window.__SIGOPT_BOX_MODULE_CACHE__[path]
  ) {
    return window.__SIGOPT_BOX_MODULE_CACHE__[path]
  }

  if (moduleCache.has(path)) {
    return moduleCache.get(path)
  }

  return null
}

/**
 * 将模块保存到缓存
 */
function saveModuleToCache(path, module) {
  moduleCache.set(path, module)

  if (typeof window !== 'undefined' && window.__SIGOPT_BOX_MODULE_CACHE__) {
    window.__SIGOPT_BOX_MODULE_CACHE__[path] = module
  }
}

/**
 * 简化版模块解析器
 */
export async function resolveSigoptBoxModule(path) {
  const cachedModule = getModuleFromCache(path)
  if (cachedModule) {
    return cachedModule
  }

  let module

  // 检查核心模块
  if (CORE_MODULES[path]) {
    module = await CORE_MODULES[path]()
    saveModuleToCache(path, module)
    return module
  }

  // 处理@s2路径（当前项目路径）
  if (path.startsWith('@s2/')) {
    const modulePath = path.replace('@s2/', '../')
    module = await import(/* @vite-ignore */ modulePath)
    saveModuleToCache(path, module)
    return module
  }

  // 处理@/路径（sigopt-box项目路径）
  const modulePath = path.replace('@/', '../../../sigopt-box/src/')
  module = await import(/* @vite-ignore */ modulePath)
  saveModuleToCache(path, module)
  return module
}

/**
 * 注入模块解析器到全局
 */
export function injectModuleResolver() {
  if (typeof window !== 'undefined') {
    window.__SIGOPT_BOX_MODULE_RESOLVER__ = resolveSigoptBoxModule
    window.__SIGOPT_BOX_MODULE_CACHE__ =
      window.__SIGOPT_BOX_MODULE_CACHE__ || {}

    const originalImport = window.__vite_plugin_vue_import_helper__
    if (originalImport) {
      window.__vite_plugin_vue_import_helper__ = function (path) {
        if (
          path.startsWith('@/') &&
          window.location.href.includes('sigopt-box')
        ) {
          return resolveSigoptBoxModule(path)
        }
        return originalImport(path)
      }
    }

    return true
  }
  return false
}

export default {
  resolveSigoptBoxModule,
  injectModuleResolver,
}
