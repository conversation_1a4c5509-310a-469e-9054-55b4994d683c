import RoadLabelManager from './RoadLabelManager.js'
import { COORDINATE_CONFIG } from '@/utils/coordinateTransform'

/**
 * 用户道路标签管理器
 * 继承自RoadLabelManager，专门处理用户绘制的道路标签
 */
class UserRoadLabelManager extends RoadLabelManager {
  constructor() {
    super()
    this.userRoadLabels = new Map() // 存储用户道路标签
    this.visibilityConfig.maxDistance = 5.0 // 用户道路标签显示距离更远
  }

  /**
   * 为单条用户道路创建标签
   */
  createUserRoadLabel(roadName, coordinates, roadFeature, options = {}) {
    if (!roadName || !coordinates || coordinates.length < 2) {
      return null
    }

    // 计算道路总长度和标签数量
    let totalLength = 0
    for (let i = 1; i < coordinates.length; i++) {
      const p1 = coordinates[i - 1]
      const p2 = coordinates[i]
      const dx = p2[0] - p1[0]
      const dy = p2[1] - p1[1]
      const dz = p2[2] - p1[2]
      totalLength += Math.sqrt(dx * dx + dy * dy + dz * dz)
    }

    // 根据长度决定标签数量 - 移除人为限制
    let labelCount = 1
    if (totalLength > 5) {
      // 让标签数量完全由道路长度决定，每5公里一个标签
      labelCount = Math.ceil(totalLength / 5)
    }

    const labels = []

    // 创建标签
    for (let i = 0; i < labelCount; i++) {
      const position = labelCount === 1 ? 0.5 : i / (labelCount - 1)
      const labelIndex = Math.floor(position * (coordinates.length - 1))

      if (labelIndex >= 0 && labelIndex < coordinates.length) {
        const labelPos = coordinates[labelIndex]

        // 计算方向
        let direction = 0
        if (labelIndex > 0 && labelIndex < coordinates.length - 1) {
          const prevIndex = Math.max(0, labelIndex - 1)
          const nextIndex = Math.min(coordinates.length - 1, labelIndex + 1)
          const prevPoint = coordinates[prevIndex]
          const nextPoint = coordinates[nextIndex]
          const dx = nextPoint[0] - prevPoint[0]
          const dy = nextPoint[1] - prevPoint[1]
          direction = Math.atan2(dy, dx)
        }

        const label = this.createLabel(
          roadName,
          [
            labelPos[0],
            COORDINATE_CONFIG.Y_LEVELS.ROADS + 0.01,
            labelPos[2] || labelPos[1],
          ],
          {
            fontSize: options.fontSize || 0.008,
            color: this.styleConfig.color,
            rotation: direction,
            roadType: 'user-drawn',
            source: 'user-drawn',
            userData: {
              isUserDrawnRoadLabel: true,
              featureName: roadName,
              feature: roadFeature,
              featureId: roadFeature?.id || roadFeature?.properties?.id,
              highway:
                roadFeature?.properties?.highway ||
                roadFeature?.properties?.level,
            },
          }
        )

        labels.push(label)
      }
    }

    return labels
  }

  /**
   * 添加用户道路标签
   */
  addUserRoadLabels(
    roadName,
    coordinates,
    roadFeature,
    targetGroup,
    options = {}
  ) {
    if (!targetGroup) {
      return []
    }

    const labels = this.createUserRoadLabel(
      roadName,
      coordinates,
      roadFeature,
      options
    )
    if (!labels || labels.length === 0) {
      return []
    }

    // 存储标签引用
    const roadId = roadFeature?.id || roadFeature?.properties?.id || roadName
    this.userRoadLabels.set(roadId, labels)

    // 添加到场景
    labels.forEach(label => {
      targetGroup.add(label)
    })

    return labels
  }

  /**
   * 移除用户道路标签
   */
  removeUserRoadLabels(roadId, targetGroup) {
    const labels = this.userRoadLabels.get(roadId)
    if (!labels) {
      return
    }

    labels.forEach(label => {
      if (targetGroup && targetGroup.children.includes(label)) {
        targetGroup.remove(label)
      }
      // 清理3D网格对象
      if (label.material) {
        if (label.material.map) {
          label.material.map.dispose()
        }
        label.material.dispose()
      }
      if (label.geometry) {
        label.geometry.dispose()
      }
    })

    this.userRoadLabels.delete(roadId)
  }

  /**
   * 更新用户道路标签的可见性
   */
  updateUserRoadLabelsVisibility(cameraDistance, _roadDisplayMode) {
    const shouldShow = cameraDistance < this.visibilityConfig.maxDistance

    this.userRoadLabels.forEach(labels => {
      labels.forEach(label => {
        // 基于距离控制可见性
        label.visible = shouldShow
      })
    })
  }

  /**
   * 根据道路等级过滤标签可见性
   */
  filterLabelsByRoadLevel(
    shouldShowPrimary,
    shouldShowSecondary,
    shouldShowTertiary
  ) {
    this.userRoadLabels.forEach(labels => {
      labels.forEach(label => {
        const roadLevel = label.userData?.highway || 'tertiary'

        let shouldShow = false
        if (roadLevel === 'primary') {
          shouldShow = shouldShowPrimary
        } else if (roadLevel === 'secondary') {
          shouldShow = shouldShowSecondary
        } else {
          shouldShow = shouldShowTertiary
        }

        label.visible = label.visible && shouldShow
      })
    })
  }

  /**
   * 批量渲染用户道路标签
   */
  renderUserRoadLabels(userRoadsGeoJson, targetGroup, options = {}) {
    if (!userRoadsGeoJson?.features || !targetGroup) {
      return
    }

    // 清除现有标签
    this.clearUserRoadLabels(targetGroup)

    // 渲染每条道路的标签
    userRoadsGeoJson.features.forEach(feature => {
      if (feature.geometry?.type === 'LineString' && feature.properties?.name) {
        const coordinates = feature.geometry.coordinates
        this.addUserRoadLabels(
          feature.properties.name,
          coordinates,
          feature,
          targetGroup,
          options
        )
      }
    })
  }

  /**
   * 清除所有用户道路标签
   */
  clearUserRoadLabels(targetGroup) {
    this.userRoadLabels.forEach((_labels, roadId) => {
      this.removeUserRoadLabels(roadId, targetGroup)
    })
    this.userRoadLabels.clear()
  }

  /**
   * 获取用户道路标签统计信息
   */
  getUserRoadLabelStats() {
    let totalLabels = 0
    let visibleLabels = 0

    this.userRoadLabels.forEach(labels => {
      totalLabels += labels.length
      labels.forEach(label => {
        if (label.visible) {
          visibleLabels++
        }
      })
    })

    return {
      totalRoads: this.userRoadLabels.size,
      totalLabels,
      visibleLabels,
    }
  }

  /**
   * 查找指定道路的标签
   */
  findLabelsByRoadName(roadName) {
    const matchingLabels = []
    this.userRoadLabels.forEach(labels => {
      labels.forEach(label => {
        if (label.userData?.featureName === roadName) {
          matchingLabels.push(label)
        }
      })
    })
    return matchingLabels
  }

  /**
   * 更新指定道路标签的样式
   */
  updateRoadLabelStyle(roadId, styleOptions) {
    const labels = this.userRoadLabels.get(roadId)
    if (!labels) {
      return
    }

    labels.forEach(label => {
      if (styleOptions.color !== undefined) {
        // 对于3D网格标签，需要重新创建纹理来改变颜色
        // 这里简化处理，只更新可见性
        console.warn('3D网格标签暂不支持动态颜色更改')
      }

      if (styleOptions.fontSize !== undefined) {
        // 对于3D网格标签，需要重新创建纹理来改变字体大小
        console.warn('3D网格标签暂不支持动态字体大小更改')
      }

      if (styleOptions.visible !== undefined) {
        label.visible = styleOptions.visible
      }
    })
  }

  /**
   * 更新用户道路标签缩放
   */
  updateUserRoadLabelsScale(targetGroup, camera) {
    if (!camera || !targetGroup || targetGroup.children.length === 0) return

    // 批量处理，减少函数调用开销
    const cameraPosition = camera.position

    targetGroup.children.forEach(label => {
      // 内联缩放计算，避免额外的函数调用
      const distance = cameraPosition.distanceTo(label.position)
      const baseDistance = 5
      const rawScale = distance / baseDistance
      const dynamicScale = Math.sqrt(rawScale) * 0.6
      const roadTypeScale = label.userData?.scaleMultiplier || 1.0
      const finalScale = dynamicScale * roadTypeScale

      label.scale.set(finalScale, finalScale, 1)
    })
  }

  /**
   * 清理资源
   */
  dispose() {
    this.clearUserRoadLabels()
    this.clearCache()
  }
}

export default UserRoadLabelManager
