/**
 * API服务实例
 * 提供基于axios的HTTP请求服务
 */
import axios from 'axios'
import { getApiBaseUrl, SERVER_CONFIG } from '../config/server.config'

// 创建axios实例 - 使用相对路径，让代理处理
const service = axios.create({
  baseURL: '/api', // 使用相对路径，通过代理访问API
  timeout: SERVER_CONFIG.timeout, // 使用配置的超时时间
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 以后可以在这里添加token等通用处理
    return config
  },
  error => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    const config = error.config || {}
    const errorMessage = error.response?.data?.message || error.message
    const status = error.response?.status
    const errorInfo = {
      url: config.url,
      method: config.method,
      status,
      message: errorMessage,
    }

    console.error('API请求最终失败:', errorInfo)
    return Promise.reject(error)
  }
)

export default service
