/**
 * 认证API模块
 * 处理用户登录、注销和获取用户信息等认证相关操作
 */
import { getToken } from '@s2/utils/auth';
import service from './service';

/**
 * 用户登录
 * @param {String} username 用户名
 * @param {String} password 密码
 * @returns {Promise} 登录结果，包含token
 */
export function login(username, password) {
	return service({
		url: '/auth/login',
		method: 'post',
		data: {
			username,
			password,
		},
	});
}

/**
 * 登出
 * @returns {Promise} 登出结果
 */
export function logout() {
	return service({
		url: '/auth/logout',
		method: 'post',
		headers: {
			Authorization: `Bearer ${getToken()}`,
		},
	});
}

/**
 * 获取用户信息
 * @returns {Promise} 用户信息
 */
export function getUserInfo() {
	return service({
		url: '/auth/userinfo',
		method: 'get',
		headers: {
			Authorization: `Bearer ${getToken()}`,
		},
	});
}

/**
 * 修改密码
 * @param {String} oldPassword 旧密码
 * @param {String} newPassword 新密码
 * @returns {Promise} 修改结果
 */
export function changePassword(oldPassword, newPassword) {
	return service({
		url: '/auth/password',
		method: 'put',
		headers: {
			Authorization: `Bearer ${getToken()}`,
		},
		data: {
			old_password: oldPassword,
			new_password: newPassword,
		},
	});
}

export default {
	login,
	logout,
	getUserInfo,
	changePassword,
};
