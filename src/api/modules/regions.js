/**
 * 区域(Regions) API模块
 * 参考路线API设计，提供区域数据的CRUD操作
 */
import service from '../service'

/**
 * 创建新区域
 * @param {Object} regionData - 区域数据
 * @param {string} regionData.region_name - 区域名称
 * @param {string} regionData.classification - 区域分类
 * @param {Object} regionData.region - 区域地理数据 (GeoJSON格式)
 * @returns {Promise} 创建结果
 */
export function createRegion(regionData) {
  return service.post('/regions', regionData)
}

/**
 * 获取所有区域
 * @returns {Promise} 区域列表
 */
export function getAllRegions() {
  return service.get('/regions')
}

/**
 * 获取单个区域
 * @param {string} regionId - 区域ID
 * @returns {Promise} 区域详情
 */
export function getRegion(regionId) {
  return service.get(`/regions/${regionId}`)
}

/**
 * 更新区域
 * @param {string} regionId - 区域ID
 * @param {Object} updateData - 更新数据
 * @param {string} [updateData.region_name] - 区域名称
 * @param {string} [updateData.classification] - 区域分类
 * @param {Object} [updateData.region] - 区域地理数据
 * @returns {Promise} 更新结果
 */
export function updateRegion(regionId, updateData) {
  return service.put(`/regions/${regionId}`, updateData)
}

/**
 * 删除区域
 * @param {string} regionId - 区域ID
 * @returns {Promise} 删除结果
 */
export function deleteRegion(regionId) {
  return service.delete(`/regions/${regionId}`)
}

/**
 * 批量创建区域
 * @param {Array} regionsData - 区域数据数组
 * @returns {Promise} 批量创建结果
 */
export function batchCreateRegions(regionsData) {
  const promises = regionsData.map(regionData => createRegion(regionData))
  return Promise.all(promises)
}

/**
 * 批量删除区域
 * @param {Array} regionIds - 区域ID数组
 * @returns {Promise} 批量删除结果
 */
export function batchDeleteRegions(regionIds) {
  const promises = regionIds.map(regionId => deleteRegion(regionId))
  return Promise.all(promises)
}

// 默认导出
export default {
  createRegion,
  getAllRegions,
  getRegion,
  updateRegion,
  deleteRegion,
  batchCreateRegions,
  batchDeleteRegions,
}
