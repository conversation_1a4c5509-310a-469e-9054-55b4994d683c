/**
 * 边缘设备API代理模块
 * 用于调用边缘设备上的API
 */
import service from '../service'

const edgeApiProxy = {
  /**
   * 调用边缘设备API
   * @param {String} edgeId 边缘设备ID
   * @param {String} path API路径
   * @param {String} method 请求方法，默认为get
   * @param {Object} data 请求参数
   * @param {Object} options 额外选项
   * @returns {Promise} 请求结果
   */
  callEdgeApi(edgeId, path, method = 'get', data = null, options = {}) {
    // 使用/api-proxy/前缀的路径格式，与接口文档保持一致
    const url = `/api-proxy/${edgeId}/${path}`

    // 使用service实例，它的拦截器会处理路径转换、方法转换和错误处理
    return service.request({
      url,
      method,
      data: method.toLowerCase() !== 'get' ? data : null,
      params: method.toLowerCase() === 'get' ? data : null,
      // 设置超时时间，边缘设备可能响应慢
      timeout: options.timeout || 30000, // 减少默认超时时间
      // 简化重试配置
      retryConfig: options.retries
        ? {
            retries: Math.min(options.retries, 2), // 最多重试2次
            retryDelay: 1000, // 固定1秒延迟
          }
        : undefined,
    })
  },

  /**
   * 调用边缘设备GET API
   * @param {String} edgeId 边缘设备ID
   * @param {String} path API路径
   * @param {Object} params 请求参数
   * @param {Object} options 额外选项
   * @returns {Promise} 请求结果
   */
  get(edgeId, path, params = null, options = {}) {
    return this.callEdgeApi(edgeId, path, 'get', params, options)
  },

  /**
   * 调用边缘设备POST API
   * @param {String} edgeId 边缘设备ID
   * @param {String} path API路径
   * @param {Object} data 请求体数据
   * @param {Object} options 额外选项
   * @returns {Promise} 请求结果
   */
  post(edgeId, path, data = null, options = {}) {
    return this.callEdgeApi(edgeId, path, 'post', data, options)
  },

  /**
   * 调用边缘设备PUT API
   * @param {String} edgeId 边缘设备ID
   * @param {String} path API路径
   * @param {Object} data 请求体数据
   * @param {Object} options 额外选项
   * @returns {Promise} 请求结果
   */
  put(edgeId, path, data = null, options = {}) {
    return this.callEdgeApi(edgeId, path, 'put', data, options)
  },

  /**
   * 调用边缘设备DELETE API
   * @param {String} edgeId 边缘设备ID
   * @param {String} path API路径
   * @param {Object} data 请求体数据
   * @param {Object} options 额外选项
   * @returns {Promise} 请求结果
   */
  delete(edgeId, path, data = null, options = {}) {
    return this.callEdgeApi(edgeId, path, 'delete', data, options)
  },
}

export default edgeApiProxy
