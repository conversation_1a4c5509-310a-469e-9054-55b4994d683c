/**
 * 交通指标API模块
 * 基于真实API数据结构设计
 */
import service from '../service'

const trafficMetricsApi = {
  /**
   * 获取设备交通指标
   * @returns {Promise} 设备交通指标数据
   */
  getDeviceMetrics() {
    return service.get('/dashboard/metrics/single-points')
  },

  /**
   * 获取干线交通指标
   * @returns {Promise} 干线交通指标数据
   */
  getCorridorMetrics() {
    return service.get('/dashboard/metrics/corridors')
  },

  /**
   * 获取区域交通指标
   * @returns {Promise} 区域交通指标数据
   */
  getAreaMetrics() {
    return service.get('/dashboard/metrics/areas')
  },
}

export default trafficMetricsApi
