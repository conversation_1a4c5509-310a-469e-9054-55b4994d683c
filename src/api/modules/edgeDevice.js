/**
 * 边缘设备API模块
 */
import service from '../service'
import edgeApiProxy from './edgeApiProxy'

/**
 * 边缘设备API模块
 */
const edgeDeviceApi = {
  /**
   * 获取所有边缘设备列表
   * @returns {Promise} 设备列表
   */
  getAllEdges() {
    return service.request({
      url: '/edges',
      method: 'get',
    })
  },

  /**
   * 获取指定边缘设备
   * @param {String} edgeId 设备ID
   * @returns {Promise} 设备详情
   */
  getEdge(edgeId) {
    return service.request({
      url: `/edges/${edgeId}`,
      method: 'get',
    })
  },

  /**
   * 添加新的边缘设备
   * @param {Object} edgeData 设备数据
   * @returns {Promise} 添加结果
   */
  addEdge(edgeData) {
    return service.request({
      url: '/edges',
      method: 'post',
      data: edgeData,
    })
  },

  /**
   * 更新边缘设备
   * @param {Object} edgeData 设备数据
   * @returns {Promise} 更新结果
   */
  updateEdge(edgeData) {
    return service.request({
      url: `/edges/${edgeData.edge_id}`,
      method: 'put',
      data: edgeData,
    })
  },

  /**
   * 删除边缘设备
   * @param {String} edgeId 设备ID
   * @returns {Promise} 删除结果
   */
  deleteEdge(edgeId) {
    return service.request({
      url: `/edges/${edgeId}`,
      method: 'delete',
    })
  },

  /**
   * 简化版预加载边缘设备配置数据
   * @returns {Promise<Object>} 预加载结果，包含成功和失败的设备ID
   */
  async preloadEdgeConfigs() {
    try {
      // 获取所有设备
      const edges = await this.getAllEdges()
      if (!Array.isArray(edges) || edges.length === 0) {
        return { success: [], failed: [], total: 0 }
      }

      console.info(`开始预加载 ${edges.length} 个设备的配置...`)
      const result = { success: [], failed: [], total: edges.length }

      // 简化为顺序处理，避免过度并发
      for (const edge of edges) {
        try {
          await edgeApiProxy.post(
            edge.edge_id,
            'coreapi/download_config',
            { id: '1' },
            { timeout: 10000 }
          )
          result.success.push(edge.edge_id)
        } catch (error) {
          console.warn(`预加载设备[${edge.edge_id}]失败:`, error.message)
          result.failed.push(edge.edge_id)
        }
      }

      console.info(
        `预加载完成: 成功 ${result.success.length}，失败 ${result.failed.length}`
      )
      return result
    } catch (error) {
      console.error('预加载设备配置失败:', error)
      throw error
    }
  },

  /**
   * 获取指定设备的完整配置并存入localStorage
   * @param {String} edgeId 设备ID
   * @returns {Promise<Object>} 设备配置数据
   */
  async fetchAndStoreDeviceConfig(edgeId) {
    if (!edgeId) {
      return Promise.reject(new Error('获取设备配置需要提供有效的设备ID'))
    }
    try {
      console.log(`正在为设备 [${edgeId}] 获取最新配置...`)
      const configData = await edgeApiProxy.post(
        edgeId,
        'coreapi/download_config',
        { id: '1' }, // 根据接口要求传递参数
        {
          timeout: 15000, // 为单个重要请求设置更长的超时时间
        }
      )

      // 将获取到的配置存入localStorage，使用sigopt-box的键名
      localStorage.setItem('FULL_CONFIG', JSON.stringify(configData))
      console.log(`设备 [${edgeId}] 的配置已成功获取并存入LocalStorage。`)

      return configData
    } catch (error) {
      console.error(`为设备 [${edgeId}] 获取或存储配置失败:`, error)
      // 即使失败，也清理一下旧的缓存，避免脏数据
      localStorage.removeItem('FULL_CONFIG')
      throw error // 将错误继续向上抛出，以便调用方可以处理
    }
  },

  /**
   * 获取指定设备的UTC状态（控制模式等详细信息）
   * @param {String} edgeId 设备ID
   * @returns {Promise<Object>} UTC状态数据
   */
  getUtcState(edgeId) {
    // 采用POST方法，body传空对象
    return edgeApiProxy.post(edgeId, 'coreapi/utc_state', {})
  },
}

export default edgeDeviceApi
