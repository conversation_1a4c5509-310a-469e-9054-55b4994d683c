/**
 * 看板数据API模块
 * 用于获取系统仪表盘数据，直接调用API无缓存
 */
import service from '../service'

const dashboardApi = {
  /**
   * 获取边缘设备状态统计
   * @returns {Promise} 边缘设备状态统计数据
   */
  getEdgesStatus() {
    return service.get('/dashboard/edges-status')
  },

  /**
   * 获取检测器状态统计
   * @returns {Promise} 检测器状态统计数据
   */
  getDetectorsStatus() {
    return service.get('/dashboard/detectors-status')
  },

  /**
   * 获取控制模式统计
   * @returns {Promise} 控制模式统计数据
   */
  getControlModesStatus() {
    return service.get('/dashboard/control-modes-status')
  },

  /**
   * 获取看板综合数据
   * @returns {Promise} 看板综合数据
   */
  getDashboardSummary() {
    return service.get('/dashboard/summary')
  },

  /**
   * 获取边缘设备告警统计
   * @returns {Promise} 边缘设备告警统计数据
   */
  getEdgesAlarms() {
    return service.get('/dashboard/edges-alarms')
  },
}

export default dashboardApi
