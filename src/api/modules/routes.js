/**
 * 路线(干线) API模块
 * 精简版：移除不必要的数据转换
 */
import service from '../service'

/**
 * 创建新路线
 * @param {Object} routeData - 路线数据
 * @param {string} routeData.route_name - 路线名称
 * @param {string} routeData.classification - 路线分类
 * @param {Object} routeData.route - 路线地理数据 (GeoJSON格式)
 * @returns {Promise} 创建结果
 */
export function createRoute(routeData) {
  return service.post('/routes', routeData)
}

/**
 * 获取所有路线
 * @returns {Promise} 路线列表
 */
export function getAllRoutes() {
  return service.get('/routes')
}

/**
 * 获取单个路线
 * @param {string} routeId - 路线ID
 * @returns {Promise} 路线详情
 */
export function getRoute(routeId) {
  return service.get(`/routes/${routeId}`)
}

/**
 * 更新路线
 * @param {string} routeId - 路线ID
 * @param {Object} updateData - 更新数据
 * @param {string} [updateData.route_name] - 路线名称
 * @param {string} [updateData.classification] - 路线分类
 * @param {Object} [updateData.route] - 路线地理数据
 * @returns {Promise} 更新结果
 */
export function updateRoute(routeId, updateData) {
  return service.put(`/routes/${routeId}`, updateData)
}

/**
 * 删除路线
 * @param {string} routeId - 路线ID
 * @returns {Promise} 删除结果
 */
export function deleteRoute(routeId) {
  return service.delete(`/routes/${routeId}`)
}

/**
 * 批量创建路线
 * @param {Array} routesData - 路线数据数组
 * @returns {Promise} 批量创建结果
 */
export function batchCreateRoutes(routesData) {
  const promises = routesData.map(routeData => createRoute(routeData))
  return Promise.all(promises)
}

/**
 * 批量删除路线
 * @param {Array} routeIds - 路线ID数组
 * @returns {Promise} 批量删除结果
 */
export function batchDeleteRoutes(routeIds) {
  const promises = routeIds.map(routeId => deleteRoute(routeId))
  return Promise.all(promises)
}

// 默认导出
export default {
  createRoute,
  getAllRoutes,
  getRoute,
  updateRoute,
  deleteRoute,
  batchCreateRoutes,
  batchDeleteRoutes,
}
