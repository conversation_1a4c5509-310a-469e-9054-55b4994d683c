/**
 * 图标管理API模块
 */
import service from '../service';

const iconsApi = {
	/**
	 * 创建新图标
	 * @param {Object} iconData 图标数据
	 * @returns {Promise} 创建结果
	 */
	createIcon(iconData) {
		return service.request({
			url: '/icons',
			method: 'post',
			data: iconData,
		});
	},

	/**
	 * 获取所有图标列表
	 * @returns {Promise} 图标列表
	 */
	getIcons() {
		return service.request({
			url: '/icons',
			method: 'get',
		});
	},

	/**
	 * 获取单个图标
	 * @param {String} iconId 图标ID
	 * @returns {Promise} 图标详情
	 */
	getIcon(iconId) {
		return service.request({
			url: `/icons/${iconId}`,
			method: 'get',
		});
	},

	/**
	 * 更新图标
	 * @param {String} iconId 图标ID
	 * @param {Object} iconData 图标数据
	 * @returns {Promise} 更新结果
	 */
	updateIcon(iconId, iconData) {
		return service.request({
			url: `/icons/${iconId}`,
			method: 'put',
			data: iconData,
		});
	},

	/**
	 * 删除图标
	 * @param {String} iconId 图标ID
	 * @returns {Promise} 删除结果
	 */
	deleteIcon(iconId) {
		return service.request({
			url: `/icons/${iconId}`,
			method: 'delete',
		});
	},

	/**
	 * 根据分类获取图标
	 * @param {String} classification 分类编码
	 * @returns {Promise} 图标列表
	 */
	async getIconByClassification(classification) {
		try {
			// 获取所有图标
			const icons = await this.getIcons();

			// 过滤出指定分类的图标
			const filteredIcons = icons.filter((icon) => icon.classification === classification);

			// 如果有匹配的图标，返回第一个
			if (filteredIcons.length > 0) {
				return filteredIcons[0];
			}

			// 没有匹配的图标，返回null
			return null;
		} catch (error) {
			console.error('获取分类图标失败:', error);
			return null;
		}
	},
};

export default iconsApi;
