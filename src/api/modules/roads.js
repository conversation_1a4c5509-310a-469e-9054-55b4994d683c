/**
 * 道路 API模块
 * 基于路线API设计，提供道路数据的CRUD操作
 */
import service from '../service'

/**
 * 创建新道路
 * @param {Object} roadData - 道路数据
 * @param {string} roadData.road_name - 道路名称
 * @param {string} roadData.road_level - 道路等级
 * @param {Object} roadData.road - 道路地理数据 (GeoJSON格式)
 * @returns {Promise} 创建结果
 */
export function createRoad(roadData) {
  return service.post('/roads', roadData)
}

/**
 * 获取所有道路
 * @returns {Promise} 道路列表
 */
export function getAllRoads() {
  return service.get('/roads')
}

/**
 * 获取单个道路
 * @param {string} roadId - 道路ID
 * @returns {Promise} 道路详情
 */
export function getRoad(roadId) {
  return service.get(`/roads/${roadId}`)
}

/**
 * 更新道路
 * @param {string} roadId - 道路ID
 * @param {Object} updateData - 更新数据
 * @param {string} [updateData.road_name] - 道路名称
 * @param {string} [updateData.road_level] - 道路等级
 * @param {Object} [updateData.road] - 道路地理数据
 * @returns {Promise} 更新结果
 */
export function updateRoad(roadId, updateData) {
  return service.put(`/roads/${roadId}`, updateData)
}

/**
 * 删除道路
 * @param {string} roadId - 道路ID
 * @returns {Promise} 删除结果
 */
export function deleteRoad(roadId) {
  return service.delete(`/roads/${roadId}`)
}

/**
 * 批量创建道路
 * @param {Array} roadsData - 道路数据数组
 * @returns {Promise} 批量创建结果
 */
export function batchCreateRoads(roadsData) {
  const promises = roadsData.map(roadData => createRoad(roadData))
  return Promise.all(promises)
}

/**
 * 批量删除道路
 * @param {Array} roadIds - 道路ID数组
 * @returns {Promise} 批量删除结果
 */
export function batchDeleteRoads(roadIds) {
  const promises = roadIds.map(roadId => deleteRoad(roadId))
  return Promise.all(promises)
}

// 默认导出
export default {
  createRoad,
  getAllRoads,
  getRoad,
  updateRoad,
  deleteRoad,
  batchCreateRoads,
  batchDeleteRoads,
}
