/**
 * 渠化图配置API模块
 * 通过边缘API代理访问渠化图配置
 */
import edgeApiProxy from './edgeApiProxy';

/**
 * 获取渠化图配置
 * @param {String} edgeId 边缘设备ID
 * @param {String} configId 配置ID
 * @returns {Promise} 配置数据
 */
export function downloadChannelization(edgeId, configId = '1') {
	// 使用拦截器会自动处理路径转换和方法转换
	return edgeApiProxy.post(
		edgeId,
		'sysapi/download_channelization', // 直接使用正确的路径
		{ id: configId },
		{
			retries: 3, // 重试次数
			timeout: 10000, // 超时设置
		}
	);
}

/**
 * 上传渠化图配置
 * @param {String} edgeId 边缘设备ID
 * @param {Object} configData 配置数据
 * @returns {Promise} 上传结果
 */
export function uploadChannelization(edgeId, configData) {
	return edgeApiProxy.post(edgeId, 'sysapi/upload_channelization', configData);
}

export default {
	downloadChannelization,
	uploadChannelization,
};
