import edgeDeviceApi from './modules/edgeDevice'
import edgeApiProxy from './modules/edgeApiProxy'
import dashboardApi from './modules/dashboard'
import trafficMetricsApi from './modules/trafficMetrics'
import service from './service'
import * as channelizationApi from './modules/channelization'
import * as routesApi from './modules/routes'
import * as regionsApi from './modules/regions'
import * as roadsApi from './modules/roads'

// 整合API
const api = {
  // 从模块导入
  edge: edgeDeviceApi,
  edgeProxy: {
    ...edgeApiProxy, // 展开所有方法 (get, post, put, delete, etc.)
    call: edgeApiProxy.callEdgeApi,
    // 直接使用拦截器处理路径和方法转换
    downloadConfig(edgeId) {
      // 即使使用coreapi路径，拦截器也会自动转换为正确的sysapi路径
      return edgeApiProxy.post(edgeId, 'sysapi/download_channelization')
    },
    saveConfig(edgeId) {
      // 统一使用sysapi路径
      return edgeApiProxy.post(edgeId, 'sysapi/save_config')
    },
  },
  dashboard: dashboardApi,
  trafficMetrics: trafficMetricsApi,
  channelization: channelizationApi,
  routes: routesApi,
  regions: regionsApi,
  roads: roadsApi,
}

// 为兼容旧代码，保留原有的导出方式
export {
  edgeDeviceApi,
  edgeApiProxy,
  dashboardApi,
  trafficMetricsApi,
  channelizationApi,
  routesApi,
  regionsApi,
  roadsApi,
}

// 导出service实例，供模块使用
export { service }

// 导出API模块
export * from './auth'
export * from './modules/edgeDevice'
export * from './modules/dashboard'
export * from './modules/trafficMetrics'
export * from './modules/channelization'
export * from './modules/routes'
export * from './modules/regions'
export * from './modules/roads'

// 默认导出整个API对象
export default api
