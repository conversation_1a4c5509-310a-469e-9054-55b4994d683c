/**
 * 相机距离监听组合式函数
 * 用于跟踪相机位置变化并计算到目标的距离
 */

import { ref, onMounted, onUnmounted } from 'vue'
import * as THREE from 'three'

/**
 * 使用相机距离监听
 * @param {Object} options - 配置选项
 * @returns {Object} 相机距离相关的响应式数据和方法
 */
export function useCameraDistance(options = {}) {
  const {
    target = new THREE.Vector3(0, 0, 0), // 目标位置
    updateInterval = 100, // 更新间隔（毫秒）
    onDistanceChange = null, // 距离变化回调
  } = options

  const cameraDistance = ref(100)
  const cameraPosition = ref({ x: 0, y: 0, z: 0 })
  const isTracking = ref(false)

  let camera = null
  let updateTimer = null
  let lastDistance = 0

  /**
   * 开始跟踪相机距离
   * @param {THREE.Camera} cameraInstance - Three.js 相机实例
   */
  const startTracking = cameraInstance => {
    if (!cameraInstance) {
      console.warn('相机实例无效，无法开始跟踪')
      return
    }

    camera = cameraInstance
    isTracking.value = true

    // 立即更新一次
    updateDistance()

    // 设置定时更新
    updateTimer = setInterval(updateDistance, updateInterval)

    console.log('开始跟踪相机距离')
  }

  /**
   * 停止跟踪相机距离
   */
  const stopTracking = () => {
    if (updateTimer) {
      clearInterval(updateTimer)
      updateTimer = null
    }

    isTracking.value = false
    camera = null

    console.log('停止跟踪相机距离')
  }

  /**
   * 更新距离计算
   */
  const updateDistance = () => {
    if (!camera) return

    // 获取相机位置
    const position = camera.position
    cameraPosition.value = {
      x: position.x,
      y: position.y,
      z: position.z,
    }

    // 计算到目标的距离
    const distance = position.distanceTo(target)
    cameraDistance.value = distance

    // 如果距离变化超过阈值，触发回调
    const distanceThreshold = 1 // 距离变化阈值
    if (Math.abs(distance - lastDistance) > distanceThreshold) {
      lastDistance = distance

      if (onDistanceChange) {
        onDistanceChange(distance, cameraPosition.value)
      }
    }
  }

  /**
   * 手动更新距离（用于外部触发）
   */
  const forceUpdate = () => {
    updateDistance()
  }

  /**
   * 设置新的目标位置
   * @param {THREE.Vector3|Array} newTarget - 新的目标位置
   */
  const setTarget = newTarget => {
    if (Array.isArray(newTarget)) {
      target.set(newTarget[0], newTarget[1], newTarget[2])
    } else if (newTarget instanceof THREE.Vector3) {
      target.copy(newTarget)
    } else {
      console.warn('无效的目标位置格式')
    }
  }

  /**
   * 获取当前距离信息
   * @returns {Object} 距离信息
   */
  const getDistanceInfo = () => {
    return {
      distance: cameraDistance.value,
      position: cameraPosition.value,
      target: {
        x: target.x,
        y: target.y,
        z: target.z,
      },
      isTracking: isTracking.value,
    }
  }

  // 组件卸载时清理
  onUnmounted(() => {
    stopTracking()
  })

  return {
    // 响应式数据
    cameraDistance,
    cameraPosition,
    isTracking,

    // 方法
    startTracking,
    stopTracking,
    forceUpdate,
    setTarget,
    getDistanceInfo,
  }
}

/**
 * 创建相机距离监听器的便捷函数
 * @param {Function} onDistanceChange - 距离变化回调
 * @param {Object} options - 其他选项
 * @returns {Object} 相机距离监听器
 */
export function createCameraDistanceTracker(onDistanceChange, options = {}) {
  return useCameraDistance({
    ...options,
    onDistanceChange,
  })
}
