import { ref } from 'vue'
import { useDevicesStore } from '@/stores/devices'
import { message } from 'ant-design-vue'
import edgeDeviceApi from '@/api/modules/edgeDevice'
import * as THREE from 'three'

/**
 * 设备交互相关的 Composable
 * 处理设备悬停、点击、监控等交互功能
 */
export function useDeviceInteraction() {
  const devicesStore = useDevicesStore()

  // 设备悬停相关状态
  const hoveredDeviceInfo = ref(null)
  const hoveredDevice3D = ref(null) // 存储3D设备对象用于定位
  let utcStateTimer = null
  let hideTooltipTimer = null
  let hasShownDeviceLoadingTip = false

  // 设备选择相关状态
  const selectedDeviceWorldPosition = ref(null)

  // 模态框状态
  const isSettingsModalVisible = ref(false)
  const selectedSettingsDevice = ref(null)
  const isMonitorModalVisible = ref(false)
  const selectedMonitorDevice = ref(null)
  const monitorType = ref('intersection') // 可选值: 'running', 'intersection', 'device'
  const isDeviceConfigVisible = ref(false)

  // 统一的UTC状态更新函数
  const updateUtcState = async () => {
    if (!hoveredDeviceInfo.value?.id) {
      return
    }

    try {
      const deviceId = hoveredDeviceInfo.value.id
      const utcStateResp = await edgeDeviceApi.getUtcState(deviceId)
      const utcState = utcStateResp?.data || null

      // 检查设备信息是否仍然存在
      if (hoveredDeviceInfo.value?.id === deviceId) {
        hoveredDeviceInfo.value = {
          ...hoveredDeviceInfo.value,
          utcState,
        }
      }
    } catch (error) {
      console.warn('获取UTC状态失败:', error)
    }
  }

  // 启动UTC状态定时器
  const startUtcTimer = () => {
    if (utcStateTimer) {
      return // 避免重复启动
    }

    updateUtcState() // 立即请求一次
    utcStateTimer = setInterval(updateUtcState, 1000)
  }

  // 停止UTC状态定时器
  const stopUtcTimer = () => {
    if (utcStateTimer) {
      clearInterval(utcStateTimer)
      utcStateTimer = null
    }
  }

  // 处理设备悬停事件
  const handleDeviceHover = eventData => {
    // 设备数据未加载时，提示用户并跳过后续逻辑
    if (!devicesStore.devices || devicesStore.devices.length === 0) {
      if (!hasShownDeviceLoadingTip) {
        message.info('设备数据正在加载，请稍后再试')
        hasShownDeviceLoadingTip = true
      }
      return
    }
    hasShownDeviceLoadingTip = false

    // 获取设备数据和3D对象
    const deviceData = eventData.device
    const device3D = eventData.object3D

    // 存储3D设备对象用于定位计算
    hoveredDevice3D.value = device3D

    // 获取设备信息
    const properties = deviceData.properties || {}
    const deviceId = properties.edge_id
    if (!deviceId) {
      return
    }

    // 如果是同一个设备且有延迟隐藏定时器，取消隐藏
    if (hoveredDeviceInfo.value?.id === deviceId && hideTooltipTimer) {
      clearTimeout(hideTooltipTimer)
      hideTooltipTimer = null
      return
    }

    // 设置提示框信息
    hoveredDeviceInfo.value = {
      id: deviceId,
      name: properties.edge_name,
      ip: properties.ip_address,
      jnc_name: properties.jnc_name,
      device:
        devicesStore.devices.find(d => d.edge_id === deviceId) || deviceData,
      utcState: null,
    }

    // 延迟启动UTC定时器，避免快速移动时的频繁调用
    setTimeout(() => {
      if (hoveredDeviceInfo.value?.id === deviceId) {
        startUtcTimer()
      }
    }, 500)
  }

  // 鼠标离开设备时的处理 - 使用延迟隐藏以支持固定显示功能
  const handleDeviceHoverEnd = _eventData => {
    // 清除之前的定时器
    if (hideTooltipTimer) {
      clearTimeout(hideTooltipTimer)
      hideTooltipTimer = null
    }

    // 启动延迟隐藏定时器，给用户时间将鼠标移到提示框上
    hideTooltipTimer = setTimeout(() => {
      if (hoveredDeviceInfo.value) {
        stopUtcTimer()
        hoveredDeviceInfo.value = null
        hoveredDevice3D.value = null
      }
      hideTooltipTimer = null
    }, 300) // 300ms延迟，与统一自动隐藏系统保持一致
  }

  // 鼠标进入提示框 - 取消延迟隐藏并保持UTC请求
  const handleTooltipMouseEnter = () => {
    // 取消延迟隐藏定时器
    if (hideTooltipTimer) {
      clearTimeout(hideTooltipTimer)
      hideTooltipTimer = null
    }

    // 确保UTC定时器运行
    if (!utcStateTimer && hoveredDeviceInfo.value?.id) {
      startUtcTimer()
    }
  }

  // 鼠标离开提示框 - 启动延迟隐藏
  const handleTooltipMouseLeave = () => {
    // 启动延迟隐藏定时器，与设备悬停结束逻辑保持一致
    hideTooltipTimer = setTimeout(() => {
      if (hoveredDeviceInfo.value) {
        stopUtcTimer()
        hoveredDeviceInfo.value = null
        hoveredDevice3D.value = null
      }
      hideTooltipTimer = null
    }, 300)
  }

  // 处理设备左键点击事件
  const handleDeviceLeftClick = eventData => {
    // 获取设备数据
    const deviceData = eventData.device
    const deviceObject = eventData.object3D

    // 保存设备世界坐标
    const worldPosition = new THREE.Vector3()
    const box = new THREE.Box3().setFromObject(deviceObject)
    box.getCenter(worldPosition)
    selectedDeviceWorldPosition.value = worldPosition.clone()

    // 查找完整设备信息
    const properties = deviceData.properties || {}
    const deviceId = properties.edge_id
    let fullDevice = null
    if (deviceId) {
      fullDevice = devicesStore.devices.find(d => d.edge_id === deviceId)
    }

    if (fullDevice) {
      // 设置监控类型为路口监控（默认）
      monitorType.value = 'intersection'
      // 调用监控准备函数
      prepareAndShowMonitor(fullDevice, '路口监控')
    }
  }

  // 处理设备右键点击事件
  const handleDeviceRightClick = eventData => {
    // 获取设备数据
    const deviceData = eventData.device
    const deviceId = deviceData.properties?.id

    if (deviceId) {
      // 从store查找完整设备数据
      const device = devicesStore.devices.find(d => d.edge_id === deviceId)
      if (device) {
        // 直接打开设置模态框
        selectedSettingsDevice.value = device
        isSettingsModalVisible.value = true
      }
    }
  }

  // 准备并显示监控模态框的通用函数
  const prepareAndShowMonitor = async (device, _monitorName) => {
    if (device) {
      // 有设备信息，直接使用传入的设备
      selectedMonitorDevice.value = device
    } else if (
      !selectedMonitorDevice.value &&
      devicesStore.devices.length > 0
    ) {
      // 没有选择设备且没有已选设备，使用第一个可用设备
      selectedMonitorDevice.value = devicesStore.devices[0]
    }

    if (selectedMonitorDevice.value) {
      try {
        // 在打开监控前，先获取并存储最新的设备配置
        await edgeDeviceApi.fetchAndStoreDeviceConfig(
          selectedMonitorDevice.value.edge_id
        )

        isMonitorModalVisible.value = true
      } catch (error) {
        message.error(`打开设备监控失败: ${error.message || '未知错误'}`)
      }
    } else {
      message.warning('没有可用设备')
    }
  }

  // 关闭提示框的通用方法
  const closeTooltip = () => {
    // 清除延迟隐藏定时器
    if (hideTooltipTimer) {
      clearTimeout(hideTooltipTimer)
      hideTooltipTimer = null
    }

    stopUtcTimer()
    hoveredDeviceInfo.value = null
    hoveredDevice3D.value = null
  }

  // 处理路口监控按钮点击
  const handleIntersectionMonitor = deviceInfo => {
    if (!deviceInfo || !deviceInfo.device) {
      return
    }

    // 设置监控类型为路口监控（实际上所有类型都使用相同配置）
    monitorType.value = 'intersection'
    prepareAndShowMonitor(deviceInfo.device, monitorType.value)

    // 关闭提示框
    closeTooltip()
  }

  // 处理设备监控按钮点击
  const handleDeviceMonitor = deviceInfo => {
    if (!deviceInfo || !deviceInfo.device) {
      return
    }

    // 设置监控类型为设备监控（实际上所有类型都使用相同配置）
    monitorType.value = 'device'
    prepareAndShowMonitor(deviceInfo.device, monitorType.value)

    // 关闭提示框
    closeTooltip()
  }

  // 处理运行监视按钮点击
  const handleRunningMonitor = deviceInfo => {
    if (!deviceInfo || !deviceInfo.device) {
      return
    }

    // 设置监控类型为运行监视（实际上所有类型都使用相同配置）
    monitorType.value = 'running'
    prepareAndShowMonitor(deviceInfo.device, monitorType.value)

    // 关闭提示框
    closeTooltip()
  }

  // 处理设备监控中心模态框关闭事件
  const handleCloseDeviceMonitor = () => {
    isDeviceConfigVisible.value = false
  }

  // 处理设置更新事件
  const handleSettingsUpdate = () => {
    // 可以在设备配置更新后进行一些操作，比如重新获取设备列表
    devicesStore.fetchDevices()
  }

  // 清理函数
  const cleanup = () => {
    stopUtcTimer()
    if (hideTooltipTimer) {
      clearTimeout(hideTooltipTimer)
      hideTooltipTimer = null
    }
    hoveredDeviceInfo.value = null
    hoveredDevice3D.value = null
  }

  return {
    // 状态
    hoveredDeviceInfo,
    hoveredDevice3D,
    selectedDeviceWorldPosition,
    isSettingsModalVisible,
    selectedSettingsDevice,
    isMonitorModalVisible,
    selectedMonitorDevice,
    monitorType,
    isDeviceConfigVisible,

    // 方法
    handleDeviceHover,
    handleDeviceHoverEnd,
    handleTooltipMouseEnter,
    handleTooltipMouseLeave,
    handleDeviceLeftClick,
    handleDeviceRightClick,
    handleIntersectionMonitor,
    handleDeviceMonitor,
    handleRunningMonitor,
    handleCloseDeviceMonitor,
    handleSettingsUpdate,
    prepareAndShowMonitor,
    cleanup,
  }
}
