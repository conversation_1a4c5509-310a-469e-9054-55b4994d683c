import { ref, computed } from 'vue'
import * as THREE from 'three'

/**
 * 统一场景交互组合式函数
 * 合并 CityModel.vue、DeviceInteractionHandler.vue、TrunkLineInteractionHandler.vue 的交互逻辑
 * 提供统一的鼠标事件处理、射线检测、对象选择功能
 */
export function useSceneInteraction(sceneManager) {
  // 鼠标位置
  const mouse = ref(new THREE.Vector2())

  // 射线检测器
  const raycaster = ref(new THREE.Raycaster())

  // 当前悬停的对象
  const hoveredObject = ref(null)

  // 选中的对象列表
  const selectedObjects = ref([])

  /**
   * 更新鼠标位置
   * @param {MouseEvent} event - 鼠标事件
   */
  const updateMousePosition = event => {
    if (!sceneManager) return false

    const container = sceneManager.getContainer()
    if (!container) return false

    const rect = container.getBoundingClientRect()
    mouse.value.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
    mouse.value.y = -((event.clientY - rect.top) / rect.height) * 2 + 1

    return true
  }

  /**
   * 执行射线检测
   * @param {Array} targets - 检测目标对象数组
   * @param {boolean} recursive - 是否递归检测子对象
   * @returns {Array} 交点数组
   */
  const performRaycast = (targets, recursive = true) => {
    if (!sceneManager) return []

    const camera = sceneManager.getCamera()
    if (!camera) return []

    raycaster.value.setFromCamera(mouse.value, camera)
    return raycaster.value.intersectObjects(targets, recursive)
  }

  /**
   * 检测特定类型的对象
   * @param {MouseEvent} event - 鼠标事件
   * @param {Object} options - 检测选项
   * @returns {Object} 检测结果
   */
  const detectObjects = (event, options = {}) => {
    if (!updateMousePosition(event)) {
      return { intersects: [], types: {} }
    }

    const results = { intersects: [], types: {} }

    // 检测路线对象
    if (options.roads && options.roadGroups) {
      const roadIntersects = []
      options.roadGroups.forEach(group => {
        if (group && group.children.length > 0) {
          const intersects = performRaycast(group.children, true)
          intersects.forEach(intersect => {
            if (
              intersect.object.userData &&
              (intersect.object.userData.featureId ||
                intersect.object.userData.featureName)
            ) {
              roadIntersects.push({ ...intersect, type: 'road' })
            }
          })
        }
      })
      results.types.roads = roadIntersects
      results.intersects.push(...roadIntersects)
    }

    // 检测POI对象
    if (options.pois && options.poiGroups) {
      const poiIntersects = []
      options.poiGroups.forEach(group => {
        if (group && group.children.length > 0) {
          const intersects = performRaycast(group.children, true)
          intersects.forEach(intersect => {
            if (
              intersect.object.userData &&
              intersect.object.userData.feature
            ) {
              poiIntersects.push({ ...intersect, type: 'poi' })
            }
          })
        }
      })
      results.types.pois = poiIntersects
      results.intersects.push(...poiIntersects)
    }

    // 检测设备对象
    if (options.devices && options.deviceGroups) {
      const deviceIntersects = []
      options.deviceGroups.forEach(group => {
        if (group && group.children.length > 0) {
          const intersects = performRaycast(group.children, true)
          intersects.forEach(intersect => {
            if (
              intersect.object.userData &&
              intersect.object.userData.deviceData
            ) {
              deviceIntersects.push({ ...intersect, type: 'device' })
            }
          })
        }
      })
      results.types.devices = deviceIntersects
      results.intersects.push(...deviceIntersects)
    }

    // 按距离排序
    results.intersects.sort((a, b) => a.distance - b.distance)

    return results
  }

  /**
   * 处理对象悬停
   * @param {THREE.Object3D} object - 悬停的对象
   */
  const handleObjectHover = object => {
    if (hoveredObject.value === object) return

    // 清除之前的悬停状态
    if (hoveredObject.value) {
      clearObjectHover()
    }

    hoveredObject.value = object

    // 应用悬停效果
    if (object && object.userData) {
      object.userData.isHovered = true
    }
  }

  /**
   * 清除对象悬停状态
   */
  const clearObjectHover = () => {
    if (hoveredObject.value && hoveredObject.value.userData) {
      hoveredObject.value.userData.isHovered = false
    }
    hoveredObject.value = null
  }

  /**
   * 选择对象
   * @param {THREE.Object3D} object - 要选择的对象
   */
  const selectObject = object => {
    if (!object) return false

    const index = selectedObjects.value.indexOf(object)
    if (index === -1) {
      selectedObjects.value.push(object)
      if (object.userData) {
        object.userData.isSelected = true
      }
      return true
    }
    return false
  }

  /**
   * 取消选择对象
   * @param {THREE.Object3D} object - 要取消选择的对象
   */
  const deselectObject = object => {
    if (!object) return false

    const index = selectedObjects.value.indexOf(object)
    if (index > -1) {
      selectedObjects.value.splice(index, 1)
      if (object.userData) {
        object.userData.isSelected = false
      }
      return true
    }
    return false
  }

  /**
   * 切换对象选择状态
   * @param {THREE.Object3D} object - 要切换的对象
   */
  const toggleObjectSelection = object => {
    if (!object) return false

    const index = selectedObjects.value.indexOf(object)
    if (index > -1) {
      return deselectObject(object)
    } else {
      return selectObject(object)
    }
  }

  /**
   * 清除所有选择
   */
  const clearAllSelections = () => {
    selectedObjects.value.forEach(object => {
      if (object.userData) {
        object.userData.isSelected = false
      }
    })
    selectedObjects.value.length = 0
  }

  /**
   * 获取最近的交点
   * @param {Array} intersects - 交点数组
   * @returns {Object|null} 最近的交点
   */
  const getClosestIntersect = intersects => {
    return intersects.length > 0 ? intersects[0] : null
  }

  /**
   * 检查对象是否被选中
   * @param {THREE.Object3D} object - 要检查的对象
   * @returns {boolean} 是否被选中
   */
  const isObjectSelected = object => {
    return selectedObjects.value.includes(object)
  }

  // 计算属性
  const hasSelectedObjects = computed(() => selectedObjects.value.length > 0)
  const selectedObjectCount = computed(() => selectedObjects.value.length)
  const hasHoveredObject = computed(() => hoveredObject.value !== null)

  return {
    // 状态
    mouse,
    raycaster,
    hoveredObject,
    selectedObjects,
    hasSelectedObjects,
    selectedObjectCount,
    hasHoveredObject,

    // 方法
    updateMousePosition,
    performRaycast,
    detectObjects,
    handleObjectHover,
    clearObjectHover,
    selectObject,
    deselectObject,
    toggleObjectSelection,
    clearAllSelections,
    getClosestIntersect,
    isObjectSelected,
  }
}
