import { ref, computed } from 'vue'
import { MaterialHighlighter } from '@/utils/highlight/MaterialHighlighter.js'
import { HIGHLIGHT_CONSTANTS, getHighlightConfig } from '@/utils/highlight/HighlightConstants.js'

/**
 * 路线高亮功能组合式函数
 * 提供统一的路线高亮管理功能
 */
export function useRoadHighlight() {
  // 选中的路线ID列表
  const selectedRoadIds = ref([])
  
  // 高亮的路线对象映射
  const highlightedRoads = ref(new Map())
  
  // 计算属性：是否有选中的路线
  const hasSelectedRoads = computed(() => selectedRoadIds.value.length > 0)
  
  // 计算属性：选中路线数量
  const selectedRoadCount = computed(() => selectedRoadIds.value.length)

  /**
   * 添加路线高亮
   * @param {THREE.Object3D} roadObject - 路线对象
   * @param {Object} options - 高亮选项
   */
  const addRoadHighlight = (roadObject, options = {}) => {
    if (!roadObject?.userData) {
      console.warn('无效的路线对象')
      return false
    }

    const featureId = getRoadFeatureId(roadObject)
    if (!featureId) {
      console.warn('无法获取路线ID')
      return false
    }

    // 如果已经高亮，跳过
    if (roadObject.userData.isHighlighted) {
      return true
    }

    // 获取高亮配置
    const highlightConfig = getHighlightConfig(HIGHLIGHT_CONSTANTS.HIGHLIGHT_TYPES.ROAD_SELECTION)
    const mergedConfig = { ...highlightConfig, ...options }

    // 应用高亮效果
    const success = MaterialHighlighter.applyHighlight(roadObject, mergedConfig)
    
    if (success) {
      highlightedRoads.value.set(featureId, roadObject)
      console.log(`路线 ${featureId} 高亮成功`)
    }

    return success
  }

  /**
   * 移除路线高亮
   * @param {THREE.Object3D} roadObject - 路线对象
   */
  const removeRoadHighlight = (roadObject) => {
    if (!roadObject) {
      return false
    }

    const featureId = getRoadFeatureId(roadObject)
    const success = MaterialHighlighter.restoreOriginalMaterial(roadObject)
    
    if (success && featureId) {
      highlightedRoads.value.delete(featureId)
      console.log(`路线 ${featureId} 高亮移除成功`)
    }

    return success
  }

  /**
   * 处理路线选择
   * @param {THREE.Object3D} roadObject - 路线对象
   */
  const handleRoadSelection = (roadObject) => {
    if (!roadObject?.userData) return false

    const featureId = getRoadFeatureId(roadObject)
    if (!featureId) return false

    const index = selectedRoadIds.value.indexOf(featureId)
    
    if (index > -1) {
      // 取消选择
      selectedRoadIds.value.splice(index, 1)
      removeRoadHighlight(roadObject)
    } else {
      // 添加选择
      selectedRoadIds.value.push(featureId)
      addRoadHighlight(roadObject)
    }

    return true
  }

  /**
   * 清除所有路线高亮
   */
  const clearAllRoadHighlights = () => {
    let clearedCount = 0

    // 遍历所有高亮的路线对象
    highlightedRoads.value.forEach((roadObject, featureId) => {
      if (MaterialHighlighter.restoreOriginalMaterial(roadObject)) {
        clearedCount++
      }
    })

    // 清空映射和选中列表
    highlightedRoads.value.clear()
    selectedRoadIds.value.length = 0

    console.log(`清除了 ${clearedCount} 个路线高亮`)
    return clearedCount
  }

  /**
   * 清除特定路线的高亮
   * @param {string} roadId - 路线ID
   */
  const clearSpecificRoadHighlight = (roadId) => {
    const roadObject = highlightedRoads.value.get(roadId)
    if (roadObject) {
      const success = removeRoadHighlight(roadObject)
      if (success) {
        // 从选中列表中移除
        const index = selectedRoadIds.value.indexOf(roadId)
        if (index > -1) {
          selectedRoadIds.value.splice(index, 1)
        }
      }
      return success
    }
    return false
  }

  /**
   * 恢复路线高亮状态（在路网切换后调用）
   * @param {Array} roadGroups - 路线组数组
   */
  const restoreRoadHighlights = (roadGroups = []) => {
    if (!selectedRoadIds.value.length) {
      return 0
    }

    let restoredCount = 0

    selectedRoadIds.value.forEach(roadId => {
      roadGroups.forEach(group => {
        if (group && group.children.length > 0) {
          group.traverse(child => {
            if (child.isLine2 || child.type === 'Line2') {
              const childRoadId = getRoadFeatureId(child)
              
              if (childRoadId === roadId && !child.userData.isHighlighted) {
                if (addRoadHighlight(child)) {
                  restoredCount++
                }
              }
            }
          })
        }
      })
    })

    console.log(`恢复了 ${restoredCount} 个路线高亮`)
    return restoredCount
  }

  /**
   * 获取路线特征ID
   * @param {THREE.Object3D} roadObject - 路线对象
   * @returns {string|null} 特征ID
   */
  const getRoadFeatureId = (roadObject) => {
    if (!roadObject?.userData) return null
    
    return (
      roadObject.userData.featureId ||
      roadObject.userData.featureName ||
      roadObject.userData.id ||
      roadObject.userData.tempId ||
      null
    )
  }

  /**
   * 检查路线是否被选中
   * @param {string} roadId - 路线ID
   * @returns {boolean} 是否被选中
   */
  const isRoadSelected = (roadId) => {
    return selectedRoadIds.value.includes(roadId)
  }

  /**
   * 获取选中的路线ID列表
   * @returns {Array} 路线ID数组
   */
  const getSelectedRoadIds = () => {
    return [...selectedRoadIds.value]
  }

  /**
   * 设置选中的路线ID列表
   * @param {Array} roadIds - 路线ID数组
   */
  const setSelectedRoadIds = (roadIds) => {
    selectedRoadIds.value = [...roadIds]
  }

  return {
    // 状态
    selectedRoadIds,
    highlightedRoads,
    hasSelectedRoads,
    selectedRoadCount,

    // 方法
    addRoadHighlight,
    removeRoadHighlight,
    handleRoadSelection,
    clearAllRoadHighlights,
    clearSpecificRoadHighlight,
    restoreRoadHighlights,
    getRoadFeatureId,
    isRoadSelected,
    getSelectedRoadIds,
    setSelectedRoadIds,
  }
}
