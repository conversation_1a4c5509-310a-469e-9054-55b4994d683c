/**
 * 干线表单组合式函数
 * 统一管理干线表单状态，避免重复代码
 */
import { reactive, computed } from 'vue'

/**
 * 干线表单默认值
 */
const DEFAULT_FORM_DATA = {
  route_name: '',
  classification: '主要干线',
  selectedRoadIds: [],
}

/**
 * 干线表单组合式函数
 */
export function useTrunkLineForm(initialData = {}) {
  // 表单数据
  const formData = reactive({
    ...DEFAULT_FORM_DATA,
    ...initialData,
  })

  // 计算属性
  const canSubmit = computed(() => {
    return (
      formData.route_name.trim() !== '' && formData.selectedRoadIds.length > 0
    )
  })

  // 重置表单
  const reset = () => {
    Object.assign(formData, DEFAULT_FORM_DATA)
  }

  // 设置表单数据
  const setFormData = data => {
    Object.assign(formData, DEFAULT_FORM_DATA, data)
  }

  // 获取表单数据副本
  const getFormData = () => {
    return { ...formData }
  }

  return {
    formData,
    canSubmit,
    reset,
    setFormData,
    getFormData,
  }
}

/**
 * 干线分类选项
 */
export const TRUNK_LINE_CLASSIFICATIONS = [
  '主要干线',
  '次要干线',
  '支线',
  '快速路',
  '环线',
  '专用道',
  '辅助线',
  '临时线',
]

export default useTrunkLineForm
