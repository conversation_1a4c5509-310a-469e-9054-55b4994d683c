import { ref, computed } from 'vue'

/**
 * 提示框优先级管理系统
 * 解决多个提示框重叠显示的问题
 */
export function useTooltipPriority() {
  // 优先级定义：数值越高优先级越高
  const PRIORITY_LEVELS = {
    DEVICE: 3,      // 设备图标优先级最高
    TRUNK_LINE: 2,  // 干线优先级中等
    AREA: 1,        // 绘制区域优先级最低
  }

  // 当前悬停的对象集合
  const hoveredObjects = ref(new Map())

  // 计算当前应该显示的提示框
  const activeTooltip = computed(() => {
    if (hoveredObjects.value.size === 0) {
      return null
    }

    let highestPriority = 0
    let selectedTooltip = null

    for (const [type, data] of hoveredObjects.value) {
      const priority = PRIORITY_LEVELS[type] || 0
      if (priority > highestPriority) {
        highestPriority = priority
        selectedTooltip = { type, data }
      }
    }

    return selectedTooltip
  })

  /**
   * 注册悬停对象
   * @param {string} type - 对象类型 (DEVICE/TRUNK_LINE/AREA)
   * @param {Object} data - 对象数据
   */
  const registerHover = (type, data) => {
    hoveredObjects.value.set(type, data)
  }

  /**
   * 注销悬停对象
   * @param {string} type - 对象类型
   */
  const unregisterHover = (type) => {
    hoveredObjects.value.delete(type)
  }

  /**
   * 清除所有悬停对象
   */
  const clearAllHovers = () => {
    hoveredObjects.value.clear()
  }

  /**
   * 检查指定类型是否正在悬停
   * @param {string} type - 对象类型
   * @returns {boolean}
   */
  const isHovering = (type) => {
    return hoveredObjects.value.has(type)
  }

  /**
   * 检查指定类型是否是当前活跃的提示框
   * @param {string} type - 对象类型
   * @returns {boolean}
   */
  const isActiveTooltip = (type) => {
    return activeTooltip.value?.type === type
  }

  /**
   * 获取指定类型的悬停数据
   * @param {string} type - 对象类型
   * @returns {Object|null}
   */
  const getHoverData = (type) => {
    return hoveredObjects.value.get(type) || null
  }

  /**
   * 获取当前悬停对象的数量
   * @returns {number}
   */
  const getHoverCount = () => {
    return hoveredObjects.value.size
  }

  return {
    // 状态
    activeTooltip,
    hoveredObjects: computed(() => hoveredObjects.value),

    // 方法
    registerHover,
    unregisterHover,
    clearAllHovers,
    isHovering,
    isActiveTooltip,
    getHoverData,
    getHoverCount,

    // 常量
    PRIORITY_LEVELS,
  }
}
