import { ref, onBeforeUnmount } from 'vue'

/**
 * 提示框自动隐藏功能的 composable
 * 提供统一的定时器管理和自动隐藏逻辑
 */
export function useTooltipAutoHide(options = {}) {
  const {
    defaultDelay = 300,
    onHide = () => {},
    enableMouseHover = true,
  } = options

  // 定时器管理 - 使用 Map 存储不同类型提示框的定时器
  const timers = ref(new Map())

  // 鼠标悬停状态管理
  const isMouseOverTooltip = ref(false)

  /**
   * 启动自动隐藏定时器
   * @param {string} tooltipType - 提示框类型 (device/trunkLine/area)
   * @param {Function} hideCallback - 隐藏回调函数
   * @param {number} delay - 延迟时间，默认300ms
   */
  const startAutoHide = (tooltipType, hideCallback, delay = defaultDelay) => {
    // 清理已存在的定时器，避免重复创建
    clearAutoHide(tooltipType)

    const timerId = setTimeout(() => {
      // 检查鼠标是否在提示框上
      if (enableMouseHover && isMouseOverTooltip.value) {
        // 鼠标在提示框上，不隐藏，但清理定时器引用
        timers.value.delete(tooltipType)
        return
      }

      try {
        // 执行隐藏回调
        hideCallback()
        // 触发隐藏事件回调
        onHide(tooltipType)
      } catch (error) {
        console.error(`隐藏 ${tooltipType} 提示框时发生错误:`, error)
      } finally {
        // 清理定时器引用
        timers.value.delete(tooltipType)
      }
    }, delay)

    // 存储定时器ID
    timers.value.set(tooltipType, timerId)
  }

  /**
   * 清理指定类型的自动隐藏定时器
   * @param {string} tooltipType - 提示框类型
   */
  const clearAutoHide = tooltipType => {
    if (timers.value.has(tooltipType)) {
      clearTimeout(timers.value.get(tooltipType))
      timers.value.delete(tooltipType)
    }
  }

  /**
   * 清理所有自动隐藏定时器
   */
  const clearAllAutoHide = () => {
    timers.value.forEach(timerId => clearTimeout(timerId))
    timers.value.clear()
  }

  /**
   * 鼠标进入提示框处理
   * 暂停自动隐藏，保持提示框显示
   * @param {string} tooltipType - 提示框类型
   */
  const handleTooltipMouseEnter = tooltipType => {
    isMouseOverTooltip.value = true
    // 取消当前的自动隐藏定时器
    clearAutoHide(tooltipType)
  }

  /**
   * 鼠标离开提示框处理
   * 重新启动自动隐藏定时器
   * @param {string} tooltipType - 提示框类型
   * @param {Function} hideCallback - 隐藏回调函数
   * @param {number} delay - 延迟时间，默认使用配置的默认值
   */
  const handleTooltipMouseLeave = (
    tooltipType,
    hideCallback,
    delay = defaultDelay
  ) => {
    isMouseOverTooltip.value = false
    // 重新启动自动隐藏定时器
    startAutoHide(tooltipType, hideCallback, delay)
  }

  /**
   * 立即隐藏指定类型的提示框
   * @param {string} tooltipType - 提示框类型
   * @param {Function} hideCallback - 隐藏回调函数
   */
  const hideImmediately = (tooltipType, hideCallback) => {
    clearAutoHide(tooltipType)
    try {
      hideCallback()
      onHide(tooltipType)
    } catch (error) {
      console.error(`立即隐藏 ${tooltipType} 提示框时发生错误:`, error)
    }
  }

  /**
   * 检查指定类型的提示框是否有活跃的自动隐藏定时器
   * @param {string} tooltipType - 提示框类型
   * @returns {boolean} 是否有活跃的定时器
   */
  const hasActiveTimer = tooltipType => {
    return timers.value.has(tooltipType)
  }

  /**
   * 获取当前活跃的定时器数量
   * @returns {number} 活跃定时器数量
   */
  const getActiveTimerCount = () => {
    return timers.value.size
  }

  // 组件卸载时自动清理所有定时器，防止内存泄漏
  onBeforeUnmount(() => {
    clearAllAutoHide()
  })

  return {
    // 核心功能
    startAutoHide,
    clearAutoHide,
    clearAllAutoHide,
    hideImmediately,

    // 鼠标事件处理
    handleTooltipMouseEnter,
    handleTooltipMouseLeave,

    // 状态查询
    isMouseOverTooltip,
    hasActiveTimer,
    getActiveTimerCount,
  }
}
