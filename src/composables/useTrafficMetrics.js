/**
 * 交通指标数据管理 Composable
 * 基于真实API数据结构设计
 */
import { ref, computed } from 'vue'
import trafficMetricsApi from '@/api/modules/trafficMetrics'
import { getServiceLevelColor } from '@/utils/trafficColors'

export function useTrafficMetrics() {
  // 响应式数据
  const deviceMetrics = ref([])
  const corridorMetrics = ref([])
  const areaMetrics = ref([])
  const isLoading = ref(false)
  const lastUpdateTime = ref(null)

  /**
   * 获取设备指标数据
   */
  const fetchDeviceMetrics = async () => {
    isLoading.value = true
    try {
      const response = await trafficMetricsApi.getDeviceMetrics()
      // 简化：直接更新数据，Vue的响应式系统已经优化了不必要的更新
      deviceMetrics.value = response
      lastUpdateTime.value = new Date()
    } catch (error) {
      // API调用失败时保持原有数据
      console.warn('获取设备指标失败:', error)
    }
    isLoading.value = false
  }

  /**
   * 获取干线指标数据
   */
  const fetchCorridorMetrics = async () => {
    isLoading.value = true
    try {
      const response = await trafficMetricsApi.getCorridorMetrics()
      // 简化：直接更新数据，Vue的响应式系统已经优化了不必要的更新
      corridorMetrics.value = response
      lastUpdateTime.value = new Date()
    } catch (error) {
      // API调用失败时保持原有数据
      console.warn('获取干线指标失败:', error)
    }
    isLoading.value = false
  }

  /**
   * 获取区域指标数据
   */
  const fetchAreaMetrics = async () => {
    isLoading.value = true
    try {
      const response = await trafficMetricsApi.getAreaMetrics()
      // 简化：直接更新数据，Vue的响应式系统已经优化了不必要的更新
      areaMetrics.value = response
      lastUpdateTime.value = new Date()
    } catch (error) {
      // API调用失败时保持原有数据
      console.warn('获取区域指标失败:', error)
    }
    isLoading.value = false
  }

  /**
   * 获取所有指标数据
   */
  const fetchAllMetrics = async () => {
    await Promise.all([
      fetchDeviceMetrics(),
      fetchCorridorMetrics(),
      fetchAreaMetrics(),
    ])
  }

  /**
   * 根据设备ID获取设备指标
   */
  const getDeviceMetricsById = deviceId => {
    return deviceMetrics.value.find(item => item.object.edge_id === deviceId)
  }

  /**
   * 根据干线ID获取干线指标
   */
  const getCorridorMetricsById = routeId => {
    return corridorMetrics.value.find(item => item.object.route_id === routeId)
  }

  /**
   * 根据区域ID获取区域指标
   */
  const getAreaMetricsById = regionId => {
    return areaMetrics.value.find(item => item.object.region_id === regionId)
  }

  // 计算属性：按服务水平分组的统计
  const deviceStatsByLevel = computed(() => {
    const stats = { A: 0, B: 0, C: 0, D: 0, E: 0, F: 0 }
    deviceMetrics.value.forEach(item => {
      const level = item.metrics.serviceLevel
      if (level) stats[level]++
    })
    return stats
  })

  const corridorStatsByLevel = computed(() => {
    const stats = { A: 0, B: 0, C: 0, D: 0, E: 0, F: 0 }
    corridorMetrics.value.forEach(item => {
      const level = item.metrics.serviceLevel
      if (level) stats[level]++
    })
    return stats
  })

  const areaStatsByLevel = computed(() => {
    const stats = { A: 0, B: 0, C: 0, D: 0, E: 0, F: 0 }
    areaMetrics.value.forEach(item => {
      const level = item.metrics.serviceLevel
      if (level) stats[level]++
    })
    return stats
  })

  return {
    // 响应式数据
    deviceMetrics,
    corridorMetrics,
    areaMetrics,
    isLoading,
    lastUpdateTime,

    // 计算属性
    deviceStatsByLevel,
    corridorStatsByLevel,
    areaStatsByLevel,

    // 方法
    getServiceLevelColor,
    fetchDeviceMetrics,
    fetchCorridorMetrics,
    fetchAreaMetrics,
    fetchAllMetrics,
    getDeviceMetricsById,
    getCorridorMetricsById,
    getAreaMetricsById,
  }
}
