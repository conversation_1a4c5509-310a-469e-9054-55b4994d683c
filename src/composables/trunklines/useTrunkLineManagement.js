import { ref, computed, watch, nextTick } from 'vue'
import { useTrunkLinesStore } from '@/stores/trunkLines.js'
import { storeToRefs } from 'pinia'

/**
 * 干线管理组合式函数
 * 提供干线的创建、编辑、删除等核心管理功能
 */
export function useTrunkLineManagement(trunkLineRenderer) {
  const trunkLinesStore = useTrunkLinesStore()
  
  // 从 store 获取响应式数据
  const {
    trunkLines,
    isCreating,
    isEditing,
    creatingTrunkLine,
    editingTrunkLine,
    selectedTrunkLine,
    loading,
    error
  } = storeToRefs(trunkLinesStore)

  // 干线创建和编辑模式状态
  const isTrunkLineCreationMode = ref(false)
  const isTrunkLineEditingMode = ref(false)
  const editingTrunkLineId = ref(null)

  // 计算属性
  const hasTrunkLines = computed(() => trunkLines.value.length > 0)
  const trunkLineCount = computed(() => trunkLines.value.length)
  const isInManagementMode = computed(() => 
    isTrunkLineCreationMode.value || isTrunkLineEditingMode.value
  )

  /**
   * 开始创建干线
   */
  const startTrunkLineCreation = () => {
    try {
      trunkLinesStore.startCreating()
      isTrunkLineCreationMode.value = true
      isTrunkLineEditingMode.value = false
      editingTrunkLineId.value = null
      
      console.log('开始创建干线模式')
      return true
    } catch (error) {
      console.error('启动干线创建模式失败:', error)
      return false
    }
  }

  /**
   * 取消创建干线
   */
  const cancelTrunkLineCreation = () => {
    try {
      trunkLinesStore.cancelCreating()
      isTrunkLineCreationMode.value = false
      
      console.log('取消干线创建模式')
      return true
    } catch (error) {
      console.error('取消干线创建模式失败:', error)
      return false
    }
  }

  /**
   * 开始编辑干线
   * @param {Object} trunkLine - 要编辑的干线对象
   */
  const startTrunkLineEditing = (trunkLine) => {
    if (!trunkLine || !trunkLine.route_id) {
      console.error('无效的干线对象')
      return false
    }

    try {
      trunkLinesStore.startEditing(trunkLine)
      isTrunkLineEditingMode.value = true
      isTrunkLineCreationMode.value = false
      editingTrunkLineId.value = trunkLine.route_id
      
      console.log(`开始编辑干线: ${trunkLine.route_name}`)
      return true
    } catch (error) {
      console.error('启动干线编辑模式失败:', error)
      return false
    }
  }

  /**
   * 取消编辑干线
   */
  const cancelTrunkLineEditing = () => {
    try {
      trunkLinesStore.cancelEditing()
      isTrunkLineEditingMode.value = false
      editingTrunkLineId.value = null
      
      console.log('取消干线编辑模式')
      return true
    } catch (error) {
      console.error('取消干线编辑模式失败:', error)
      return false
    }
  }

  /**
   * 创建干线
   * @param {Array} selectedRoadIds - 选中的路线ID数组
   * @param {Object} options - 创建选项
   */
  const createTrunkLine = async (selectedRoadIds, options = {}) => {
    if (!selectedRoadIds || selectedRoadIds.length === 0) {
      console.error('没有选中的路线')
      return false
    }

    try {
      const result = await trunkLinesStore.createTrunkLine(selectedRoadIds, options)
      
      if (result) {
        // 创建成功后退出创建模式
        cancelTrunkLineCreation()
        console.log('干线创建成功')
      }
      
      return result
    } catch (error) {
      console.error('创建干线失败:', error)
      return false
    }
  }

  /**
   * 更新干线
   * @param {Object} updateData - 更新数据
   */
  const updateTrunkLine = async (updateData) => {
    if (!updateData || !updateData.route_id) {
      console.error('无效的更新数据')
      return false
    }

    try {
      const result = await trunkLinesStore.updateTrunkLine(updateData)
      
      if (result) {
        // 更新成功后退出编辑模式
        cancelTrunkLineEditing()
        console.log(`干线 ${updateData.route_name} 更新成功`)
      }
      
      return result
    } catch (error) {
      console.error('更新干线失败:', error)
      return false
    }
  }

  /**
   * 删除干线
   * @param {string} routeId - 干线ID
   */
  const deleteTrunkLine = async (routeId) => {
    if (!routeId) {
      console.error('无效的干线ID')
      return false
    }

    try {
      const result = await trunkLinesStore.deleteTrunkLine(routeId)
      
      if (result) {
        // 从渲染器中移除干线
        if (trunkLineRenderer) {
          trunkLineRenderer.removeTrunkLine(routeId)
        }
        console.log(`干线 ${routeId} 删除成功`)
      }
      
      return result
    } catch (error) {
      console.error('删除干线失败:', error)
      return false
    }
  }

  /**
   * 选择干线
   * @param {Object} trunkLine - 干线对象
   */
  const selectTrunkLine = (trunkLine) => {
    try {
      trunkLinesStore.selectTrunkLine(trunkLine)
      console.log(`选中干线: ${trunkLine?.route_name || '未知'}`)
      return true
    } catch (error) {
      console.error('选择干线失败:', error)
      return false
    }
  }

  /**
   * 取消选择干线
   */
  const deselectTrunkLine = () => {
    try {
      trunkLinesStore.deselectTrunkLine()
      console.log('取消选择干线')
      return true
    } catch (error) {
      console.error('取消选择干线失败:', error)
      return false
    }
  }

  /**
   * 退出所有管理模式
   */
  const exitAllModes = () => {
    cancelTrunkLineCreation()
    cancelTrunkLineEditing()
    deselectTrunkLine()
  }

  /**
   * 获取干线列表
   * @param {Object} filters - 过滤条件
   */
  const getTrunkLines = (filters = {}) => {
    return trunkLinesStore.searchTrunkLinesAdvanced(filters)
  }

  /**
   * 根据ID获取干线
   * @param {string} routeId - 干线ID
   */
  const getTrunkLineById = (routeId) => {
    return trunkLines.value.find(line => line.route_id === routeId)
  }

  // 监听干线数据变化，自动更新渲染
  watch(
    trunkLines,
    (newTrunkLines) => {
      if (!trunkLineRenderer) return

      // 清除所有现有干线渲染
      trunkLineRenderer.clearAllTrunkLines()

      // 渲染所有干线
      if (newTrunkLines && newTrunkLines.length > 0) {
        nextTick(() => {
          trunkLineRenderer.renderTrunkLines(newTrunkLines)
        })
      }
    },
    { deep: true, immediate: true }
  )

  return {
    // 状态
    trunkLines,
    isCreating,
    isEditing,
    creatingTrunkLine,
    editingTrunkLine,
    selectedTrunkLine,
    loading,
    error,
    isTrunkLineCreationMode,
    isTrunkLineEditingMode,
    editingTrunkLineId,
    hasTrunkLines,
    trunkLineCount,
    isInManagementMode,

    // 方法
    startTrunkLineCreation,
    cancelTrunkLineCreation,
    startTrunkLineEditing,
    cancelTrunkLineEditing,
    createTrunkLine,
    updateTrunkLine,
    deleteTrunkLine,
    selectTrunkLine,
    deselectTrunkLine,
    exitAllModes,
    getTrunkLines,
    getTrunkLineById,
  }
}
