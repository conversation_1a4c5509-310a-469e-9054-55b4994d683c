import { ref, onUnmounted } from 'vue'
import * as THREE from 'three'
import { ICON_CONFIG } from '@/config/icon.config'

// 聚合资源管理器（单例模式）
class ClusterResourceManager {
  constructor() {
    this.clusterTexture = null
    this.clusterMaterial = null
    this.spritePool = []
    this.svgDataUrl = null
  }

  // 获取SVG数据URL（单例缓存）
  getSvgDataUrl() {
    if (!this.svgDataUrl) {
      const svgContent = `<svg t="1755588582107" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1713" width="200" height="200"><path d="M702.932 777.584c-11.692-3.161-23.743 3.758-26.903 15.45-3.165 11.698 3.758 23.744 15.451 26.903 73.027 19.747 105.712 47.546 105.712 66.333 0 14.494-19.804 37.052-75.398 56.907-55.701 19.895-130.205 30.845-209.791 30.845-79.586 0-154.099-10.95-209.799-30.845-55.593-19.855-75.396-42.413-75.396-56.907 0-18.793 32.702-46.602 105.761-66.348 11.698-3.161 18.618-15.201 15.456-26.899-3.159-11.692-15.199-18.617-26.897-15.455-89.119 24.084-138.198 62.687-138.198 108.702 0 26.869 17.359 47.165 31.924 59.459 17.477 14.762 41.905 27.805 72.594 38.766 60.318 21.541 140.063 33.407 224.549 33.407 84.493 0 164.238-11.872 224.556-33.407 30.689-10.961 55.115-24.005 72.595-38.766 14.564-12.295 31.924-32.59 31.924-59.459 0-46-49.056-84.603-138.137-108.686v0zM422.048 826.725c23.419 26.842 56.222 42.223 90.118 42.223h0.005c34.028 0 66.802-15.506 90.080-42.569 7.059-7.696 67.087-73.789 127.916-160.351 88.146-125.43 132.837-229.369 132.837-308.923 0-47.367-9.283-93.344-27.597-136.64-17.684-41.803-42.986-79.343-75.212-111.563-32.219-32.218-69.751-57.526-111.554-75.202-43.302-18.315-89.278-27.603-136.639-27.603-47.367 0-93.344 9.289-136.641 27.603-41.802 17.678-79.342 42.985-111.561 75.202-32.219 32.226-57.526 69.759-75.202 111.563-18.315 43.297-27.603 89.273-27.603 136.64 0 79.552 44.724 183.524 132.929 309.029 61.247 87.146 121.682 153.568 128.124 160.59v0zM375.908 347.29c0-77.421 62.989-140.405 140.402-140.405 77.421 0 140.404 62.986 140.404 140.405 0 77.415-62.982 140.404-140.404 140.404-77.414 0-140.402-62.989-140.402-140.404v0zM375.908 347.29z" p-id="1714"></path></svg>`
      const tintedSvg = svgContent.replace('<path ', '<path fill="#ffffff" ')
      this.svgDataUrl = `data:image/svg+xml;base64,${btoa(tintedSvg)}`
    }
    return this.svgDataUrl
  }

  // 获取聚合纹理（单例缓存）
  async getClusterTexture() {
    if (!this.clusterTexture) {
      const textureLoader = new THREE.TextureLoader()
      const svgDataUrl = this.getSvgDataUrl()
      this.clusterTexture = await new Promise((resolve, reject) => {
        textureLoader.load(svgDataUrl, resolve, undefined, reject)
      })
    }
    return this.clusterTexture
  }

  // 获取聚合材质（单例缓存）
  async getClusterMaterial() {
    if (!this.clusterMaterial) {
      const texture = await this.getClusterTexture()
      this.clusterMaterial = new THREE.SpriteMaterial({
        map: texture,
        transparent: true,
        alphaTest: ICON_CONFIG.sprite.alphaTest,
        depthWrite: ICON_CONFIG.sprite.depthWrite,
        sizeAttenuation: ICON_CONFIG.sprite.sizeAttenuation,
        color: new THREE.Color(ICON_CONFIG.clusterColor),
      })
    } else {
      // 每次获取时同步颜色，确保配置变化立即生效
      this.clusterMaterial.color.set(ICON_CONFIG.clusterColor)
    }
    return this.clusterMaterial
  }

  // 从对象池获取精灵
  async getSprite() {
    if (this.spritePool.length > 0) {
      const sprite = this.spritePool.pop()
      sprite.visible = true
      // 确保颜色与配置同步
      if (sprite.material) {
        sprite.material.color.set(ICON_CONFIG.clusterColor)
      }
      return sprite
    }

    // 对象池为空时创建新精灵
    const material = await this.getClusterMaterial()
    const sprite = new THREE.Sprite(material)
    // 同步颜色（防止缓存材质颜色过期）
    sprite.material.color.set(ICON_CONFIG.clusterColor)
    return sprite
  }

  // 将精灵归还到对象池
  releaseSprite(sprite) {
    if (!sprite) return

    // 重置精灵状态
    sprite.visible = false
    sprite.userData = {}
    sprite.position.set(0, 0, 0)
    sprite.scale.set(1, 1, 1)

    // 归还到对象池
    this.spritePool.push(sprite)
  }

  // 清理所有资源
  dispose() {
    // 清理纹理
    if (this.clusterTexture) {
      this.clusterTexture.dispose()
      this.clusterTexture = null
    }

    // 清理材质
    if (this.clusterMaterial) {
      this.clusterMaterial.dispose()
      this.clusterMaterial = null
    }

    // 清理对象池
    this.spritePool.forEach(sprite => {
      if (sprite.material && sprite.material !== this.clusterMaterial) {
        sprite.material.dispose()
      }
    })
    this.spritePool = []

    this.svgDataUrl = null
  }
}

// 增量更新管理器
class IncrementalUpdateManager {
  constructor() {
    this.lastCameraPosition = new THREE.Vector3()
    this.lastCameraRotation = new THREE.Euler()
    this.lastViewportSize = { width: 0, height: 0 }
    this.lastDeviceCount = 0
    this.currentClusters = []

    // 变化检测阈值
    this.POSITION_THRESHOLD = 0.1 // 相机位置变化阈值
    this.ROTATION_THRESHOLD = 0.01 // 相机旋转变化阈值
  }

  // 检测是否需要重新计算聚合
  needsUpdate(camera, devices, container) {
    // 检查相机位置变化
    const positionChanged =
      camera.position.distanceTo(this.lastCameraPosition) >
      this.POSITION_THRESHOLD

    // 检查相机旋转变化
    const currentRotation = camera.rotation
    const rotationChanged =
      Math.abs(currentRotation.x - this.lastCameraRotation.x) >
        this.ROTATION_THRESHOLD ||
      Math.abs(currentRotation.y - this.lastCameraRotation.y) >
        this.ROTATION_THRESHOLD ||
      Math.abs(currentRotation.z - this.lastCameraRotation.z) >
        this.ROTATION_THRESHOLD

    // 检查视口大小变化
    const rect = container.getBoundingClientRect()
    const viewportChanged =
      rect.width !== this.lastViewportSize.width ||
      rect.height !== this.lastViewportSize.height

    // 检查设备数量变化
    const deviceCountChanged = devices.length !== this.lastDeviceCount

    return (
      positionChanged ||
      rotationChanged ||
      viewportChanged ||
      deviceCountChanged
    )
  }

  // 更新缓存状态
  updateCache(camera, devices, container, clusters) {
    this.lastCameraPosition.copy(camera.position)
    this.lastCameraRotation.copy(camera.rotation)

    const rect = container.getBoundingClientRect()
    this.lastViewportSize = { width: rect.width, height: rect.height }

    this.lastDeviceCount = devices.length
    this.currentClusters = clusters
  }

  // 获取缓存的聚合结果
  getCachedClusters() {
    return this.currentClusters
  }
}

/**
 * 设备聚合功能的 Vue 3 Composable
 * @param {Object} options 配置选项
 * @returns {Object} 聚合系统接口
 */
export function useDeviceCluster(options = {}) {
  // 配置参数
  const config = {
    distanceThreshold: 4,
    pixelThreshold: 60,
    baseScale: ICON_CONFIG.baseScale,
    yLevel: ICON_CONFIG.yLevel,
  }

  // 响应式状态
  const isClusterMode = ref(false)

  // 管理器实例
  const resourceManager = new ClusterResourceManager()
  const updateManager = new IncrementalUpdateManager()

  // 聚合组引用
  let clusterGroup = null

  // 将世界坐标投影到屏幕像素坐标
  const projectToScreenPixels = (worldPosition, camera, container) => {
    const vector = worldPosition.clone()
    vector.project(camera)

    const rect = container.getBoundingClientRect()
    const x = (vector.x * 0.5 + 0.5) * rect.width
    const y = (vector.y * -0.5 + 0.5) * rect.height

    return { x, y }
  }

  // 计算两个屏幕像素点之间的欧氏距离
  const calculatePixelDistance = (pos1, pos2) => {
    const dx = pos1.x - pos2.x
    const dy = pos1.y - pos2.y
    return Math.sqrt(dx * dx + dy * dy)
  }

  // 基于屏幕像素距离进行设备聚合
  const clusterDevicesByPixelDistance = (
    devices,
    camera,
    container,
    pixelThreshold
  ) => {
    // 移除过度防御：调用方已确保devices是有效数组

    // 计算每个设备的屏幕像素坐标
    const deviceData = devices.map(device => ({
      device,
      worldPos: device.position.clone(),
      screenPos: projectToScreenPixels(device.position, camera, container),
      clustered: false,
    }))

    const clusters = []

    // 贪心聚合算法
    for (let i = 0; i < deviceData.length; i++) {
      if (deviceData[i].clustered) continue

      const cluster = {
        devices: [deviceData[i].device],
        worldPositions: [deviceData[i].worldPos],
        centerWorldPos: deviceData[i].worldPos.clone(),
      }

      deviceData[i].clustered = true

      // 查找距离当前设备≤pixelThreshold的其他设备
      for (let j = i + 1; j < deviceData.length; j++) {
        if (deviceData[j].clustered) continue

        const distance = calculatePixelDistance(
          deviceData[i].screenPos,
          deviceData[j].screenPos
        )
        if (distance <= pixelThreshold) {
          cluster.devices.push(deviceData[j].device)
          cluster.worldPositions.push(deviceData[j].worldPos)
          deviceData[j].clustered = true
        }
      }

      // 计算聚合中心位置（质心）
      if (cluster.worldPositions.length > 1) {
        const center = new THREE.Vector3()
        cluster.worldPositions.forEach(pos => center.add(pos))
        center.divideScalar(cluster.worldPositions.length)
        cluster.centerWorldPos = center
      }

      clusters.push(cluster)
    }

    return clusters
  }

  // 创建聚合图标精灵
  const createClusterSprite = async cluster => {
    try {
      const clusterSprite = await resourceManager.getSprite()

      // 设置聚合图标的缩放（与设备图标共用配置）
      clusterSprite.scale.set(ICON_CONFIG.baseScale, ICON_CONFIG.baseScale, 1)
      clusterSprite.center.set(ICON_CONFIG.center.x, ICON_CONFIG.center.y)
      clusterSprite.renderOrder = ICON_CONFIG.renderOrder

      // 设置位置
      clusterSprite.position.copy(cluster.centerWorldPos)
      clusterSprite.position.y = config.yLevel

      // 设置用户数据
      clusterSprite.userData = {
        isCluster: true,
        deviceCount: cluster.devices.length,
        devices: cluster.devices,
      }

      return clusterSprite
    } catch (error) {
      console.error('创建聚合图标失败:', error)
      return null
    }
  }

  // 清理聚合组
  const clearClusterGroup = () => {
    if (!clusterGroup) return

    // 将所有精灵归还到对象池
    const spritesToRelease = [...clusterGroup.children]
    spritesToRelease.forEach(sprite => {
      clusterGroup.remove(sprite)
      resourceManager.releaseSprite(sprite)
    })
  }

  // 初始化聚合组
  const initClusterGroup = group => {
    clusterGroup = group
  }

  // 组件卸载时清理资源
  onUnmounted(() => {
    clearClusterGroup()
    resourceManager.dispose()
  })

  // 主要更新方法（增量更新机制）
  const update = async (
    camera,
    devices,
    container,
    currentDistance,
    deviceMarkersGroup
  ) => {
    // 简化检查：只检查关键的clusterGroup，其他参数由调用方保证

    // 判断是否应该启用聚合模式
    const shouldCluster = currentDistance > config.distanceThreshold

    if (shouldCluster && !isClusterMode.value) {
      // 进入聚合模式
      console.log('进入设备聚合模式，相机距离:', currentDistance)
      await activateClusterMode(camera, devices, container, deviceMarkersGroup)
    } else if (!shouldCluster && isClusterMode.value) {
      // 退出聚合模式
      console.log('退出设备聚合模式，相机距离:', currentDistance)
      deactivateClusterMode(deviceMarkersGroup)
    } else if (shouldCluster && isClusterMode.value) {
      // 已在聚合模式，使用增量更新
      await updateClustersIncremental(camera, devices, container)
    }
  }

  // 激活聚合模式
  const activateClusterMode = async (
    camera,
    devices,
    container,
    deviceMarkersGroup
  ) => {
    const visibleDevices = devices.filter(device => device.visible)

    // 简化：直接处理，空数组会被后续逻辑自然处理
    // 计算聚合
    const clusters = clusterDevicesByPixelDistance(
      visibleDevices,
      camera,
      container,
      config.pixelThreshold
    )

    // 更新缓存
    updateManager.updateCache(camera, visibleDevices, container, clusters)

    // 清理现有聚合并创建新的
    clearClusterGroup()
    await renderClusters(clusters)

    // 切换显示状态
    if (deviceMarkersGroup) deviceMarkersGroup.visible = false
    clusterGroup.visible = true

    isClusterMode.value = true
  }

  // 停用聚合模式
  const deactivateClusterMode = deviceMarkersGroup => {
    clearClusterGroup()

    // 切换显示状态
    clusterGroup.visible = false
    if (deviceMarkersGroup) deviceMarkersGroup.visible = true

    isClusterMode.value = false
  }

  // 增量更新聚合
  const updateClustersIncremental = async (camera, devices, container) => {
    const visibleDevices = devices.filter(device => device.visible)

    if (visibleDevices.length === 0) return

    // 检查是否需要更新
    if (!updateManager.needsUpdate(camera, visibleDevices, container)) {
      console.log('使用缓存的聚合结果，跳过重新计算')
      return
    }

    console.log('检测到变化，重新计算聚合')

    // 重新计算聚合
    const clusters = clusterDevicesByPixelDistance(
      visibleDevices,
      camera,
      container,
      config.pixelThreshold
    )

    // 更新缓存
    updateManager.updateCache(camera, visibleDevices, container, clusters)

    // 检查聚合是否有实质变化
    if (hasClusterChanges(clusters)) {
      clearClusterGroup()
      await renderClusters(clusters)
    }
  }

  // 渲染聚合图标
  const renderClusters = async clusters => {
    for (const cluster of clusters) {
      const clusterSprite = await createClusterSprite(cluster)
      if (clusterSprite) {
        clusterGroup.add(clusterSprite)
      }
    }
  }

  // 检查聚合是否有变化
  const hasClusterChanges = newClusters => {
    const currentClusters = updateManager.getCachedClusters()

    // 简单比较：数量不同则有变化
    if (newClusters.length !== currentClusters.length) {
      return true
    }

    // 可以在这里添加更精确的比较逻辑
    return false
  }

  return {
    // 响应式状态
    isClusterMode,

    // 初始化方法
    initClusterGroup,

    // 核心方法
    update,

    // 工具方法
    clearClusterGroup,

    // 配置
    config,
  }
}
