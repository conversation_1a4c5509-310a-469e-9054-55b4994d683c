import { ref, computed } from 'vue'
import { storeToRefs } from 'pinia'
import axios from 'axios'
import { getBaseUrl } from '@/config/server.config'
import { useDevicesStore } from '@/stores/devices'
import { useTrunkLinesStore } from '@/stores/trunkLines'
import { useRegionsStore } from '@/stores/regions'
import { useSceneStateStore } from '@/stores/sceneState'
import {
  DB_CONFIG,
  initIndexedDB as initSharedDB,
  saveToIndexedDB as saveShared,
  getFromIndexedDB as getShared,
} from '@/utils/indexedDBConfig'

/**
 * 数据管理 Composable
 * 负责处理所有数据相关的操作：IndexedDB、API数据获取、GeoJSON转换等
 */
export function useDataManager() {
  // ===== IndexedDB 配置 =====
  const CITY_DATA_STORE = 'cityData'
  const ROAD_DATA_STORE = 'roadNetworks'
  const USER_DRAWN_ROADS_STORE = 'userDrawnRoads'
  const USER_POINTS_STORE = 'userPoints'
  const USER_POLYGONS_STORE = 'userPolygons'

  let db = null

  // ===== Store 引用 =====
  const devicesStore = useDevicesStore()
  const { devices: apiDevices } = storeToRefs(devicesStore)
  const trunkLinesStore = useTrunkLinesStore()
  const { trunkLines: _trunkLines } = storeToRefs(trunkLinesStore)
  const regionsStore = useRegionsStore()
  const { regions } = storeToRefs(regionsStore)
  const sceneStateStore = useSceneStateStore()

  // ===== 响应式数据状态 =====
  const jinanCityGeoJson = ref(null)
  const jinanRoadNetworkGeoJson = ref(null)
  const userDrawnRoadsGeoJson = ref({ type: 'FeatureCollection', features: [] })
  const userPointsGeoJson = ref({ type: 'FeatureCollection', features: [] })
  const userPolygonsGeoJson = ref({ type: 'FeatureCollection', features: [] })

  // 中心点数据
  const cityOutlineGeoCenter = ref(null)
  const roadNetworkGeoCenter = ref(null)

  // 加载状态
  const isDataLoading = ref(false)
  const dataLoadingProgress = ref(0)

  // ===== IndexedDB 操作函数 =====

  /**
   * 初始化 IndexedDB
   */
  const initIndexedDB = async () => {
    try {
      db = await initSharedDB()
      return db
    } catch (error) {
      console.error('IndexedDB初始化失败:', error)
      throw error
    }
  }

  /**
   * 保存数据到 IndexedDB
   */
  const saveToIndexedDB = async (storeName, id, data) => {
    if (!db) {
      throw new Error('数据库未初始化')
    }

    if (!data) {
      throw new Error('数据为空')
    }

    try {
      // 直接保存到指定存储
      await saveShared(db, storeName, id, data)

      // 验证数据是否真的保存成功
      const savedData = await getShared(db, storeName, id)
      if (!savedData) {
        throw new Error('数据保存后验证失败')
      }

      return true
    } catch (error) {
      console.error(
        `保存数据到IndexedDB失败 (${storeName}/${id}):`,
        error.message
      )
      throw error
    }
  }

  /**
   * 从 IndexedDB 获取数据
   */
  const getFromIndexedDB = async (storeName, id) => {
    if (!db) {
      return null
    }

    try {
      // 直接从指定存储获取数据
      let data = await getShared(db, storeName, id)

      // 如果没有找到数据，尝试一些向后兼容的查找
      if (!data) {
        // 对于城市数据，尝试从boundaries存储获取
        if (storeName === CITY_DATA_STORE) {
          const boundaryId = id === 'default' ? 1 : id
          data = await getShared(db, 'boundaries', boundaryId)
        }

        // 对于路网数据，尝试不同的ID格式
        if (storeName === ROAD_DATA_STORE && id === 'default') {
          data = await getShared(db, 'roadNetworks', 1)
        }
      }

      return data
    } catch (error) {
      console.error(
        `从IndexedDB获取数据失败 (${storeName}/${id}):`,
        error.message
      )
      return null
    }
  }

  // ===== 数据转换函数 =====

  /**
   * 将API设备数据转换为GeoJSON格式
   */
  const devicesGeoJson = computed(() => {
    const features = apiDevices.value
      .filter(
        device =>
          device.edge_coord &&
          typeof device.edge_coord.lng === 'number' &&
          typeof device.edge_coord.lat === 'number'
      )
      .map(device => ({
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: [device.edge_coord.lng, device.edge_coord.lat],
        },
        properties: {
          // 确保使用正确的字段名
          id: device.edge_id, // 使用edge_id作为id
          name: device.edge_name, // 使用edge_name作为name
          ip: device.ip_address, // 使用ip_address作为ip
          ...device, // 将整个设备对象作为属性，方便后续使用
          source: 'api',
        },
      }))
    return { type: 'FeatureCollection', features: features }
  })

  /**
   * 将服务器区域数据转换为GeoJSON格式
   */
  const convertServerRegionsToGeoJson = regions => {
    if (!regions || !Array.isArray(regions)) {
      return { type: 'FeatureCollection', features: [] }
    }

    const features = regions
      .filter(region => {
        if (!region.region || !region.region.coordinates) {
          return false
        }

        return true
      })
      .map(region => ({
        type: 'Feature',
        geometry: region.region,
        properties: {
          id: region.region_id,
          name: region.region_name,
          classification: region.classification,
          description: region.description || '',
          source: 'server',
        },
      }))
    return { type: 'FeatureCollection', features }
  }

  // ===== 中心点计算函数 =====

  /**
   * 从GeoJSON计算平均中心点
   */
  const calculateAverageCenterFromGeoJson = geojson => {
    if (!geojson || !geojson.features || geojson.features.length === 0) {
      return null
    }

    let accumulatedLng = 0
    let accumulatedLat = 0
    let totalPoints = 0

    geojson.features.forEach(feature => {
      if (feature.geometry) {
        if (feature.geometry.type === 'Point') {
          const [lng, lat] = feature.geometry.coordinates
          accumulatedLng += lng
          accumulatedLat += lat
          totalPoints++
        } else if (feature.geometry.type === 'LineString') {
          feature.geometry.coordinates.forEach(coord => {
            const [lng, lat] = coord
            accumulatedLng += lng
            accumulatedLat += lat
            totalPoints++
          })
        } else if (feature.geometry.type === 'Polygon') {
          feature.geometry.coordinates[0].forEach(coord => {
            const [lng, lat] = coord
            accumulatedLng += lng
            accumulatedLat += lat
            totalPoints++
          })
        }
      }
    })

    if (totalPoints === 0) {
      return null
    }

    return {
      lng: accumulatedLng / totalPoints,
      lat: accumulatedLat / totalPoints,
    }
  }

  /**
   * 计算路网中心点
   */
  const calculateRoadNetworkCenter = geojson => {
    if (!geojson || !geojson.features || geojson.features.length === 0) {
      return null
    }

    let totalPoints = 0
    let accumulatedLng = 0
    let accumulatedLat = 0

    geojson.features.forEach(feature => {
      if (feature.geometry && feature.geometry.type === 'LineString') {
        feature.geometry.coordinates.forEach(coord => {
          if (Array.isArray(coord) && coord.length >= 2) {
            accumulatedLng += coord[0]
            accumulatedLat += coord[1]
            totalPoints++
          }
        })
      }
    })

    if (totalPoints === 0) {
      return null
    }

    const centerLng = accumulatedLng / totalPoints
    const centerLat = accumulatedLat / totalPoints

    return { lng: centerLng, lat: centerLat }
  }

  // ===== 数据加载函数 =====

  /**
   * 加载城市数据
   */
  const loadCityData = async () => {
    try {
      // 从 IndexedDB 加载城市数据
      const cityData = await getFromIndexedDB(CITY_DATA_STORE, 'default')
      if (cityData) {
        jinanCityGeoJson.value = cityData
        return cityData
      } else {
        // 如果IndexedDB中没有数据，则从服务器获取ID为1的行政区划数据
        try {
          const API_BASE_URL = getBaseUrl('geoData')
          const response = await axios.get(`${API_BASE_URL}/boundarys/1`)

          if (response.data && response.data.boundary) {
            // 保存到 IndexedDB
            await saveToIndexedDB(
              CITY_DATA_STORE,
              'default',
              response.data.boundary
            )

            // 加载到地图
            jinanCityGeoJson.value = response.data.boundary
            return response.data.boundary
          }
        } catch (error) {
          console.error('自动获取行政区划数据失败:', error)
          throw error
        }
      }
    } catch (error) {
      console.error('加载城市数据失败:', error)
      throw error
    }
  }

  /**
   * 加载路网数据
   */
  const loadRoadNetworkData = async () => {
    try {
      // 从 IndexedDB 加载路网数据
      const roadData = await getFromIndexedDB(ROAD_DATA_STORE, 'default')
      if (roadData) {
        jinanRoadNetworkGeoJson.value = roadData
        return roadData
      } else {
        // 如果IndexedDB中没有数据，则从服务器获取ID为1的路网数据
        try {
          const API_BASE_URL = getBaseUrl('geoData')
          const response = await axios.get(`${API_BASE_URL}/road_networks/1`)

          if (response.data && response.data.road_network) {
            // 保存到 IndexedDB
            await saveToIndexedDB(
              ROAD_DATA_STORE,
              'default',
              response.data.road_network
            )

            // 加载到地图
            jinanRoadNetworkGeoJson.value = response.data.road_network
            return response.data.road_network
          }
        } catch (error) {
          console.error('自动获取路网数据失败:', error)
          throw error
        }
      }
    } catch (error) {
      console.error('加载路网数据失败:', error)
      throw error
    }
  }

  /**
   * 加载用户绘制的道路数据
   */
  const loadUserDrawnRoads = async () => {
    try {
      const data = await getFromIndexedDB(USER_DRAWN_ROADS_STORE, 'default')

      if (
        data &&
        data.type === 'FeatureCollection' &&
        Array.isArray(data.features)
      ) {
        userDrawnRoadsGeoJson.value = data
      } else {
        // IndexedDB中没有找到用户绘制的道路数据
        // 初始化空的道路集合
        userDrawnRoadsGeoJson.value = {
          type: 'FeatureCollection',
          features: [],
        }
      }
    } catch (error) {
      console.error('加载用户绘制的道路数据失败:', error)
      // 初始化空的道路集合
      userDrawnRoadsGeoJson.value = { type: 'FeatureCollection', features: [] }
    }
  }

  /**
   * 加载用户点位数据
   */
  const loadUserPoints = async () => {
    try {
      const data = await getFromIndexedDB(USER_POINTS_STORE, 'default')

      if (
        data &&
        data.type === 'FeatureCollection' &&
        Array.isArray(data.features)
      ) {
        userPointsGeoJson.value = data
      } else {
        // IndexedDB中没有找到用户点位数据
        // 初始化空的点位集合
        userPointsGeoJson.value = { type: 'FeatureCollection', features: [] }
      }
    } catch (error) {
      console.error('加载用户点位数据失败:', error)
      // 初始化空的点位集合
      userPointsGeoJson.value = { type: 'FeatureCollection', features: [] }
    }
  }

  /**
   * 加载用户区域数据（优先从服务器获取最新数据）
   */
  const loadUserPolygons = async () => {
    try {
      // 1. 优先从服务器加载最新区域数据
      let serverFeatures = []
      let serverDataLoaded = false

      try {
        console.log('正在从服务器加载区域数据...')
        const serverRegionsGeoJson = await loadServerRegionsToMap()
        if (serverRegionsGeoJson && serverRegionsGeoJson.features) {
          serverFeatures = serverRegionsGeoJson.features.map(feature => ({
            ...feature,
            properties: {
              ...feature.properties,
              source: 'server-region',
              server_id: feature.properties.id,
            },
          }))
          serverDataLoaded = true
          console.log(`从服务器加载了 ${serverFeatures.length} 个区域`)
        }
      } catch (error) {
        console.warn('从服务器加载区域数据失败，将尝试从本地缓存加载:', error)
      }

      // 2. 从IndexedDB加载本地用户绘制的区域数据（作为补充或备用）
      const localData = await getFromIndexedDB(USER_POLYGONS_STORE, 'default')
      let localFeatures = []

      if (
        localData &&
        localData.type === 'FeatureCollection' &&
        Array.isArray(localData.features)
      ) {
        // 过滤掉服务器区域，只保留本地绘制的区域
        localFeatures = localData.features.filter(
          feature => feature.properties.source !== 'server-region'
        )
        console.log(`从本地缓存加载了 ${localFeatures.length} 个用户绘制区域`)
      }

      // 3. 合并数据：服务器数据 + 本地绘制数据
      const allFeatures = [...serverFeatures, ...localFeatures]
      userPolygonsGeoJson.value = {
        type: 'FeatureCollection',
        features: allFeatures,
      }

      // 4. 如果成功从服务器获取数据，更新本地缓存以保持同步
      if (serverDataLoaded && allFeatures.length > 0) {
        try {
          await saveUserPolygons()
          console.log('已更新本地区域数据缓存')
        } catch (error) {
          console.warn('更新本地缓存失败:', error)
        }
      }

      console.log(
        `总共加载了 ${allFeatures.length} 个区域 (服务器: ${serverFeatures.length}, 本地: ${localFeatures.length})`
      )
    } catch (error) {
      console.error('加载用户区域数据失败:', error)
      // 初始化空的区域集合
      userPolygonsGeoJson.value = { type: 'FeatureCollection', features: [] }
    }
  }

  // ===== Store 初始化函数 =====

  /**
   * 检查数据一致性并清理过期缓存
   */
  const checkDataConsistency = async () => {
    try {
      // 检查本地数据时间戳
      const lastSyncTime = await getFromIndexedDB('settings', 'lastDataSync')
      const currentTime = Date.now()

      // 如果超过1小时未同步，或者是首次访问，强制从服务器重新加载
      const SYNC_INTERVAL = 60 * 60 * 1000 // 1小时
      const shouldForceSync =
        !lastSyncTime || currentTime - lastSyncTime > SYNC_INTERVAL

      if (shouldForceSync) {
        console.log('检测到数据可能过期，将强制从服务器重新同步区域数据')

        // 清理区域相关的本地缓存
        try {
          await saveToIndexedDB(USER_POLYGONS_STORE, 'default', {
            type: 'FeatureCollection',
            features: [],
          })
          console.log('已清理本地区域数据缓存')
        } catch (error) {
          console.warn('清理本地缓存失败:', error)
        }

        // 更新同步时间戳
        await saveToIndexedDB('settings', 'lastDataSync', currentTime)
        return true // 表示需要重新加载数据
      }

      return false // 表示数据是最新的
    } catch (error) {
      console.warn('数据一致性检查失败:', error)
      return true // 出错时也强制重新加载
    }
  }

  /**
   * 统一初始化所有Store
   */
  const initializeAllStores = async () => {
    try {
      // 初始化设备Store
      await devicesStore.fetchDevices()

      // 初始化干线Store
      await trunkLinesStore.initialize()

      // 初始化区域Store
      await regionsStore.initialize()
    } catch (error) {
      console.error('Store初始化过程中出现错误:', error)
    }
  }

  /**
   * 加载服务器区域数据到地图
   */
  const loadServerRegionsToMap = async () => {
    try {
      // 等待区域Store初始化完成
      if (regions.value && regions.value.length > 0) {
        const serverRegionsGeoJson = convertServerRegionsToGeoJson(
          regions.value
        )
        return serverRegionsGeoJson
      } else {
        return { type: 'FeatureCollection', features: [] }
      }
    } catch (error) {
      console.error('加载服务器区域数据到地图失败:', error)
      return { type: 'FeatureCollection', features: [] }
    }
  }

  /**
   * 手动刷新区域数据（强制从服务器重新获取）
   */
  const refreshUserPolygons = async () => {
    try {
      console.log('手动刷新区域数据...')

      // 清理本地缓存
      await saveToIndexedDB(USER_POLYGONS_STORE, 'default', {
        type: 'FeatureCollection',
        features: [],
      })

      // 重新初始化Store
      await regionsStore.initialize()

      // 重新加载数据
      await loadUserPolygons()

      // 更新同步时间戳
      await saveToIndexedDB('settings', 'lastDataSync', Date.now())

      console.log('区域数据刷新完成')
      return true
    } catch (error) {
      console.error('刷新区域数据失败:', error)
      return false
    }
  }

  // ===== 数据保存函数 =====

  /**
   * 保存用户绘制的道路数据
   */
  const saveUserDrawnRoads = async () => {
    try {
      // 转换为普通对象以避免响应式代理问题
      const plainData = JSON.parse(JSON.stringify(userDrawnRoadsGeoJson.value))
      await saveToIndexedDB(USER_DRAWN_ROADS_STORE, 'default', plainData)
      return true
    } catch (error) {
      console.error('保存用户绘制道路数据失败:', error)
      throw error
    }
  }

  /**
   * 保存用户区域数据（只保存本地绘制的区域，不包括服务器区域）
   */
  const saveUserPolygons = async () => {
    try {
      // 过滤出只有本地绘制的区域（排除服务器区域）
      const localFeatures = userPolygonsGeoJson.value.features.filter(
        feature => feature.properties.source !== 'server-region'
      )

      const localPolygonsData = {
        type: 'FeatureCollection',
        features: localFeatures,
      }

      await saveToIndexedDB(USER_POLYGONS_STORE, 'default', localPolygonsData)
    } catch (error) {
      console.error('处理用户区域数据时出错:', error)
    }
  }

  /**
   * 保存用户点位数据
   */
  const saveUserPoints = async () => {
    try {
      // 转换为普通对象以避免响应式代理问题
      const plainData = JSON.parse(JSON.stringify(userPointsGeoJson.value))
      await saveToIndexedDB(USER_POINTS_STORE, 'default', plainData)
    } catch (error) {
      console.error('处理用户点位数据时出错:', error)
    }
  }

  // ===== 数据事件处理函数 =====

  /**
   * 处理城市数据加载事件
   */
  const handleCityDataLoaded = async data => {
    jinanCityGeoJson.value = data

    // 保存数据到IndexedDB
    try {
      // 保存到 IndexedDB
      await saveToIndexedDB(CITY_DATA_STORE, 'default', data)
    } catch (e) {
      console.error('处理城市数据时出错:', e)
    }
  }

  /**
   * 处理路网数据加载事件
   */
  const handleRoadDataLoaded = async data => {
    jinanRoadNetworkGeoJson.value = data

    // 计算路网中心点
    const roadCenter = calculateRoadNetworkCenter(data)
    if (roadCenter) {
      roadNetworkGeoCenter.value = roadCenter
    }

    // 保存到IndexedDB
    try {
      await saveToIndexedDB(ROAD_DATA_STORE, 'default', data)
    } catch (e) {
      console.error('处理路网数据时出错:', e)
    }
  }

  return {
    // 状态
    jinanCityGeoJson,
    jinanRoadNetworkGeoJson,
    userDrawnRoadsGeoJson,
    userPointsGeoJson,
    userPolygonsGeoJson,
    cityOutlineGeoCenter,
    roadNetworkGeoCenter,
    isDataLoading,
    dataLoadingProgress,

    // 计算属性
    devicesGeoJson,

    // IndexedDB 操作
    initIndexedDB,
    saveToIndexedDB,
    getFromIndexedDB,

    // 数据加载
    loadCityData,
    loadRoadNetworkData,
    loadUserDrawnRoads,
    loadUserPoints,
    loadUserPolygons,
    refreshUserPolygons,

    // Store 初始化
    initializeAllStores,
    loadServerRegionsToMap,
    checkDataConsistency,

    // 数据保存
    saveUserDrawnRoads,
    saveUserPolygons,
    saveUserPoints,

    // 数据事件处理
    handleCityDataLoaded,
    handleRoadDataLoaded,

    // 数据转换
    convertServerRegionsToGeoJson,

    // 中心点计算
    calculateAverageCenterFromGeoJson,
    calculateRoadNetworkCenter,

    // Store 引用
    devicesStore,
    trunkLinesStore,
    regionsStore,
    sceneStateStore,

    // 常量
    CITY_DATA_STORE,
    ROAD_DATA_STORE,
    USER_DRAWN_ROADS_STORE,
    USER_POINTS_STORE,
    USER_POLYGONS_STORE,
  }
}
