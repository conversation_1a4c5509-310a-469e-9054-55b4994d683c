// Three.js 场景引擎（核心）
// 我使用 Vue 3 Composition API 管理 Three.js 的创建、销毁与渲染循环
// 我暴露统一的图层/工具注册接口，便于后续插件化扩展

import { ref, onMounted, onUnmounted } from 'vue'
import * as THREE from 'three'

/**
 * useThreeWorld
 * @param {Object} options
 * @param {number} [options.backgroundColor=0x050a14] - 背景色
 * @returns {Object} world 引擎接口
 */
export function useThreeWorld(options = {}) {
  // 我在这里定义基础配置，遵循现有项目默认背景
  const backgroundColor = options.backgroundColor ?? 0x050a14

  // DOM 容器引用（外部组件传递）
  const containerRef = ref(null)

  // Three 基本对象
  const scene = new THREE.Scene()
  scene.background = new THREE.Color(backgroundColor)

  // 我准备两套相机：透视（3D）与正交（2D），通过开关切换
  const perspectiveCamera = new THREE.PerspectiveCamera(60, 1, 0.1, 10000)
  // 我将默认相机位置调整为更贴近当前坐标尺度（参考 ThreeCanvas 的经验值）
  perspectiveCamera.position.set(6, 3, -16)
  perspectiveCamera.lookAt(new THREE.Vector3(0, 0, 0))

  const orthographicCamera = new THREE.OrthographicCamera(
    -10,
    10,
    10,
    -10,
    0.1,
    2000
  )
  // 俯视相机不需要太高的高度，否则在当前坐标尺度下对象会过小
  orthographicCamera.position.set(0, 20, 0)
  orthographicCamera.up.set(0, 0, -1)
  orthographicCamera.lookAt(new THREE.Vector3(0, 0, 0))

  const activeCamera = ref(perspectiveCamera)
  const is2DMode = ref(false)

  const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: false })
  renderer.setPixelRatio(Math.min(window.devicePixelRatio || 1, 2))
  renderer.setSize(1, 1)

  // 我创建一个统一的根分组挂载所有图层
  const layersRoot = new THREE.Group()
  layersRoot.name = 'LayersRoot'
  scene.add(layersRoot)

  // 图层/工具注册表
  const layerRegistry = new Map() // id -> { id, group, clear?, onBeforeRender?, onAfterRender? }
  const toolRegistry = new Map() // 预留给后续绘制/交互工具

  // 动画循环
  const isRunning = ref(false)
  let rafHandle = null

  const tick = () => {
    // 我在这里预留每帧回调，以便部分图层自定义更新
    layerRegistry.forEach(layer => {
      if (typeof layer.onBeforeRender === 'function') {
        layer.onBeforeRender({ scene, camera: activeCamera.value, renderer })
      }
    })

    renderer.render(scene, activeCamera.value)

    layerRegistry.forEach(layer => {
      if (typeof layer.onAfterRender === 'function') {
        layer.onAfterRender({ scene, camera: activeCamera.value, renderer })
      }
    })

    rafHandle = requestAnimationFrame(tick)
  }

  // 自适应尺寸
  const resize = () => {
    const el = containerRef.value
    if (!el) return
    const { clientWidth: w, clientHeight: h } = el
    if (w === 0 || h === 0) return

    // 我更新透视相机投影
    perspectiveCamera.aspect = w / h
    perspectiveCamera.updateProjectionMatrix()

    // 我更新正交相机的视锥（使用世界单位的基准尺寸，避免随像素放大导致对象极小）
    const aspect = w / h
    const FRUSTUM_SIZE = 20 // 依据当前坐标尺度取一个合适视域
    orthographicCamera.left = (-FRUSTUM_SIZE * aspect) / 2
    orthographicCamera.right = (FRUSTUM_SIZE * aspect) / 2
    orthographicCamera.top = FRUSTUM_SIZE / 2
    orthographicCamera.bottom = -FRUSTUM_SIZE / 2
    orthographicCamera.updateProjectionMatrix()

    renderer.setSize(w, h, false)
  }

  // UI 控制（与现有基础UI组件集成时只需调用这些方法）
  const resetView = () => {
    // 我将相机重置到初始视角（根据当前模式选择不同的相机姿态）
    if (is2DMode.value) {
      orthographicCamera.position.set(0, 20, 0)
      orthographicCamera.up.set(0, 0, -1)
      orthographicCamera.lookAt(0, 0, 0)
    } else {
      perspectiveCamera.position.set(6, 3, -16)
      perspectiveCamera.lookAt(0, 0, 0)
    }
  }

  const toggle2D3D = () => {
    // 我在2D与3D间切换活动相机
    is2DMode.value = !is2DMode.value
    activeCamera.value = is2DMode.value ? orthographicCamera : perspectiveCamera
    resize()
  }

  const setBackgroundColor = color => {
    // 我允许外部动态调整背景色
    scene.background = new THREE.Color(color)
  }

  // 图层注册
  const registerLayer = layer => {
    // 我要求 layer 具备 { id, group }，其余方法可选
    if (!layer || !layer.id || !layer.group) {
      throw new Error('注册图层失败：layer 需要包含 { id, group }')
    }
    if (layerRegistry.has(layer.id)) {
      throw new Error(`注册图层失败：已存在同名图层 ${layer.id}`)
    }

    layersRoot.add(layer.group)
    layerRegistry.set(layer.id, layer)
    return layer.id
  }

  const unregisterLayer = id => {
    const layer = layerRegistry.get(id)
    if (!layer) return
    // 我在卸载时从场景移除分组，并清理
    layersRoot.remove(layer.group)
    if (typeof layer.clear === 'function') layer.clear()
    layerRegistry.delete(id)
  }

  const listLayers = () => Array.from(layerRegistry.keys())
  const getLayer = id => layerRegistry.get(id) || null

  // 工具注册（预留）
  const registerTool = tool => {
    if (!tool || !tool.id) {
      throw new Error('注册工具失败：tool 需要包含 { id }')
    }
    if (toolRegistry.has(tool.id)) {
      throw new Error(`注册工具失败：已存在同名工具 ${tool.id}`)
    }
    toolRegistry.set(tool.id, tool)
    return tool.id
  }
  const unregisterTool = id => toolRegistry.delete(id)

  onMounted(() => {
    const el = containerRef.value
    if (!el) return
    el.appendChild(renderer.domElement)
    resize()
    if (!isRunning.value) {
      isRunning.value = true
      rafHandle = requestAnimationFrame(tick)
    }
    window.addEventListener('resize', resize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', resize)
    if (rafHandle) cancelAnimationFrame(rafHandle)
    isRunning.value = false

    // 我谨慎释放渲染器/场景引用（避免修改无关代码）
    renderer.dispose()
  })

  return {
    // 基础对象
    scene,
    camera: activeCamera,
    renderer,
    containerRef,

    // UI 控制
    ui: {
      resetView,
      toggle2D3D,
    },
    setBackgroundColor,

    // 图层/工具注册
    registerLayer,
    unregisterLayer,
    listLayers,
    getLayer,
    registerTool,
    unregisterTool,

    // 运行状态
    is2DMode,
  }
}
