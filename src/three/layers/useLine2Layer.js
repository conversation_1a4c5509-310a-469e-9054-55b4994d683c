// 线图层（Line2 多线渲染 + 可选标签，第一批仅实现线渲染）
// 我使用 three/examples/jsm/lines 的 Line2，符合你对 line2 多线路网的要求

import * as THREE from 'three'
import { Line2 } from 'three/examples/jsm/lines/Line2.js'
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial.js'
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry.js'
import { transformGeoJsonCoordinates, COORDINATE_CONFIG } from '@/utils/coordinateTransform'

function assertLineGeoJson(fc) {
  if (!fc || fc.type !== 'FeatureCollection' || !Array.isArray(fc.features)) {
    throw new Error('Line2Layer: 需要合法的 FeatureCollection.features 数组')
  }
  for (const f of fc.features) {
    if (f.type !== 'Feature') throw new Error('Line2Layer: Feature.type 必须为 Feature')
    if (!f.properties || f.properties.id == null)
      throw new Error('Line2Layer: Feature.properties.id 必须存在且唯一')
    const g = f.geometry
    if (!g || (g.type !== 'LineString' && g.type !== 'MultiLineString'))
      throw new Error('Line2Layer: geometry.type 必须为 LineString 或 MultiLineString')
    const coords = g.coordinates
    if (!Array.isArray(coords) || coords.length < 1) throw new Error('Line2Layer: 坐标不能为空')
  }
}

export function useLine2Layer(world, options = {}) {
  const id = options.id || 'line2-layer'
  const yLevel = options.yLevel ?? COORDINATE_CONFIG.Y_LEVELS.ROADS
  const lineColor = options.lineColor ?? 0xffffff
  const lineWidth = options.lineWidth ?? 2.0 // 像素宽度（LineMaterial 中是屏幕像素）

  // 我按材质缓存，避免重复创建
  const material = new LineMaterial({
    color: lineColor,
    linewidth: lineWidth, // in pixels
    transparent: true,
    depthTest: true,
    depthWrite: false,
  })

  // material 分辨率需设置为渲染器 size
  function syncMaterialResolution() {
    const size = world?.renderer?.getSize?.(new THREE.Vector2())
    if (!size) return
    material.resolution.set(size.x, size.y)
  }
  syncMaterialResolution()

  const group = new THREE.Group()
  group.name = 'Line2Layer'

  function clear() {
    const toRemove = [...group.children]
    for (const obj of toRemove) {
      group.remove(obj)
      if (obj.geometry) obj.geometry.dispose()
      if (obj.material && obj.material !== material) obj.material.dispose()
    }
  }

  function addLineFromCoords(coords) {
    // coords: [[lng, lat], ...]
    const vertices = transformGeoJsonCoordinates(coords).flatMap(([x, z]) => [x, yLevel, z])
    if (vertices.length < 6) return // 少于2点不渲染

    const geometry = new LineGeometry()
    geometry.setPositions(vertices)

    const line = new Line2(geometry, material)
    line.computeLineDistances()
    group.add(line)
  }

  function renderLine({ data /* FeatureCollection */, options: opts }) {
    assertLineGeoJson(data)
    clear()

    // 同步 material 参数
    if (opts?.lineColor != null) material.color.set(opts.lineColor)
    if (opts?.lineWidth != null) material.linewidth = opts.lineWidth
    syncMaterialResolution()

    for (const f of data.features) {
      if (f.geometry.type === 'LineString') {
        addLineFromCoords(f.geometry.coordinates)
      } else {
        // MultiLineString
        for (const seg of f.geometry.coordinates) addLineFromCoords(seg)
      }
    }
  }

  // 渲染器尺寸变化时需要更新分辨率
  const onAfterRender = () => {
    syncMaterialResolution()
  }

  return { id, group, renderLine, clear, onAfterRender }
}

