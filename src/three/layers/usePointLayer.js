// 点图层（基础渲染）
// 我复用 ICON_CONFIG、对象池与单例纹理材质的理念，渲染 GeoJSON Point
// 事件：鼠标移入/移出/左键/右键，返回原始 feature

import * as THREE from 'three'
import { ICON_CONFIG } from '@/config/icon.config'
import { transformCoordinate } from '@/utils/coordinateTransform'

// 我在这里做必要的外部数据校验（入口处），内部假设数据已有效
function assertPointGeoJson(fc) {
  if (!fc || fc.type !== 'FeatureCollection' || !Array.isArray(fc.features)) {
    throw new Error('PointLayer: 需要合法的 FeatureCollection.features 数组')
  }
  for (const f of fc.features) {
    if (f.type !== 'Feature')
      throw new Error('PointLayer: Feature.type 必须为 Feature')
    if (!f.properties || f.properties.id == null)
      throw new Error('PointLayer: Feature.properties.id 必须存在且唯一')
    if (!f.geometry || f.geometry.type !== 'Point')
      throw new Error('PointLayer: geometry.type 必须为 Point')
    const c = f.geometry.coordinates
    if (!Array.isArray(c) || c.length < 2)
      throw new Error('PointLayer: Point 坐标必须为 [lng, lat, (opt height)]')
  }
}

class IconResourceManager {
  constructor() {
    this.textureCache = new Map() // key: dataUrl/hash -> THREE.Texture
    this.materialCache = new Map() // key: textureKey -> THREE.SpriteMaterial
    this.spritePool = []
  }

  // 我将 svg/png/jpg 的 dataUrl 作为键；外部传入字符串（data URL）或 Image 对象时，使用者需先转为 dataURL
  async getTexture(dataUrl) {
    if (this.textureCache.has(dataUrl)) return this.textureCache.get(dataUrl)
    const loader = new THREE.TextureLoader()
    const texture = await new Promise((resolve, reject) => {
      loader.load(dataUrl, resolve, undefined, reject)
    })
    this.textureCache.set(dataUrl, texture)
    return texture
  }

  async getMaterial(dataUrl) {
    if (this.materialCache.has(dataUrl)) return this.materialCache.get(dataUrl)
    const texture = await this.getTexture(dataUrl)
    const mat = new THREE.SpriteMaterial({
      map: texture,
      color: 0xffffff,
      transparent: true,
      alphaTest: ICON_CONFIG.sprite.alphaTest,
      depthWrite: ICON_CONFIG.sprite.depthWrite,
      depthTest: ICON_CONFIG.sprite.depthTest,
      sizeAttenuation: ICON_CONFIG.sprite.sizeAttenuation,
    })
    this.materialCache.set(dataUrl, mat)
    return mat
  }

  async getSprite(dataUrl) {
    if (this.spritePool.length > 0) {
      const s = this.spritePool.pop()
      s.visible = true
      return s
    }
    const mat = await this.getMaterial(dataUrl)
    const sprite = new THREE.Sprite(mat)
    sprite.scale.set(ICON_CONFIG.baseScale, ICON_CONFIG.baseScale, 1)
    sprite.center.set(ICON_CONFIG.center.x, ICON_CONFIG.center.y)
    sprite.renderOrder = ICON_CONFIG.renderOrder
    return sprite
  }

  releaseSprite(sprite) {
    if (!sprite) return
    sprite.visible = false
    sprite.userData = {}
    sprite.position.set(0, 0, 0)
    sprite.scale.set(1, 1, 1)
    this.spritePool.push(sprite)
  }

  dispose() {
    for (const t of this.textureCache.values()) t.dispose()
    for (const m of this.materialCache.values()) m.dispose()
    this.textureCache.clear()
    this.materialCache.clear()
    this.spritePool.length = 0
  }
}

/**
 * usePointLayer
 * 我用于渲染 GeoJSON 点位（FeatureCollection of Point），支持传入统一或逐点的图标 dataURL。
 *
 * @param {Object} world - useThreeWorld 返回的引擎对象
 * @param {Object} [options]
 * @param {string} [options.id='point-layer'] - 图层ID
 * @param {number} [options.yLevel=ICON_CONFIG.yLevel] - 点位Y轴层级
 * @param {string} [options.icon] - 统一 dataURL 图标（svg/png/jpg）
 * @returns {{ id: string, group: import('three').Group, renderPoints: Function, clear: Function }}
 *
 * 使用示例：
 * await pointLayer.renderPoints({ data: pointFC, icon: dataUrl })
 * // 或在feature.properties.iconDataUrl中为不同点设置不同图标
 *
 * GeoJSON 要求：
 * - 顶层：FeatureCollection
 * - features[].type = 'Feature'
 * - features[].properties.id 存在且唯一
 * - features[].geometry.type = 'Point'
 * - coordinates = [lng, lat, (opt height)]
 */
export function usePointLayer(_world, options = {}) {
  // 我缓存资源管理器，延续单例/对象池思路（按层实例持有）
  const resources = new IconResourceManager()

  const group = new THREE.Group()
  group.name = 'PointLayer'

  // 事件回调（由使用者传入）
  const onMouseIn = options.onMouseIn
  const onMouseOut = options.onMouseOut
  const onClick = options.onClick
  const onContextMenu = options.onContextMenu

  let currentIconDataUrl = options.icon // 可选图标 dataUrl（若每个点不同，可在数据里指定）

  const id = options.id || 'point-layer'

  // 我渲染点位（完整替换方式），遵循禁止自动修正
  async function renderPoints({ data, icon }) {
    assertPointGeoJson(data)
    if (icon) currentIconDataUrl = icon

    clear()

    const yLevel = options.yLevel ?? ICON_CONFIG.yLevel
    // 我设置一个更大的默认缩放值，确保图标可见
    const baseScale =
      options.baseScale ??
      options.iconScale ??
      Math.max(ICON_CONFIG.baseScale, 0.5)

    console.info('[PointLayer] renderPoints start', {
      featureCount: data.features.length,
      yLevel,
      baseScale,
      iconDataUrl: currentIconDataUrl ? 'provided' : 'none',
    })

    for (const feature of data.features) {
      const [lng, lat, h = 0] = feature.geometry.coordinates
      // 我将经纬度转换为三维坐标 [x, y, z]
      const [x, y, z] = transformCoordinate(lng, lat, h)

      const useDataUrl = feature.properties?.iconDataUrl || currentIconDataUrl
      if (!useDataUrl) {
        console.warn('[PointLayer] 跳过无图标的点位', feature.properties?.id)
        continue
      }

      const sprite = await resources.getSprite(useDataUrl)
      // 我在此按图层配置重设缩放，避免 ICON_CONFIG.baseScale 过小导致看不见
      sprite.scale.set(baseScale, baseScale, 1)
      sprite.position.set(x, yLevel + y, z)
      sprite.userData = { feature }
      group.add(sprite)

      console.info('[PointLayer] 添加点位', {
        id: feature.properties?.id,
        position: [x, yLevel + y, z],
        scale: baseScale,
      })
    }

    console.info('[PointLayer] renderPoints complete', {
      totalSprites: group.children.length,
    })
  }

  function clear() {
    // 归还所有 sprite
    const children = [...group.children]
    for (const c of children) {
      group.remove(c)
      resources.releaseSprite(c)
    }
  }

  // 简单拾取与事件（可被更高级交互替代）
  // 我在此不绑定全局事件总线，交互第三批再统一整合

  return {
    id,
    group,
    renderPoints,
    clear,
  }
}
