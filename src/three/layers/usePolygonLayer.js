// 多边形图层（填充）
// 我支持 Polygon/MultiPolygon，遵循外部数据校验，填充色可配置

import * as THREE from 'three'
import {
  transformGeoJsonCoordinates,
  COORDINATE_CONFIG,
} from '@/utils/coordinateTransform'

function assertPolygonGeoJson(fc) {
  if (!fc || fc.type !== 'FeatureCollection' || !Array.isArray(fc.features)) {
    throw new Error('PolygonLayer: 需要合法的 FeatureCollection.features 数组')
  }
  for (const f of fc.features) {
    if (f.type !== 'Feature')
      throw new Error('PolygonLayer: Feature.type 必须为 Feature')
    if (!f.properties || f.properties.id == null)
      throw new Error('PolygonLayer: Feature.properties.id 必须存在且唯一')
    const g = f.geometry
    if (!g || (g.type !== 'Polygon' && g.type !== 'MultiPolygon'))
      throw new Error(
        'PolygonLayer: geometry.type 必须为 Polygon 或 MultiPolygon'
      )
    const coords = g.coordinates
    if (!Array.isArray(coords) || coords.length < 1)
      throw new Error('PolygonLayer: 坐标不能为空')
  }
}

/**
 * usePolygonLayer
 * 我用于渲染面数据（Polygon / MultiPolygon），支持填充与轮廓线。
 *
 * @param {Object} world - useThreeWorld 返回的引擎对象
 * @param {Object} [options]
 * @param {string} [options.id='polygon-layer'] - 图层ID
 * @param {number} [options.yLevel=COORDINATE_CONFIG.Y_LEVELS.AREAS] - 面的Y层级
 * @param {string|number} [options.fillColor='#228be6'] - 填充颜色
 * @param {string|number} [options.outlineColor='#55ccff'] - 轮廓颜色
 * @param {number} [options.outlineWidth=1] - 轮廓宽度（LineBasicMaterial）
 * @returns {{ id: string, group: import('three').Group, renderPolygon: Function, clear: Function }}
 *
 * 使用示例：
 * polygonLayer.renderPolygon({ data: polygonFC, options: { fillColor: '#22D3EE', outlineColor: '#fff' } })
 *
 * GeoJSON 要求：
 * - 顶层：FeatureCollection
 * - features[].type = 'Feature'
 * - features[].properties.id 存在且唯一
 * - features[].geometry.type ∈ { Polygon, MultiPolygon }
 * - coordinates 为有效环（首尾闭合，不自交，面积非零）
 */
export function usePolygonLayer(world, options = {}) {
  const id = options.id || 'polygon-layer'
  const yLevel = options.yLevel ?? COORDINATE_CONFIG.Y_LEVELS.AREAS
  const fillColor = new THREE.Color(options.fillColor ?? '#228be6')
  const outlineColor = new THREE.Color(options.outlineColor ?? '#55ccff')
  const outlineWidth = options.outlineWidth ?? 1

  const group = new THREE.Group()
  group.name = 'PolygonLayer'

  function clear() {
    const toRemove = [...group.children]
    for (const obj of toRemove) {
      group.remove(obj)
      if (obj.geometry) obj.geometry.dispose()
      if (obj.material) obj.material.dispose()
    }
  }

  function addPolygon(rings) {
    // rings: [outerRing, hole1, hole2...], each ring [[lng,lat]...]
    // 我构建 Shape + holes
    const outer = transformGeoJsonCoordinates(rings[0])
    const shape = new THREE.Shape(
      outer.map(([x, z]) => new THREE.Vector2(x, z))
    )

    for (let i = 1; i < rings.length; i++) {
      const hole = transformGeoJsonCoordinates(rings[i])
      const path = new THREE.Path(hole.map(([x, z]) => new THREE.Vector2(x, z)))
      shape.holes.push(path)
    }

    const geometry = new THREE.ShapeGeometry(shape)
    const material = new THREE.MeshBasicMaterial({
      color: fillColor,
      transparent: true,
      opacity: 0.6,
      depthWrite: false,
    })
    const mesh = new THREE.Mesh(geometry, material)
    mesh.position.y = yLevel
    group.add(mesh)

    // 轮廓线（使用 LineLoop）
    const outlineGeom = new THREE.BufferGeometry()
    const outlinePoints = outer.map(
      ([x, z]) => new THREE.Vector3(x, yLevel + 1e-4, z)
    )
    outlineGeom.setFromPoints(outlinePoints)
    const outlineMat = new THREE.LineBasicMaterial({
      color: outlineColor,
      linewidth: outlineWidth,
      transparent: true,
      opacity: 0.9,
    })
    const outline = new THREE.LineLoop(outlineGeom, outlineMat)
    group.add(outline)
  }

  function renderPolygon({ data, options: opts }) {
    assertPolygonGeoJson(data)
    clear()

    if (opts?.fillColor) fillColor.set(opts.fillColor)
    if (opts?.outlineColor) outlineColor.set(opts.outlineColor)

    for (const f of data.features) {
      if (f.geometry.type === 'Polygon') {
        addPolygon(f.geometry.coordinates)
      } else {
        for (const rings of f.geometry.coordinates) addPolygon(rings)
      }
    }
  }

  return { id, group, renderPolygon, clear }
}
