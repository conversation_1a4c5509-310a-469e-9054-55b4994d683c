<template>
  <div class="device-selector">
    <a-select
      v-model:value="selectedDeviceId"
      :loading="devicesLoading"
      :style="{ width: width }"
      :placeholder="placeholder"
      @change="handleDeviceChange"
    >
      <a-select-option
        v-for="device in devices"
        :key="device.edge_id"
        :value="device.edge_id"
      >
        {{ device.edge_name }} ({{ device.edge_id }})
      </a-select-option>
    </a-select>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { useDevicesStore } from '@s2/stores/devices'
import { storeToRefs } from 'pinia'

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  width: {
    type: String,
    default: '300px',
  },
  placeholder: {
    type: String,
    default: '请选择设备',
  },
  initialDeviceId: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

// 设备状态
const selectedDeviceId = ref('')

// 从设备store中获取设备列表
const devicesStore = useDevicesStore()
const { devices, isLoading: devicesLoading } = storeToRefs(devicesStore)

// 监听props.modelValue变化
watch(
  () => props.modelValue,
  newValue => {
    if (newValue !== selectedDeviceId.value) {
      selectedDeviceId.value = newValue
    }
  }
)

// 监听selectedDeviceId变化
watch(
  () => selectedDeviceId.value,
  newValue => {
    emit('update:modelValue', newValue)
  }
)

// 监听initialDeviceId变化
watch(
  () => props.initialDeviceId,
  newValue => {
    if (newValue && newValue !== selectedDeviceId.value) {
      selectedDeviceId.value = newValue
    }
  },
  { immediate: true }
)

// 处理设备选择变更
const handleDeviceChange = deviceId => {
  selectedDeviceId.value = deviceId

  // 查找对应的设备对象
  const selectedDevice = devices.value.find(d => d.edge_id === deviceId)

  // 通知父组件设备变更
  emit('change', selectedDevice)
}

// 组件挂载时只设置初始值，不重复获取设备列表（已在CityModel中统一初始化）
onMounted(() => {
  // 如果设备列表为空且未初始化，才获取设备列表
  if (devices.value.length === 0 && !devicesStore.initialized) {
    devicesStore.fetchDevices()
  }

  // 如果有初始设备ID，设置为选中
  if (props.initialDeviceId) {
    selectedDeviceId.value = props.initialDeviceId
  } else if (props.modelValue) {
    selectedDeviceId.value = props.modelValue
  }
})
</script>

<style scoped>
.device-selector {
  display: inline-block;
}
</style>
