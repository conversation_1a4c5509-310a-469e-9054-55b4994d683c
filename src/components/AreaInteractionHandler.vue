<template></template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { throttle } from 'lodash'
import * as THREE from 'three'

const props = defineProps({
  // Three.js 相关属性
  sceneManager: Object, // 场景管理器
  userPolygonsGroup: Object, // 用户绘制区域组
  picking: Object, // 统一拾取管线
})

const emit = defineEmits([
  'areaHover', // 区域悬停事件
  'areaHoverEnd', // 区域悬停结束事件
  'areaLeftClick', // 区域左键点击事件
  'areaRightClick', // 区域右键点击事件
])

// 内部状态
const hoveredArea = ref(null) // 当前鼠标悬停的区域
const isSceneInteracting = ref(false) // 场景交互状态

// 鼠标移动优化相关
const lastMousePosition = ref({ x: 0, y: 0 })
const MOUSE_MOVE_THRESHOLD = 3 // 像素阈值，小于此值不触发检测

// 构建区域事件数据的工具函数
const buildAreaEvent = (areaObject, originalEvent = null) => {
  const userData = areaObject.userData
  const feature = userData.feature

  // 如果有原始事件，计算鼠标在3D空间中的位置
  let mouseWorldPosition = null
  if (originalEvent && props.picking) {
    const { ndc } = props.picking.getNDCFromDOM(originalEvent)
    const hit = props.picking.pick(areaObject, { ndc, recursive: false })
    if (hit.intersections.length > 0) {
      const p = hit.intersections[0].point
      mouseWorldPosition = new THREE.Vector3(p.x, p.y, p.z)
    }
  }

  return {
    area: feature,
    areaName: feature?.properties?.name,
    object3D: areaObject,
    userData,
    mouseWorldPosition, // 鼠标在区域上的3D位置
    ...(originalEvent && { originalEvent }),
  }
}

// 检测区域交互（点击或悬停）
const checkAreaIntersection = event => {
  if (!props.userPolygonsGroup || !props.picking) {
    return null
  }

  // 只检测填充面（Mesh类型），过滤掉轮廓线
  const fillMeshes = props.userPolygonsGroup.children.filter(
    child =>
      child instanceof THREE.Mesh &&
      child.userData?.featureType === 'user-polygon-fill'
  )
  if (fillMeshes.length === 0) return null

  const { ndc } = props.picking.getNDCFromDOM(event)
  const result = props.picking.pick(fillMeshes, { ndc, recursive: false })

  if (result.intersections.length > 0) {
    const obj = result.intersections[0].object
    if (obj.userData?.feature) {
      return obj
    }
  }

  return null
}

// 清除悬停状态
const clearHoverState = () => {
  if (hoveredArea.value) {
    emit('areaHoverEnd', buildAreaEvent(hoveredArea.value))
    hoveredArea.value = null
  }
}

// 鼠标按下处理
const handleMouseDown = event => {
  // 记录场景交互状态，防止在拖动时触发悬停
  if (event.buttons > 0) {
    isSceneInteracting.value = true
  }
}

// 优化的鼠标移动处理函数，添加移动阈值检测
const handleMouseMoveInternal = event => {
  if (event.buttons > 0) {
    isSceneInteracting.value = true
    clearHoverState()
    return
  }

  // 检查鼠标移动距离是否超过阈值
  const deltaX = Math.abs(event.clientX - lastMousePosition.value.x)
  const deltaY = Math.abs(event.clientY - lastMousePosition.value.y)
  const totalDelta = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

  // 如果移动距离小于阈值，不进行检测
  if (totalDelta < MOUSE_MOVE_THRESHOLD) {
    return
  }

  // 更新最后鼠标位置
  lastMousePosition.value = { x: event.clientX, y: event.clientY }

  // 检测鼠标悬停在区域上
  const area = checkAreaIntersection(event)

  if (area) {
    // 如果区域变化了
    if (area !== hoveredArea.value) {
      clearHoverState()

      // 触发新区域悬停事件
      emit('areaHover', buildAreaEvent(area, event))
      hoveredArea.value = area
    }
  } else if (hoveredArea.value) {
    // 没有检测到区域，清除悬停状态
    clearHoverState()
  }
}

// 使用节流优化的鼠标移动处理函数
const handleMouseMove = throttle(handleMouseMoveInternal, 16) // 约60fps

// 鼠标抬起处理
const handleMouseUp = event => {
  if (isSceneInteracting.value) {
    isSceneInteracting.value = false
    return
  }

  // 只处理左键点击
  if (event.button === 0) {
    const area = checkAreaIntersection(event)
    if (area) {
      emit('areaLeftClick', buildAreaEvent(area, event))
    }
  }
}

// 右键点击处理，只在点击到区域时阻止默认行为
const handleContextMenu = event => {
  const area = checkAreaIntersection(event)
  if (area) {
    event.preventDefault()
    emit('areaRightClick', buildAreaEvent(area, event))
  }
}

// 鼠标离开场景容器时，发送悬停结束事件
const handleMouseLeave = () => {
  clearHoverState()
}

// 生命周期钩子
onMounted(() => {
  const container = props.sceneManager?.getContainer()
  if (container) {
    container.addEventListener('mousedown', handleMouseDown)
    container.addEventListener('mousemove', handleMouseMove)
    container.addEventListener('mouseup', handleMouseUp)
    container.addEventListener('contextmenu', handleContextMenu)
    container.addEventListener('mouseleave', handleMouseLeave)
  }
})

onBeforeUnmount(() => {
  const container = props.sceneManager?.getContainer()
  if (container) {
    container.removeEventListener('mousedown', handleMouseDown)
    container.removeEventListener('mousemove', handleMouseMove)
    container.removeEventListener('mouseup', handleMouseUp)
    container.removeEventListener('contextmenu', handleContextMenu)
    container.removeEventListener('mouseleave', handleMouseLeave)
  }

  // 清理状态
  clearHoverState()
})
</script>
