<template>
  <a-modal
    :visible="visible"
    :width="1600"
    @cancel="handleClose"
    :footer="null"
    :destroyOnClose="true"
    :wrapClassName="`${monitorType}-monitor-modal`"
    :bodyStyle="{ maxHeight: '90vh', overflow: 'hidden' }"
  >
    <template #title>
      <div class="modal-title-with-selector">
        <span class="modal-title">{{ titleText }}</span>
        <device-selector
          v-model="state.selectedDeviceId"
          :initial-device-id="state.initialDeviceId"
          @change="handleDeviceChange"
          class="device-selector"
        />
      </div>
    </template>
    <a-spin :spinning="state.loading">
      <div v-if="state.error" class="error-container">
        <a-alert type="error" :message="state.error" show-icon />
        <div class="error-actions">
          <a-button type="primary" @click="initModalContent">重试加载</a-button>
          <a-button @click="handleClose">关闭</a-button>
        </div>
      </div>
      <div v-else class="monitor-container">
        <lazy-component-loader
          :component-type="componentType"
          :edge-id="state.selectedDeviceId"
          :embedded-mode="true"
          :monitor-type="monitorType"
          @loading="loading => (state.loading = loading)"
        />
      </div>
    </a-spin>
  </a-modal>
</template>

<script setup>
import { reactive, computed, watchEffect, defineAsyncComponent } from 'vue'
import DeviceSelector from './DeviceSelector.vue'

const LazyComponentLoader = defineAsyncComponent({
  loader: () => import('./LazyComponentLoader.vue'),
  errorComponent: { template: '<div>组件加载失败</div>' },
  delay: 200,
})

const props = defineProps({
  visible: { type: Boolean, default: false },
  device: { type: Object, default: null },
  monitorType: {
    type: String,
    default: 'running',
    validator: value => ['running', 'intersection', 'device'].includes(value),
  },
})

const emit = defineEmits(['close'])

// 合并相关状态
const state = reactive({
  loading: true,
  error: '',
  selectedDeviceId: '',
  initialDeviceId: '',
})

const MONITOR_CONFIG = {
  running: { title: '运行监视', component: 'index' },
  intersection: { title: '路口监控', component: 'intersection-screen' },
  device: { title: '设备监控', component: 'device-control-screen' },
}

// 缓存配置查找
const currentConfig = computed(
  () => MONITOR_CONFIG[props.monitorType] || MONITOR_CONFIG.running
)
const titleText = computed(() => currentConfig.value.title)
const componentType = computed(() => currentConfig.value.component)

// 自动同步设备ID
watchEffect(() => {
  state.initialDeviceId = props.device?.edge_id || ''
})

// 监听模态框显示状态
watchEffect(() => {
  if (props.visible) {
    state.loading = true
    state.error = ''
    // 清除路口监控的本地存储数据
    if (props.monitorType === 'intersection') {
      localStorage.removeItem('backup_channelization')
    }
    state.loading = false
  } else {
    state.error = ''
    state.loading = false
  }
})

const handleDeviceChange = () => {
  state.loading = true
  setTimeout(() => (state.loading = false), 300)
}

const initModalContent = () => {
  state.loading = true
  state.error = ''
  if (props.monitorType === 'intersection') {
    localStorage.removeItem('backup_channelization')
  }
  state.loading = false
}

const handleClose = () => {
  state.error = ''
  state.loading = false
  emit('close')
}
</script>

<style scoped>
.modal-title-with-selector {
  display: flex;
  align-items: center;
}

.modal-title {
  font-size: 16px;
  font-weight: bold;
}

.monitor-container {
  height: calc(90vh - 150px);
  overflow: hidden;
}

.error-container {
  padding: 20px;
  text-align: center;
}

.error-actions {
  margin-top: 15px;
}

.error-actions button + button {
  margin-left: 8px;
}

.device-selector {
  margin-left: 16px;
}
</style>
