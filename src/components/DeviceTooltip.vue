<template>
  <div
    v-if="actualTooltipData"
    class="universal-tooltip"
    :class="[`tooltip-${tooltipType}`]"
    :style="tooltipStyle"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <div class="tooltip-header">
      <div class="tooltip-title">{{ tooltipTitle }}</div>
      <button class="tooltip-close" @click="handleClose">×</button>
    </div>

    <!-- 设备信息内容 -->
    <div v-if="tooltipType === 'device'" class="tooltip-info">
      <div><span>设备ID:</span> {{ actualTooltipData.id }}</div>
      <div v-if="actualTooltipData.ip">
        <span>IP地址:</span> {{ actualTooltipData.ip }}
      </div>
      <div v-if="actualTooltipData.jnc_name">
        <span>路口名称:</span> {{ actualTooltipData.jnc_name }}
      </div>
      <!-- 控制模式相关字段展示 -->
      <template v-if="actualTooltipData.utcState">
        <div>
          <span>边缘计算控制模式:</span>
          {{ actualTooltipData.utcState.mode ?? '--' }}
        </div>
        <div>
          <span>UTC控制模式名称:</span>
          {{ actualTooltipData.utcState.mode_name ?? '--' }}
        </div>
        <div>
          <span>UTC阶段:</span>
          {{ actualTooltipData.utcState.utc_stage ?? '--' }}
        </div>
        <div>
          <span>UTC方案:</span>
          {{ actualTooltipData.utcState.utc_plan ?? '--' }}
        </div>
        <div>
          <span>阶段倒计时:</span>
          {{ actualTooltipData.utcState.stage_count_down ?? '--' }}
        </div>
        <div>
          <span>阶段时间长度:</span>
          {{ actualTooltipData.utcState.stage_length ?? '--' }}
        </div>
        <div>
          <span>过渡模式:</span>
          {{ actualTooltipData.utcState.transition_mode ?? '--' }}
        </div>
        <div>
          <span>过渡方式名称:</span>
          {{ actualTooltipData.utcState.transition_name ?? '--' }}
        </div>
      </template>

      <!-- 交通指标信息 -->
      <template v-if="showTrafficMetrics && trafficMetrics">
        <div class="traffic-metrics-section">
          <div class="metrics-header">
            <span class="metrics-title">交通指标</span>
            <div
              class="service-level-badge"
              :style="{
                backgroundColor: getServiceLevelColor(
                  trafficMetrics.serviceLevel
                ),
              }"
            >
              {{ trafficMetrics.serviceLevel }}级 -
              {{ getServiceLevelDescription(trafficMetrics.serviceLevel) }}
            </div>
          </div>
          <div class="metrics-content">
            <div class="metric-item">
              <span>车均控制延误:</span>
              <span class="metric-value"
                >{{ trafficMetrics.controlDelay }}s/veh</span
              >
            </div>
            <div class="metric-item">
              <span>95%排队长度:</span>
              <span class="metric-value"
                >{{ trafficMetrics.q95QueueVeh }}veh</span
              >
            </div>
            <div class="metric-item">
              <span>停车率:</span>
              <span class="metric-value">{{ trafficMetrics.stopRate }}%</span>
            </div>
            <div class="metric-item">
              <span>饱和度:</span>
              <span class="metric-value">{{ trafficMetrics.vByC }}</span>
            </div>
            <div class="metric-item">
              <span>队列利用率:</span>
              <span class="metric-value">{{ trafficMetrics.q95Ratio }}</span>
            </div>
            <div class="metric-item">
              <span>储车位:</span>
              <span class="metric-value"
                >{{ trafficMetrics.storageVeh }}veh</span
              >
            </div>
          </div>
        </div>
      </template>
    </div>

    <!-- 干线信息内容 -->
    <div v-else-if="tooltipType === 'trunkLine'" class="tooltip-info">
      <div><span>干线ID:</span> {{ actualTooltipData.route_id }}</div>
      <div><span>分类:</span> {{ actualTooltipData.classification }}</div>
      <div v-if="actualTooltipData.metadata?.roadCount">
        <span>包含路段:</span> {{ actualTooltipData.metadata.roadCount }} 条
      </div>
      <div v-if="actualTooltipData.metadata?.totalLength">
        <span>总长度:</span>
        {{ formatLength(actualTooltipData.metadata.totalLength) }}
      </div>

      <!-- 干线交通指标信息 -->
      <template v-if="showTrafficMetrics && trafficMetrics">
        <div class="traffic-metrics-section">
          <div class="metrics-header">
            <span class="metrics-title">交通指标</span>
            <div
              class="service-level-badge"
              :style="{
                backgroundColor: getServiceLevelColor(
                  trafficMetrics.serviceLevel
                ),
              }"
            >
              {{ trafficMetrics.serviceLevel }}级 -
              {{ getServiceLevelDescription(trafficMetrics.serviceLevel) }}
            </div>
          </div>
          <div class="metrics-content">
            <div class="metric-item">
              <span>平均行程速度:</span>
              <span class="metric-value"
                >{{ trafficMetrics.avgSpeed }}km/h</span
              >
            </div>
            <div class="metric-item">
              <span>一次通过率:</span>
              <span class="metric-value"
                >{{ trafficMetrics.onePassRate }}%</span
              >
            </div>
            <div class="metric-item">
              <span>绿波带宽:</span>
              <span class="metric-value"
                >{{ trafficMetrics.greenBandwidth }}s</span
              >
            </div>
            <div class="metric-item">
              <span>带宽利用率:</span>
              <span class="metric-value">{{
                trafficMetrics.bandwidthUtilization
              }}</span>
            </div>
            <div class="metric-item">
              <span>行程时间指数:</span>
              <span class="metric-value">{{ trafficMetrics.tti }}</span>
            </div>
            <div class="metric-item">
              <span>停车次数:</span>
              <span class="metric-value"
                >{{ trafficMetrics.stopsPerKm }}次/km</span
              >
            </div>
            <div class="metric-item">
              <span>协调周期:</span>
              <span class="metric-value">{{ trafficMetrics.cycle }}s</span>
            </div>
          </div>
        </div>
      </template>
    </div>

    <!-- 区域信息内容 -->
    <div v-else-if="tooltipType === 'area'" class="tooltip-info">
      <div>
        <span>区域名称:</span>
        {{ actualTooltipData.properties?.name || '未命名区域' }}
      </div>
      <div v-if="actualTooltipData.properties?.classification">
        <span>分类:</span>
        {{
          getAreaClassificationText(actualTooltipData.properties.classification)
        }}
      </div>
      <div v-if="actualTooltipData.properties?.description">
        <span>描述:</span> {{ actualTooltipData.properties.description }}
      </div>

      <!-- 区域交通指标信息 -->
      <template v-if="showTrafficMetrics && trafficMetrics">
        <div class="traffic-metrics-section">
          <div class="metrics-header">
            <span class="metrics-title">交通指标</span>
            <div
              class="service-level-badge"
              :style="{
                backgroundColor: getServiceLevelColor(
                  trafficMetrics.serviceLevel
                ),
              }"
            >
              {{ trafficMetrics.serviceLevel }}级 -
              {{ getServiceLevelDescription(trafficMetrics.serviceLevel) }}
            </div>
          </div>
          <div class="metrics-content">
            <div class="metric-item">
              <span>交通运行指数:</span>
              <span class="metric-value">{{ trafficMetrics.tpi }}</span>
            </div>
            <div class="metric-item">
              <span>拥堵里程比例:</span>
              <span class="metric-value"
                >{{ trafficMetrics.congestionMileageRatio }}%</span
              >
            </div>
            <div class="metric-item">
              <span>拥堵率:</span>
              <span class="metric-value">{{ trafficMetrics.tcr }}%</span>
            </div>
            <div class="metric-item">
              <span>时间可靠性指数:</span>
              <span class="metric-value">{{ trafficMetrics.tbi }}</span>
            </div>
          </div>
        </div>
      </template>
    </div>

    <!-- 设备操作按钮 -->
    <div v-if="tooltipType === 'device'" class="tooltip-buttons">
      <button @click="handleIntersectionMonitor">路口监控</button>
      <button @click="handleDeviceMonitor">设备监控</button>
      <button @click="handleRunningMonitor">运行监视</button>
    </div>

    <!-- 干线操作按钮已删除 -->
    <!-- 区域操作按钮已删除 -->
  </div>
</template>

<script setup>
import { computed } from 'vue'
import * as THREE from 'three'
import {
  getServiceLevelColor,
  getServiceLevelDescription,
} from '@/utils/trafficColors'

// 常量定义
const DEFAULT_POSITION = { left: '50px', top: '50px' }
const TOOLTIP_OFFSET = { x: 20, y: -10 }

// Props
const props = defineProps({
  // 通用属性
  tooltipType: {
    type: String,
    required: true,
    validator: value => ['device', 'trunkLine', 'area'].includes(value),
  },
  tooltipData: {
    type: Object,
    default: null,
  },
  object3d: {
    type: Object,
    default: null,
  },
  sceneManager: {
    type: Object,
    default: null,
  },

  // 鼠标位置（用于干线等线条对象的精确定位）
  mouseWorldPosition: {
    type: Object,
    default: null,
  },
  // 兼容旧的设备属性（向后兼容）
  deviceInfo: {
    type: Object,
    default: null,
  },
  device3d: {
    type: Object,
    default: null,
  },
  // 交通指标相关属性
  trafficMetrics: {
    type: Object,
    default: null,
  },
  showTrafficMetrics: {
    type: Boolean,
    default: false,
  },
})

// Emits
const emit = defineEmits([
  'close',
  'mouseEnter',
  'mouseLeave',
  // 设备相关事件
  'intersectionMonitor',
  'deviceMonitor',
  'runningMonitor',
  // 干线和区域相关事件已删除
])

// 兼容性处理：获取实际的数据和3D对象
const actualTooltipData = computed(() => {
  return props.tooltipData || props.deviceInfo
})

const actual3DObject = computed(() => {
  return props.object3d || props.device3d
})

// 计算提示框标题
const tooltipTitle = computed(() => {
  if (props.tooltipType === 'device') {
    return actualTooltipData.value?.name
  } else if (props.tooltipType === 'trunkLine') {
    return actualTooltipData.value?.route_name
  } else if (props.tooltipType === 'area') {
    return actualTooltipData.value?.properties?.name
  }
  return
})

// 计算提示框位置 - 基于3D对象位置
const tooltipStyle = computed(() => {
  const object3d = actual3DObject.value
  const { sceneManager } = props

  // 早期返回：缺少必要参数
  if (!object3d || !sceneManager?.camera || !sceneManager?.renderer) {
    return DEFAULT_POSITION
  }

  try {
    let worldPosition = new THREE.Vector3()

    // 根据对象类型计算位置
    if (props.tooltipType === 'trunkLine') {
      // 对于干线，优先使用鼠标位置，否则使用对象的世界位置
      if (props.mouseWorldPosition) {
        worldPosition.copy(props.mouseWorldPosition)
      } else {
        // 使用对象的世界位置作为备选
        object3d.getWorldPosition(worldPosition)
      }
    } else if (props.tooltipType === 'area') {
      // 对于区域对象，优先使用鼠标位置，否则使用对象的世界位置
      if (props.mouseWorldPosition) {
        worldPosition.copy(props.mouseWorldPosition)
      } else {
        // 使用对象的世界位置作为备选
        object3d.getWorldPosition(worldPosition)
      }
    } else {
      // 对于设备等点对象，直接获取世界坐标
      object3d.getWorldPosition(worldPosition)
    }

    // 将世界坐标投影到屏幕坐标
    worldPosition.project(sceneManager.camera)

    // 转换为屏幕像素坐标并添加边界检查
    const canvas = sceneManager.renderer.domElement
    const rawX = (worldPosition.x * 0.5 + 0.5) * canvas.clientWidth
    const rawY = (worldPosition.y * -0.5 + 0.5) * canvas.clientHeight

    // 边界检查，确保提示框不超出屏幕
    const x = Math.max(
      0,
      Math.min(rawX + TOOLTIP_OFFSET.x, canvas.clientWidth - 300)
    )
    const y = Math.max(
      0,
      Math.min(rawY + TOOLTIP_OFFSET.y, canvas.clientHeight - 200)
    )

    return {
      left: `${x}px`,
      top: `${y}px`,
    }
  } catch (error) {
    return DEFAULT_POSITION
  }
})

// 格式化长度显示
const formatLength = length => {
  if (length >= 1000) {
    return `${(length / 1000).toFixed(2)} km`
  }
  return `${length.toFixed(0)} m`
}

// 区域相关辅助函数
const getAreaClassificationText = classification => {
  const textMap = {
    A: '一级区域',
    B: '二级区域',
    C: '三级区域',
  }
  return textMap[classification] || classification
}

// 事件处理函数
const handleClose = () => {
  emit('close')
}

const handleMouseEnter = () => {
  emit('mouseEnter')
}

const handleMouseLeave = () => {
  emit('mouseLeave')
}

const handleIntersectionMonitor = () => {
  emit('intersectionMonitor', actualTooltipData.value)
}

const handleDeviceMonitor = () => {
  emit('deviceMonitor', actualTooltipData.value)
}

const handleRunningMonitor = () => {
  emit('runningMonitor', actualTooltipData.value)
}

// 干线和区域相关事件处理函数已删除
</script>

<style scoped lang="scss">
/* 通用悬停提示框样式 */
.universal-tooltip {
  background-color: rgba(18, 31, 63, 0.9);
  border: 1px solid rgba(79, 209, 197, 0.5);
  border-radius: 4px;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
  color: #ffffff;
  font-size: 12px;
  backdrop-filter: blur(4px);
  width: 260px;
  position: absolute;
  z-index: 1100; /* 提高层级，确保在stats-dashboard面板之上 */

  .tooltip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    border-bottom: 1px solid rgba(79, 209, 197, 0.3);
    padding-bottom: 4px;
  }

  .tooltip-title {
    font-weight: bold;
    font-size: 14px;
    color: #4fd1c5;
  }

  .tooltip-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
    cursor: pointer;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    padding: 0;
    border-radius: 50%;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: #ffffff;
    }
  }

  .tooltip-info {
    div {
      margin-bottom: 4px;

      span {
        color: #a0aec0;
        margin-right: 4px;
      }
    }
  }

  .tooltip-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    padding-top: 8px;
    border-top: 1px solid rgba(79, 209, 197, 0.3);

    button {
      background: rgba(79, 209, 197, 0.15);
      border: 1px solid rgba(79, 209, 197, 0.3);
      color: #4fd1c5;
      border-radius: 4px;
      padding: 4px 8px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: rgba(79, 209, 197, 0.3);
      }

      &:active {
        transform: translateY(1px);
      }
    }
  }

  // 交通指标样式
  .traffic-metrics-section {
    margin-top: 12px;
    padding-top: 8px;
    border-top: 1px solid rgba(79, 209, 197, 0.3);

    .metrics-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .metrics-title {
        color: #4fd1c5;
        font-weight: bold;
        font-size: 13px;
      }

      .service-level-badge {
        color: #000;
        font-weight: bold;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 3px;
        white-space: nowrap;
      }
    }

    .metrics-content {
      .metric-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 3px;
        padding: 1px 0;

        span:first-child {
          color: rgba(255, 255, 255, 0.8);
          font-size: 11px;
        }

        .metric-value {
          color: #4fd1c5;
          font-weight: 500;
          font-size: 11px;
        }
      }
    }
  }
}
</style>
