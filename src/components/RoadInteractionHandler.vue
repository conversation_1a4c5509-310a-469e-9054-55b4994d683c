<template>
  <!-- 自定义删除确认框 -->
  <a-modal
    v-model:open="showDeleteConfirm"
    title="确认删除道路"
    :ok-text="'确认删除'"
    :cancel-text="'取消'"
    :ok-button-props="{ danger: true }"
    @ok="confirmDelete"
    @cancel="cancelDelete"
    :mask-closable="false"
    :closable="false"
  >
    <div v-if="pendingRoadEvent">
      <p>
        确定要删除道路 <strong>"{{ pendingRoadEvent.roadName }}"</strong> 吗？
      </p>
      <p style="color: #ff4d4f; margin-top: 8px">
        ⚠️ 此操作将永久删除该道路，无法撤销。
      </p>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as THREE from 'three'
import { message } from 'ant-design-vue'

const props = defineProps({
  // Three.js 相关属性
  sceneManager: Object, // 场景管理器
  roadNetworkGroup: Object, // 路网组
  dynamicRoadLoader: Object, // 动态道路加载器
  picking: Object, // 统一拾取管线
})

const emit = defineEmits([
  'roadDelete', // 道路删除事件
])

// 内部状态
const isSceneInteracting = ref(false) // 场景交互状态

// 拖拽判定（左键）
const isMouseDown = ref(false)
const isDragging = ref(false)
const dragStartPos = ref({ x: 0, y: 0 })
const DRAG_THRESHOLD_PX = 5

// 构建道路事件数据的工具函数
const buildRoadEvent = (roadObject, originalEvent = null) => {
  const userData = roadObject.userData

  // 获取道路标识符
  const roadId =
    userData.featureId || userData.featureName || userData.id || userData.tempId
  const roadName = userData.name || userData.featureName || '未命名道路'

  // 计算鼠标在3D空间中的位置
  let mouseWorldPosition = null
  if (originalEvent && props.picking) {
    const { ndc } = props.picking.getNDCFromDOM(originalEvent)
    const hit = props.picking.pick(roadObject, {
      ndc,
      recursive: false,
      line2Threshold: 10,
    })
    if (hit.intersections.length > 0) {
      const p = hit.intersections[0].point
      mouseWorldPosition = new THREE.Vector3(p.x, p.y, p.z)
    }
  }

  return {
    roadId,
    roadName,
    object3D: roadObject,
    userData,
    mouseWorldPosition, // 鼠标在道路上的3D位置
    ...(originalEvent && { originalEvent }),
  }
}

// 检测道路交互（点击）
const checkRoadIntersection = event => {
  if (!props.picking) return null

  const allRoadGroups = []

  if (props.roadNetworkGroup && props.roadNetworkGroup.children.length > 0) {
    allRoadGroups.push(props.roadNetworkGroup)
  }

  if (props.dynamicRoadLoader && props.dynamicRoadLoader.renderedRoadGroups) {
    Object.values(props.dynamicRoadLoader.renderedRoadGroups).forEach(group => {
      if (group && group.children.length > 0) {
        allRoadGroups.push(group)
      }
    })
  }

  if (allRoadGroups.length === 0) return null

  const { ndc } = props.picking.getNDCFromDOM(event)

  // 依次检测各组，找到第一个有效道路对象
  for (const group of allRoadGroups) {
    const result = props.picking.pick(group, {
      ndc,
      recursive: true,
      line2Threshold: 10,
    })

    for (const intersect of result.intersections) {
      const obj = intersect.object
      if (
        obj.isLine2 ||
        obj.type === 'Line2' ||
        (obj.userData &&
          (obj.userData.featureId ||
            obj.userData.featureName ||
            obj.userData.id ||
            obj.userData.tempId ||
            obj.userData.isUserDrawnRoad))
      ) {
        return obj
      }
    }
  }

  return null
}

// 左键按下
const handleMouseDown = event => {
  // 仅处理左键
  if (event.button !== 0) return
  isMouseDown.value = true
  isDragging.value = false
  dragStartPos.value = { x: event.clientX, y: event.clientY }
  // 标记场景交互（拖拽开始可能发生）
  isSceneInteracting.value = true
}

// 鼠标移动（判定是否拖拽）
const handleMouseMove = event => {
  if (!isMouseDown.value) return
  const dx = event.clientX - dragStartPos.value.x
  const dy = event.clientY - dragStartPos.value.y
  if (
    !isDragging.value &&
    (Math.abs(dx) > DRAG_THRESHOLD_PX || Math.abs(dy) > DRAG_THRESHOLD_PX)
  ) {
    isDragging.value = true
  }
}

// 左键抬起
const handleMouseUp = event => {
  // 仅处理左键
  if (event.button !== 0) return

  // 结束交互标记
  isMouseDown.value = false
  const wasDragging = isDragging.value
  isDragging.value = false
  isSceneInteracting.value = false

  // 若发生拖拽，则认为是场景操作，不触发删除
  if (wasDragging) return

  // 点击判定：进行道路拾取
  const road = checkRoadIntersection(event)
  if (road) {
    // 准备删除事件数据并弹窗
    const roadEvent = buildRoadEvent(road, event)
    pendingRoadEvent.value = roadEvent
    showDeleteConfirm.value = true
  }
}

// 确认删除状态
const showDeleteConfirm = ref(false)
const pendingRoadEvent = ref(null)

// 确认删除
const confirmDelete = () => {
  if (pendingRoadEvent.value) {
    const roadName = pendingRoadEvent.value.roadName
    emit('roadDelete', pendingRoadEvent.value)

    // 清理状态
    pendingRoadEvent.value = null
    showDeleteConfirm.value = false
  }
}

// 取消删除
const cancelDelete = () => {
  if (pendingRoadEvent.value) {
    // 清理状态
    pendingRoadEvent.value = null
    showDeleteConfirm.value = false
  }
}

// 窗口失焦处理 - 清理所有状态
const handleWindowBlur = () => {
  // 清理删除确认状态
  if (showDeleteConfirm.value) {
    showDeleteConfirm.value = false
    pendingRoadEvent.value = null
  }

  // 重置交互状态
  isSceneInteracting.value = false
  isMouseDown.value = false
  isDragging.value = false
}

// 生命周期钩子
onMounted(() => {
  const container = props.sceneManager.getContainer()
  if (!container) {
    return
  }

  // 添加事件监听器（左键点击+拖拽判定）
  container.addEventListener('mousedown', handleMouseDown, false)
  container.addEventListener('mousemove', handleMouseMove, false)
  container.addEventListener('mouseup', handleMouseUp, false)

  // 添加窗口失焦事件监听器，确保在页面切换时清理状态
  window.addEventListener('blur', handleWindowBlur)
})

onBeforeUnmount(() => {
  // 清理所有状态
  if (showDeleteConfirm.value) {
    showDeleteConfirm.value = false
    pendingRoadEvent.value = null
  }

  isSceneInteracting.value = false
  isMouseDown.value = false
  isDragging.value = false

  // 移除场景容器事件监听器
  const container = props.sceneManager.getContainer()
  if (container) {
    container.removeEventListener('mousedown', handleMouseDown, false)
    container.removeEventListener('mousemove', handleMouseMove, false)
    container.removeEventListener('mouseup', handleMouseUp, false)
  }

  // 移除窗口事件监听器
  window.removeEventListener('blur', handleWindowBlur)
})
</script>
