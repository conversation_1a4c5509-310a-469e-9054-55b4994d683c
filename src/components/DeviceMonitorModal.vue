<template>
  <a-modal
    :visible="visible && !isLocationPickingActive"
    :title="'设备监控中心'"
    :width="1200"
    :footer="null"
    :maskClosable="true"
    :destroyOnClose="false"
    wrapClassName="device-monitor-modal"
    :bodyStyle="{ maxHeight: '90vh', overflow: 'hidden' }"
    @cancel="handleClose"
  >
    <a-spin :spinning="isLoading">
      <div class="modal-content-container">
        <!-- 左侧设备列表 -->
        <div class="device-list-container">
          <div class="list-header">
            <span>设备列表</span>
            <a-button
              type="primary"
              size="small"
              @click="handleOpenForm()"
              :loading="isLoading"
              >新增设备</a-button
            >
          </div>
          <div class="list-tip">点击设备项编辑设备信息</div>
          <div class="list-content">
            <div v-if="devices.length === 0" class="empty-state">
              <a-empty description="暂无监控设备，请新增" />
            </div>
            <div v-else class="device-list">
              <div
                v-for="device in devices"
                :key="device.edge_id"
                class="device-item"
                :class="{ selected: selectedDeviceId === device.edge_id }"
                @click="handleDeviceClick(device)"
              >
                <div class="device-info">
                  <span class="device-name">{{ device.edge_name }}</span>
                  <span class="device-ip">{{ device.ip_address }}</span>
                </div>

                <div class="actions">
                  <a-popconfirm
                    title="确定要删除此设备吗?"
                    ok-text="确定"
                    cancel-text="取消"
                    @confirm.stop="handleDeleteDevice(device.edge_id)"
                  >
                    <a-button type="link" danger size="small" @click.stop
                      >删除</a-button
                    >
                  </a-popconfirm>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧表单区域 -->
        <div class="device-form-container">
          <EdgeDeviceForm
            ref="deviceFormRef"
            :visible="isFormVisible"
            :device="editingDevice"
            :is-loading="isSubmitting"
            :selected-coordinates="selectedCoordinates"
            @submit="handleFormSubmit"
            @cancel="handleFormCancel"
            @location-pick="handleLocationPick"
          />
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script setup>
import {
  ref,
  watch,
  onMounted,
  onBeforeUnmount,
  nextTick,
  onUnmounted,
} from 'vue'
import { useDevicesStore } from '@s2/stores/devices'
import { useChannelizationStore } from '@s2/stores/channelization' // 渠化图存储用于禁用/启用自动保存
import { storeToRefs } from 'pinia'
import { message } from 'ant-design-vue'
import EdgeDeviceForm from './EdgeDeviceForm.vue'
import eventBus from '@/utils/eventBus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  deviceInfo: Object,
})

const emit = defineEmits(['update:visible', 'close', 'focus-on-device'])

// 渠化图存储 - 恢复此引用以确保API调用正常
const channelizationStore = useChannelizationStore()

// 设备管理状态变量
const isFormVisible = ref(false)
const isSubmitting = ref(false)
const editingDevice = ref(null)
const selectedDeviceId = ref(null)
const isLocationPickingActive = ref(false)
const deviceFormRef = ref(null)
const selectedCoordinates = ref(null)
const tempFormState = ref(null)
const shouldRestoreModal = ref(false)

// 加载设备列表
const devicesStore = useDevicesStore()
const { devices, isLoading } = storeToRefs(devicesStore)

// 监听可见性变化
watch(
  () => props.visible,
  newVisible => {
    if (newVisible) {
      // 当模态框打开时，只在设备列表为空且未初始化时才获取设备列表
      if (devices.value.length === 0 && !devicesStore.initialized) {
        devicesStore.fetchDevices()
      }
    } else if (!isLocationPickingActive.value) {
      // 当模态框关闭且非位置选择模式时，重置状态
      resetComponentState()
    }
  }
)

// 重置组件所有状态到初始值
const resetComponentState = () => {
  isFormVisible.value = false
  isSubmitting.value = false
  editingDevice.value = null
  selectedDeviceId.value = null
  isLocationPickingActive.value = false
  selectedCoordinates.value = null
  tempFormState.value = null
  shouldRestoreModal.value = false
}

// 关闭对话框处理
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

// 设备表单相关方法
// 打开表单处理
const handleOpenForm = (device = null) => {
  editingDevice.value = device
  isFormVisible.value = true
}

// 关闭表单处理
const handleFormCancel = () => {
  handleClose()
}

// 表单提交处理
const handleFormSubmit = async formData => {
  isSubmitting.value = true
  try {
    if (editingDevice.value) {
      // 编辑模式
      await devicesStore.updateDevice(formData)
      message.success(`设备 ${formData.edge_name} 更新成功`)
    } else {
      // 新增模式
      await devicesStore.addDevice(formData)
      message.success(`设备 ${formData.edge_name} 添加成功`)
    }
    isFormVisible.value = false
    editingDevice.value = null
  } catch (error) {
    message.error(error.message || '操作失败')
  } finally {
    isSubmitting.value = false
  }
}

// 删除设备处理
const handleDeleteDevice = async edgeId => {
  // 如果删除的是当前选中的设备，清除选中状态
  if (selectedDeviceId.value === edgeId) {
    selectedDeviceId.value = null
  }

  try {
    await devicesStore.deleteDevice(edgeId)
    message.info(`设备已删除`)
  } catch (error) {
    message.error(error.message || '删除失败')
  }
}

// 设备点击处理
const handleDeviceClick = device => {
  selectedDeviceId.value = device.edge_id
  emit('focus-on-device', device)
  // 直接打开编辑表单
  handleOpenForm(device)
}

// 处理位置选择事件
const handleLocationPick = isActive => {
  isLocationPickingActive.value = isActive

  // 当位置选择模式开始时，保存表单状态并关闭模态框
  if (isActive) {
    // 保存表单状态
    if (deviceFormRef.value) {
      tempFormState.value = deviceFormRef.value.getFormState()
    }
    shouldRestoreModal.value = true
  }
}

onMounted(() => {
  // 禁用渠化图自动保存，防止监控界面修改配置
  channelizationStore.disableAutoSave()

  // 监听位置选择事件，保存坐标到父组件
  eventBus.on('location-selected', handleLocationSelected)
})

// 组件卸载前的清理
onBeforeUnmount(() => {
  // 恢复渠化图自动保存功能
  channelizationStore.enableAutoSave()
})

onUnmounted(() => {
  eventBus.off('location-selected')
})

// 处理位置选择结果
const handleLocationSelected = coordinates => {
  selectedCoordinates.value = coordinates

  // 同时更新临时表单状态中的坐标
  if (tempFormState.value) {
    tempFormState.value.edge_coord = { ...coordinates }

    // 如果路口坐标未设置，也一并更新
    if (
      tempFormState.value.jnc_coord.lng === 0 &&
      tempFormState.value.jnc_coord.lat === 0
    ) {
      tempFormState.value.jnc_coord = { ...coordinates }
    }
  }

  // 位置选择完成后，恢复显示模态框
  if (shouldRestoreModal.value) {
    setTimeout(() => {
      isLocationPickingActive.value = false
      isFormVisible.value = true

      // 在下一个tick中更新表单数据
      nextTick(() => {
        if (deviceFormRef.value && tempFormState.value) {
          deviceFormRef.value.setFormState(tempFormState.value)
        }
      })
    }, 200)
    shouldRestoreModal.value = false
  }
}
</script>

<style lang="scss" scoped>
/* 设备管理模式的样式 */
.modal-content-container {
  display: flex;
  height: 70vh;
  gap: 16px;
}

.device-list-container {
  width: 300px;
  border-right: 1px solid rgba(79, 209, 197, 0.3);
  display: flex;
  flex-direction: column;
}

.device-form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  margin-bottom: 12px;
  font-weight: 600;
}

.list-tip {
  font-size: 12px;
  color: #a0aec0;
  margin-bottom: 12px;
}

.list-content {
  flex: 1;
  overflow-y: auto;
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  border: 1px solid rgba(79, 209, 197, 0.3);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  &:hover {
    border-color: #4fd1c5;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.selected {
    border-color: #4fd1c5;
    box-shadow: 0 0 8px rgba(79, 209, 197, 0.5);
    background-color: rgba(79, 209, 197, 0.1);
  }
}

.device-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex-grow: 1;
  min-width: 0; // 允许 flex item 收缩
}

.device-name {
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.device-ip {
  font-size: 12px;
  color: #a0aec0; // 辅助文字颜色
  white-space: nowrap;
}

.actions {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0; // 防止按钮被压缩
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
