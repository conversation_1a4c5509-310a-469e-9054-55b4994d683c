<template></template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { throttle } from 'lodash'
import * as THREE from 'three'

const props = defineProps({
  // Three.js 相关属性
  sceneManager: Object, // 场景管理器
  deviceMarkersGroup: Object, // 设备标记组
  picking: Object, // 统一拾取管线
})

const emit = defineEmits([
  'deviceLeftClick', // 设备左键点击事件
  'deviceRightClick', // 设备右键点击事件
  'deviceHover', // 设备悬停事件
  'deviceHoverEnd', // 设备悬停结束事件
])

// 内部状态
const hoveredDevice = ref(null) // 当前鼠标悬停的设备
const isSceneInteracting = ref(false) // 场景交互状态

// 鼠标移动优化相关
const lastMousePosition = ref({ x: 0, y: 0 })
const MOUSE_MOVE_THRESHOLD = 3 // 像素阈值，小于此值不触发检测

// 构建设备事件数据的工具函数
const buildDeviceEvent = (device, originalEvent = null) => ({
  device: device.userData.feature,
  object3D: device,
  ...(originalEvent && { originalEvent }),
})

// 检测设备交互（点击或悬停）- 基于统一 picking
const checkDeviceIntersection = event => {
  if (!props.sceneManager?.getCamera() || !props.deviceMarkersGroup) return null
  if (!props.picking) return null

  const { ndc } = props.picking.getNDCFromDOM(event)
  const result = props.picking.pick(props.deviceMarkersGroup, {
    ndc,
    recursive: true,
  })

  if (result.intersections.length > 0) {
    // 找到包含 feature 的对象（向上查找）
    let obj = result.intersections[0].object
    while (obj && !obj.userData?.feature) {
      obj = obj.parent
    }
    if (obj && obj.userData?.feature) {
      return obj
    }
  }
  return null
}

// 清除悬停状态
const clearHoverState = () => {
  if (hoveredDevice.value) {
    emit('deviceHoverEnd', buildDeviceEvent(hoveredDevice.value))
    hoveredDevice.value = null
  }
}

// 鼠标事件处理
const handleMouseDown = () => {
  isSceneInteracting.value = false
}

// 优化的鼠标移动处理函数，添加移动阈值检测
const handleMouseMoveInternal = event => {
  if (event.buttons > 0) {
    isSceneInteracting.value = true
    clearHoverState()
    return
  }

  // 检查鼠标移动距离是否超过阈值
  const deltaX = Math.abs(event.clientX - lastMousePosition.value.x)
  const deltaY = Math.abs(event.clientY - lastMousePosition.value.y)
  const totalDelta = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

  // 如果移动距离小于阈值，不进行检测
  if (totalDelta < MOUSE_MOVE_THRESHOLD) {
    return
  }

  // 更新最后鼠标位置
  lastMousePosition.value = { x: event.clientX, y: event.clientY }

  // 检测鼠标悬停在设备上
  const device = checkDeviceIntersection(event)

  if (device) {
    // 如果设备变化了
    if (device !== hoveredDevice.value) {
      clearHoverState()

      // 触发新设备悬停事件
      emit('deviceHover', buildDeviceEvent(device, event))
      hoveredDevice.value = device
    }
  } else if (hoveredDevice.value) {
    // 没有检测到设备，清除悬停状态
    clearHoverState()
  }
}

// 使用节流优化的鼠标移动处理函数
const handleMouseMove = throttle(handleMouseMoveInternal, 16) // 约60fps

const handleMouseUp = event => {
  if (isSceneInteracting.value) {
    isSceneInteracting.value = false
    return
  }

  // 只处理左键点击
  if (event.button === 0) {
    const device = checkDeviceIntersection(event)
    if (device) {
      emit('deviceLeftClick', buildDeviceEvent(device, event))
    }
  }
}

// 右键点击处理，只在点击到设备时阻止默认行为
const handleContextMenu = event => {
  const device = checkDeviceIntersection(event)
  if (device) {
    event.preventDefault()
    emit('deviceRightClick', buildDeviceEvent(device, event))
  }
}

// 鼠标离开场景容器时，发送悬停结束事件
const handleMouseLeave = () => {
  clearHoverState()
}

// 生命周期钩子
onMounted(() => {
  const container = props.sceneManager?.getContainer()
  if (container) {
    container.addEventListener('mousedown', handleMouseDown)
    container.addEventListener('mousemove', handleMouseMove)
    container.addEventListener('mouseup', handleMouseUp)
    container.addEventListener('contextmenu', handleContextMenu)
    container.addEventListener('mouseleave', handleMouseLeave)
  }
})

onBeforeUnmount(() => {
  const container = props.sceneManager?.getContainer()
  if (container) {
    container.removeEventListener('mousedown', handleMouseDown)
    container.removeEventListener('mousemove', handleMouseMove)
    container.removeEventListener('mouseup', handleMouseUp)
    container.removeEventListener('contextmenu', handleContextMenu)
    container.removeEventListener('mouseleave', handleMouseLeave)
  }

  // 确保清除任何残留的悬停状态
  clearHoverState()
})
</script>

<style scoped></style>
