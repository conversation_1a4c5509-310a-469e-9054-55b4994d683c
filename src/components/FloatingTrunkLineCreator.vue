<template>
  <div
    v-if="visible"
    class="floating-trunk-line-creator"
    :style="panelStyle"
    @mousedown="handleMouseDown"
  >
    <div class="creator-panel">
      <div ref="headerRef" class="panel-header">
        <h4>{{ editMode ? '编辑干线' : '创建干线' }}</h4>
        <a-button
          type="text"
          size="small"
          class="close-btn"
          @click="handleCancel"
        >
          <template #icon><CloseOutlined /></template>
        </a-button>
      </div>

      <div class="panel-content">
        <!-- 干线基本信息 -->
        <div class="form-section">
          <div v-if="editMode" class="form-item">
            <label>干线ID:</label>
            <a-input v-model:value="trunkLineId" readonly size="small" />
          </div>

          <div class="form-item">
            <label>干线名称:</label>
            <a-input
              v-model:value="trunkLineName"
              placeholder="请输入干线名称"
              size="small"
              @keyup.enter="handleGenerate"
            />
          </div>

          <div class="form-item">
            <label>干线分类:</label>
            <a-select
              v-model:value="trunkLineClassification"
              size="small"
              style="width: 120px"
              :get-popup-container="triggerNode => triggerNode.parentNode"
              placement="bottomLeft"
            >
              <a-select-option value="主要干线">主要干线</a-select-option>
              <a-select-option value="次要干线">次要干线</a-select-option>
              <a-select-option value="支线">支线</a-select-option>
            </a-select>
          </div>
        </div>

        <!-- 选择提示 -->
        <div class="selection-hint">
          <div class="hint-text">
            <InfoCircleOutlined />
            <span>在地图上点击路线来选择干线路径</span>
          </div>
          <div class="selection-count">
            已选择 {{ selectedRoadIds.length }} 条路线
          </div>
        </div>

        <!-- 已选路线列表 -->
        <div v-if="selectedRoadIds.length > 0" class="selected-roads">
          <div class="section-title">已选路线:</div>
          <div class="selected-roads-list">
            <a-tag
              v-for="roadId in selectedRoadIds"
              :key="roadId"
              closable
              size="small"
              class="road-tag"
              @close="removeRoadSelection(roadId)"
            >
              {{ getRoadName(roadId) }}
            </a-tag>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <a-button
            type="primary"
            size="small"
            :disabled="!canGenerate || isCreating"
            :loading="isCreating"
            class="generate-btn"
            @click="handleGenerate"
          >
            {{
              isCreating
                ? editMode
                  ? '保存中...'
                  : '创建中...'
                : editMode
                ? '保存修改'
                : '生成干线'
            }}
          </a-button>
          <a-button
            size="small"
            :disabled="isCreating"
            class="cancel-btn"
            @click="handleCancel"
          >
            取消
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { CloseOutlined, InfoCircleOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { useTrunkLinesStore } from '@/stores/trunkLines'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  selectedRoadIds: {
    type: Array,
    default: () => [],
  },
  availableRoads: {
    type: Array,
    default: () => [],
  },
  roadNetworkData: {
    type: Object,
    default: null,
  },
  dynamicRoadLoader: {
    type: Object,
    default: null,
  },
  editMode: {
    type: Boolean,
    default: false,
  },
  editingTrunkLine: {
    type: Object,
    default: null,
  },
})

// Emits
const emit = defineEmits(['close', 'remove-road-selection'])

// Store
const trunkLinesStore = useTrunkLinesStore()

// 响应式数据
const trunkLineName = ref('')
const trunkLineClassification = ref('主要干线')
const isCreating = ref(false)
const trunkLineId = ref('')

// 拖拽相关数据
const headerRef = ref(null)
const panelPosition = ref({ x: 0, y: 0 })
const isDragging = ref(false)
const dragOffset = ref({ x: 0, y: 0 })

// 计算属性
const canGenerate = computed(() => {
  return trunkLineName.value.trim() && props.selectedRoadIds.length > 0
})

// 面板样式计算属性
const panelStyle = computed(() => {
  return {
    transform: `translate(${panelPosition.value.x}px, ${panelPosition.value.y}px)`,
    cursor: isDragging.value ? 'grabbing' : 'default',
  }
})

// 方法
const getRoadName = roadId => {
  const road = props.availableRoads.find(r => r.id === roadId)
  return road?.name || `路线 ${roadId}`
}

const removeRoadSelection = roadId => {
  emit('remove-road-selection', roadId)
}

// 生成干线
const handleGenerate = async () => {
  if (!canGenerate.value) {
    message.warning('请填写干线名称并选择路段')
    return
  }

  if (isCreating.value) {
    return // 防止重复提交
  }

  try {
    isCreating.value = true

    if (props.editMode && props.editingTrunkLine) {
      // 编辑模式，调用store的保存方法
      const updateData = {
        route_id: trunkLineId.value,
        route_name: trunkLineName.value.trim(),
        classification: trunkLineClassification.value,
        selectedRoadIds: [...props.selectedRoadIds],
      }
      const result = await trunkLinesStore.saveEditedTrunkLine(updateData)
      if (result) {
        message.success(`干线 "${trunkLineName.value}" 修改成功！`)
        handleCancel()
      }
    } else {
      // 创建模式
      trunkLinesStore.creatingTrunkLine.route_name = trunkLineName.value.trim()
      trunkLinesStore.creatingTrunkLine.classification =
        trunkLineClassification.value
      trunkLinesStore.creatingTrunkLine.selectedRoadIds = [
        ...props.selectedRoadIds,
      ]
      if (!props.roadNetworkData && !props.dynamicRoadLoader) {
        message.error('缺少路网数据，无法创建干线')
        return
      }
      const result = await trunkLinesStore.createTrunkLine(
        props.roadNetworkData,
        props.dynamicRoadLoader,
        {
          description: `从 ${props.selectedRoadIds.length} 条路段创建的干线`,
          tags: ['用户创建'],
        }
      )
      if (result) {
        handleCancel()
      }
    }
  } catch (error) {
    message.error(
      (props.editMode ? '修改' : '创建') +
        '干线失败: ' +
        (error.message || '未知错误')
    )
  } finally {
    isCreating.value = false
  }
}

const handleCancel = () => {
  // 如果是编辑模式，需要取消store中的编辑状态
  if (props.editMode) {
    trunkLinesStore.cancelEditing()
  }
  handleReset()
  emit('close')
}

const handleReset = () => {
  // 精简重置逻辑
  trunkLineName.value = ''
  trunkLineClassification.value = '主要干线'
  isCreating.value = false
}

// 拖拽相关方法
const initializePosition = () => {
  // 默认位置在右边，距离右边缘 20px，垂直居中
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight
  const panelWidth = 350 // 面板宽度
  const panelHeight = 400 // 估算面板高度

  panelPosition.value = {
    x: windowWidth - panelWidth - 20,
    y: (windowHeight - panelHeight) / 2,
  }
}

const handleMouseDown = event => {
  // 只有点击头部区域才能拖拽
  if (!headerRef.value?.contains(event.target)) {
    return
  }

  isDragging.value = true

  // 计算鼠标相对于面板的偏移
  const rect = event.currentTarget.getBoundingClientRect()
  dragOffset.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top,
  }

  // 添加全局事件监听
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)

  // 阻止默认行为
  event.preventDefault()
}

const handleMouseMove = event => {
  if (!isDragging.value) {
    return
  }

  // 计算新位置
  const newX = event.clientX - dragOffset.value.x
  const newY = event.clientY - dragOffset.value.y

  // 限制面板在窗口范围内
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight
  const panelWidth = 350
  const panelHeight = 400

  panelPosition.value = {
    x: Math.max(0, Math.min(newX, windowWidth - panelWidth)),
    y: Math.max(0, Math.min(newY, windowHeight - panelHeight)),
  }
}

const handleMouseUp = () => {
  isDragging.value = false

  // 移除全局事件监听
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

// 监听visible变化，重置表单
watch(
  () => props.visible,
  (newVal, oldVal) => {
    if (newVal) {
      // 面板显示时重置表单并初始化位置
      handleReset()
      initializePosition()
    } else if (oldVal) {
      // 面板关闭时也重置表单
      handleReset()
    }
  }
)

// 监听props.editingTrunkLine变化，自动填充表单
watch(
  () => props.editingTrunkLine,
  val => {
    if (props.editMode && val) {
      trunkLineId.value = val.route_id || ''
      trunkLineName.value = val.route_name || ''
      trunkLineClassification.value = val.classification || '主要干线'
    }
  },
  { immediate: true }
)

// 生命周期
onMounted(() => {
  initializePosition()
})

onBeforeUnmount(() => {
  // 清理事件监听
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
})
</script>

<style lang="scss" scoped>
.floating-trunk-line-creator {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10000;
  user-select: none;

  .creator-panel {
    background: rgba(15, 34, 58, 0.95);
    border: 2px solid rgba(79, 209, 197, 0.5);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    width: 360px;
    max-height: 80vh;
    overflow: visible; /* 改为visible，让下拉菜单可以显示在面板外 */

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid rgba(79, 209, 197, 0.3);
      background: rgba(79, 209, 197, 0.1);
      border-radius: 12px 12px 0 0;
      cursor: grab;

      &:active {
        cursor: grabbing;
      }

      h4 {
        margin: 0;
        color: #4fd1c5;
        font-size: 18px;
        font-weight: 600;
        pointer-events: none; // 防止标题文字干扰拖拽
      }

      .close-btn {
        color: #4fd1c5;
        cursor: pointer; // 确保关闭按钮有正确的鼠标样式

        &:hover {
          background: rgba(79, 209, 197, 0.2);
        }
      }
    }

    .panel-content {
      padding: 20px;
      max-height: calc(80vh - 80px); /* 减去header高度 */
      overflow-y: auto; /* 只在内容区域滚动 */

      .form-section {
        margin-bottom: 20px;

        .form-item {
          margin-bottom: 16px;

          label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #ffffff;
            font-weight: 500;
          }

          /* 确保选择器下拉菜单在最顶层 */
          :deep(.ant-select-dropdown) {
            z-index: 10001 !important;
          }
        }
      }

      .selection-hint {
        background: rgba(79, 209, 197, 0.1);
        border: 1px solid rgba(79, 209, 197, 0.3);
        border-radius: 6px;
        padding: 12px;
        margin-bottom: 20px;

        .hint-text {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #a8a8a8;
          font-size: 14px;
          margin-bottom: 4px;
        }

        .selection-count {
          font-size: 14px;
          color: #4fd1c5;
          font-weight: 600;
        }
      }

      .selected-roads {
        margin-bottom: var(--spacing-md);

        .section-title {
          font-size: var(--font-size-sm);
          color: var(--color-text-secondary);
          margin-bottom: var(--spacing-xs);
        }

        .selected-roads-list {
          display: flex;
          flex-wrap: wrap;
          gap: var(--spacing-xs);

          .road-tag {
            font-size: var(--font-size-xs);
          }
        }
      }

      .action-buttons {
        display: flex;
        gap: var(--spacing-sm);
        justify-content: flex-end;

        .generate-btn {
          background: var(--color-primary);
          border-color: var(--color-primary);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .floating-trunk-line-creator {
    .creator-panel {
      width: 90vw;
      max-width: 360px;
    }
  }
}
</style>
