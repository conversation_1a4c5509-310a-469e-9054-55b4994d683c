<template>
  <StatsDashboard
    title="设备列表"
    :position="position"
    :value="`${devices.length}台`"
    :show-value="false"
  >
    <template #content>
      <div class="device-list">
        <div v-if="devices.length === 0" class="no-devices">暂无设备数据</div>
        <div v-else class="device-table">
          <div class="device-row header">
            <div class="device-cell">设备ID</div>
            <div class="device-cell">设备名称</div>
            <div class="device-cell">路口名称</div>
          </div>
          <!-- 性能优化：使用虚拟滚动处理大量设备数据 -->
          <div class="device-list-container" ref="deviceListContainer">
            <div
              v-for="device in visibleDevices"
              :key="device.edge_id"
              class="device-row"
              style="cursor: pointer"
              @click="handleDeviceClick(device)"
            >
              <div class="device-cell">{{ device.edge_id }}</div>
              <div class="device-cell">{{ device.edge_name }}</div>
              <div class="device-cell">
                {{ device.jnc_name }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </StatsDashboard>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import StatsDashboard from '@/components/StatsDashboard.vue'

// Props
const props = defineProps({
  devices: {
    type: Array,
    default: () => [],
  },
  position: {
    type: Object,
    default: () => ({
      top: '13vh',
      left: '20px',
    }),
  },
})

// Emits
const emit = defineEmits(['deviceClick'])

// 虚拟滚动相关状态
const deviceListContainer = ref(null)
const scrollTop = ref(0)
const containerHeight = ref(200) // 容器高度
const itemHeight = 32 // 每个设备行的高度
const visibleCount = computed(
  () => Math.ceil(containerHeight.value / itemHeight) + 2
) // 可见项数量 + 缓冲

// 计算可见的设备列表
const visibleDevices = computed(() => {
  // 如果设备数量较少，直接返回全部
  if (props.devices.length <= 20) {
    return props.devices
  }

  const startIndex = Math.floor(scrollTop.value / itemHeight)
  const endIndex = Math.min(
    startIndex + visibleCount.value,
    props.devices.length
  )

  return props.devices.slice(startIndex, endIndex)
})

// 滚动事件处理
const handleScroll = event => {
  scrollTop.value = event.target.scrollTop
}

// 事件处理函数
const handleDeviceClick = device => {
  emit('deviceClick', device)
}

// 组件挂载时设置滚动监听
onMounted(() => {
  if (deviceListContainer.value) {
    deviceListContainer.value.addEventListener('scroll', handleScroll, {
      passive: true,
    })

    // 获取容器实际高度
    const rect = deviceListContainer.value.getBoundingClientRect()
    containerHeight.value = rect.height
  }
})

// 组件卸载时清理监听器
onUnmounted(() => {
  if (deviceListContainer.value) {
    deviceListContainer.value.removeEventListener('scroll', handleScroll)
  }
})

// 监听设备数据变化，重置滚动位置
watch(
  () => props.devices,
  () => {
    scrollTop.value = 0
    if (deviceListContainer.value) {
      deviceListContainer.value.scrollTop = 0
    }
  }
)
</script>

<style scoped lang="scss">
/* 设备列表表格样式 */
.device-list {
  height: 100%; // 填满容器高度
  overflow-y: auto;
  padding: 0 4px;
  display: flex;
  flex-direction: column;
}

.no-devices {
  display: flex;
  justify-content: center;
  align-items: center;
  height: clamp(40px, 8vh, 100px); // 动态高度：最小40px，最大100px
  color: #a0aec0;
  font-size: clamp(9px, 1vw, 12px); // 减小字体
}

.device-table {
  width: 100%;
  border-collapse: collapse;
}

.device-list-container {
  flex: 1; // 占据剩余空间
  overflow-y: auto;
  scroll-behavior: smooth;

  // 优化滚动性能
  will-change: scroll-position;
  -webkit-overflow-scrolling: touch;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(79, 209, 197, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(79, 209, 197, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(79, 209, 197, 0.5);
    }
  }
}

.device-row {
  display: flex;
  border-bottom: 1px solid rgba(79, 209, 197, 0.2);

  &.header {
    font-weight: 600;
    color: #4fd1c5;
    border-bottom: 2px solid rgba(79, 209, 197, 0.5);

    .device-cell {
      font-size: clamp(8px, 0.9vw, 12px); // 表头字体稍大一点但仍然紧凑
      font-weight: 600;
    }
  }

  &.more {
    text-align: center;
    color: #a0aec0;
    font-size: clamp(8px, 0.8vw, 11px); // 减小字体
  }
}

.device-cell {
  flex: 1;
  padding: clamp(1px, 0.3vh, 3px) clamp(1px, 0.3vw, 2px); // 减小内边距
  font-size: clamp(7px, 0.8vw, 11px); // 进一步减小字体
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.1; // 减小行高，更紧凑
}
</style>
