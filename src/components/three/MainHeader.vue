<template>
  <header class="main-header">
    <div class="header-content">
      <div class="left-section">
        <router-link v-if="showBackButton" to="/" class="back-button">
          <left-outlined /> 返回主页
        </router-link>
        <h1 class="system-title">
          {{ customTitle || '智能交通信号边缘设施统一管控系统' }}
        </h1>
      </div>

      <div class="nav-buttons" v-if="!hideNavButtons">
        <button
          class="transparent-button"
          @click="handleNavigation('intersection-monitor')"
        >
          路口监控
        </button>
        <button
          class="transparent-button"
          @click="handleNavigation('device-monitor')"
        >
          设备监控
        </button>
        <button
          class="transparent-button"
          @click="handleNavigation('running-monitor')"
        >
          运行监视
        </button>
        <a-dropdown overlay-class-name="quick-config-menu" :trigger="['click']">
          <a class="transparent-button" @click.prevent> 地图配置 </a>
          <template #overlay>
            <a-menu @click="handleMapConfigMenuClick">
              <a-menu-item key="data">数据管理</a-menu-item>
              <a-menu-item key="drawing">绘制工具</a-menu-item>
              <a-menu-item key="icons">图标管理</a-menu-item>
              <a-menu-item key="trunklines">干线管理</a-menu-item>
              <a-menu-item key="regions">区域管理</a-menu-item>
              <a-menu-item key="settings">显示设置</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <button
          class="transparent-button"
          @click="handleNavigation('device-config')"
        >
          设备管理
        </button>
        <a-dropdown overlay-class-name="quick-config-menu" :trigger="['click']">
          <a class="transparent-button" @click.prevent> 设备配置 </a>
          <template #overlay>
            <a-menu @click="handleMenuClick">
              <a-menu-item key="network">基础信息</a-menu-item>
              <a-menu-item key="detector-config">检测器</a-menu-item>
              <a-menu-item key="phase-detector-config">相位</a-menu-item>
              <a-menu-item key="stage-pool">阶段</a-menu-item>
              <a-menu-item key="service-config">方案</a-menu-item>
              <a-menu-item key="schedules">调度表</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>

      <div class="right-section">
        <div class="user-controls" v-if="showUserControls">
          <span class="username">{{ username }}</span>
          <a-dropdown>
            <a class="user-menu" @click.prevent>
              <user-outlined />
              <down-outlined />
            </a>
            <template #overlay>
              <a-menu>
                <a-menu-item key="settings">
                  <setting-outlined /> 设置
                </a-menu-item>
                <a-menu-item
                  key="change-password"
                  @click="showChangePasswordModal = true"
                >
                  <key-outlined /> 修改密码
                </a-menu-item>
                <a-menu-item key="logout" @click="handleLogout">
                  <logout-outlined /> 退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
    </div>

    <!-- 修改密码模态框 -->
    <ChangePasswordModal v-model:visible="showChangePasswordModal" />
    <!-- 设备配置模态框 -->
    <DeviceSettingsModal
      v-model:visible="deviceSettingsModalVisible"
      :device="selectedDeviceForModal"
      :initial-tab="modalInitialTab"
      @close="deviceSettingsModalVisible = false"
    />
  </header>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  LeftOutlined,
  UserOutlined,
  DownOutlined,
  SettingOutlined,
  LogoutOutlined,
  KeyOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { removeToken } from '@/utils/auth'
import { logout } from '@/api/auth'
import ChangePasswordModal from '@/components/ChangePasswordModal.vue'
import DeviceSettingsModal from '@/components/DeviceSettingsModal.vue'
import { useSceneStateStore } from '@/stores/sceneState'
import { useDevicesStore } from '@/stores/devices'
import { storeToRefs } from 'pinia'

// 定义props
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  showBackButton: {
    type: Boolean,
    default: false,
  },
  hideNavButtons: {
    type: Boolean,
    default: false,
  },
  showUserControls: {
    type: Boolean,
    default: true,
  },
})

const router = useRouter()
const sceneStateStore = useSceneStateStore()
const { selectedDevice } = storeToRefs(sceneStateStore)
const deviceStore = useDevicesStore()
const { devices } = storeToRefs(deviceStore)

// 设备配置模态框状态
const deviceSettingsModalVisible = ref(false)
const selectedDeviceForModal = ref(null)
const modalInitialTab = ref('network')

// 计算标题
const customTitle = computed(() => props.title || '')

// 从localStorage获取用户名，如果没有则显示默认值
const username = ref('管理员')

// 修改密码模态框显示状态
const showChangePasswordModal = ref(false)

// 处理快捷配置菜单点击
const handleMenuClick = ({ key }) => {
  let targetDevice = selectedDevice.value

  // 如果没有在场景中选中设备，则默认使用列表的第一个设备
  if (!targetDevice) {
    if (devices.value && devices.value.length > 0) {
      targetDevice = devices.value[0]
    } else {
      message.warning('设备列表为空，无法打开配置')
      return
    }
  }

  selectedDeviceForModal.value = targetDevice
  modalInitialTab.value = key
  deviceSettingsModalVisible.value = true
}

// 处理登出逻辑
const handleLogout = async () => {
  try {
    // 调用登出API
    const response = await logout()

    // 检查API返回结果
    if (response.returnCode === 0) {
      removeToken()
      message.success('已退出登录')
      router.push('/login')
    } else {
      // API返回失败，但仍然清除本地token
      removeToken()
      message.warning('服务器登出失败，但已清除本地登录信息')
      router.push('/login')
    }
  } catch (error) {
    console.error('登出失败:', error)
    // 即使API调用失败，也清除本地token
    removeToken()
    message.warning('登出遇到问题，但已清除本地登录信息')
    router.push('/login')
  }
}

// 新增：处理地图配置菜单点击
const handleMapConfigMenuClick = ({ key }) => {
  emit('map-config-click', key)
}

// 处理导航按钮点击
const handleNavigation = route => {
  // 直接触发导航事件
  emit('nav-click', route)
}

// 定义事件
const emit = defineEmits(['nav-click', 'map-config-click'])
</script>

<style scoped>
.main-header {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  pointer-events: none;
  z-index: 20;
  flex-shrink: 0;
  background: linear-gradient(
      135deg,
      rgba(8, 15, 25, 0.98) 0%,
      rgba(12, 20, 35, 0.96) 25%,
      rgba(18, 28, 45, 0.94) 50%,
      rgba(15, 25, 40, 0.96) 75%,
      rgba(10, 18, 30, 0.98) 100%
    ),
    radial-gradient(
      ellipse 600px 150px at 20% 50%,
      rgba(0, 120, 255, 0.15) 0%,
      rgba(0, 150, 255, 0.08) 40%,
      transparent 70%
    ),
    radial-gradient(
      ellipse 500px 120px at 80% 50%,
      rgba(0, 255, 180, 0.12) 0%,
      rgba(0, 200, 150, 0.06) 40%,
      transparent 70%
    ),
    linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 150, 255, 0.03) 50%,
      transparent 100%
    );
  backdrop-filter: blur(8px);
  border-bottom: 2px solid transparent;
  border-image: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 150, 255, 0.6) 25%,
      rgba(0, 255, 180, 0.4) 50%,
      rgba(0, 150, 255, 0.6) 75%,
      transparent 100%
    )
    1;
  box-shadow: 0 0 20px rgba(0, 120, 255, 0.3), 0 0 40px rgba(0, 150, 255, 0.15),
    0 4px 25px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.08),
    inset 0 -1px 0 rgba(0, 150, 255, 0.2);
}

.main-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(
      90deg,
      transparent 0%,
      rgba(0, 150, 255, 0.08) 30%,
      rgba(0, 255, 180, 0.06) 50%,
      rgba(0, 150, 255, 0.08) 70%,
      transparent 100%
    ),
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 60px,
      rgba(0, 150, 255, 0.04) 62px,
      transparent 64px,
      transparent 120px
    ),
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 8px,
      rgba(0, 255, 180, 0.02) 10px,
      transparent 12px,
      transparent 24px
    );
  pointer-events: none;
  opacity: 0.8;
}

.main-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 15% 30%,
      rgba(0, 150, 255, 0.06) 0%,
      transparent 40%
    ),
    radial-gradient(
      circle at 85% 70%,
      rgba(0, 255, 180, 0.05) 0%,
      transparent 35%
    ),
    linear-gradient(
      45deg,
      transparent 0%,
      rgba(0, 120, 255, 0.03) 50%,
      transparent 100%
    );
  pointer-events: none;
  animation: mechaGlow 6s ease-in-out infinite alternate;
}

@keyframes mechaGlow {
  0% {
    opacity: 0.6;
    filter: brightness(0.9);
  }
  50% {
    opacity: 0.8;
    filter: brightness(1.1);
  }
  100% {
    opacity: 0.7;
    filter: brightness(1);
  }
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  position: relative;
  padding: 1vh 1.25vw;
}

.left-section {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-shrink: 0;
}

.right-section {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
}

.system-title {
  font-size: 2vw;
  font-weight: 600;
  color: #ffffff;
  letter-spacing: 1.5px;
  text-shadow: 0 0 10px rgba(0, 150, 255, 0.6), 0 0 20px rgba(0, 150, 255, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.4);
  margin: 0;
  padding: 0;
  text-align: left;
  font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
  background: linear-gradient(
    135deg,
    #ffffff 0%,
    rgba(200, 230, 255, 0.95) 30%,
    rgba(0, 180, 255, 0.9) 70%,
    rgba(0, 255, 200, 0.85) 100%
  );
  -webkit-text-fill-color: transparent;
  background-clip: text;
  white-space: nowrap;
  position: relative;
}

.back-button,
.nav-link {
  color: rgba(66, 153, 225, 0.9);
  font-weight: 500;
  text-decoration: none;
  background-color: rgba(15, 34, 58, 0.6);
  border: 1px solid rgba(66, 153, 225, 0.4);
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s;
  pointer-events: auto;
}

.back-button:hover,
.nav-link:hover {
  color: #fff;
  background-color: rgba(66, 153, 225, 0.2);
  border-color: rgba(66, 153, 225, 0.8);
}

.nav-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
  flex: 1;
  justify-content: center;
  padding: 0 20px;
}

.transparent-button {
  color: rgba(66, 153, 225, 0.9);
  font-size: 1.2vw;
  background: linear-gradient(
    to right,
    rgba(15, 34, 58, 0) 0%,
    rgba(15, 34, 58, 0.4) 30%,
    rgba(15, 34, 58, 0.4) 70%,
    rgba(15, 34, 58, 0) 100%
  );
  border: none;
  border-radius: 4px;
  padding: 4px 12px;
  cursor: pointer;
  transition: all 0.3s;
  pointer-events: auto;
  outline: none;
  white-space: nowrap;
}

.transparent-button:hover {
  color: #fff;
  background: linear-gradient(
    to right,
    rgba(66, 153, 225, 0) 0%,
    rgba(66, 153, 225, 0.2) 30%,
    rgba(66, 153, 225, 0.2) 70%,
    rgba(66, 153, 225, 0) 100%
  );
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.6);
}

.transparent-button:focus {
  outline: none;
}

.user-controls {
  display: flex;
  align-items: center;
  background-color: rgba(10, 25, 47, 0.6);
  padding: 4px 12px;
  border-radius: 4px;
  border: 1px solid rgba(79, 209, 197, 0.4);
  pointer-events: auto;
}

.username {
  margin-right: 8px;
  color: rgba(79, 209, 197, 0.9);
}

.user-menu {
  color: rgba(79, 209, 197, 0.9);
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.user-menu:hover {
  color: #fff;
}
</style>

<style>
.quick-config-menu .ant-dropdown-menu {
  background: transparent !important;
  box-shadow: none !important;
  padding: 5px;
  border-radius: 4px;
  border: 1px solid rgba(79, 209, 197, 0.4);
}

.quick-config-menu .ant-dropdown-menu-item {
  color: rgba(79, 209, 197, 0.9) !important;
  font-size: 0.9vw !important;
  background: linear-gradient(
    to right,
    rgba(10, 41, 47, 0) 0%,
    rgba(10, 25, 47, 0.4) 30%,
    rgba(10, 25, 47, 0.4) 70%,
    rgba(10, 41, 47, 0) 100%
  ) !important;
  border: none !important;
  border-radius: 4px !important;
  padding: 6px 12px !important;
  cursor: pointer !important;
  transition: all 0.3s !important;
  margin-bottom: 3px !important;
}

.quick-config-menu .ant-dropdown-menu-item:last-child {
  margin-bottom: 0 !important;
}

.quick-config-menu .ant-dropdown-menu-item:hover,
.quick-config-menu .ant-dropdown-menu-item-active {
  color: #fff !important;
  background: linear-gradient(
    to right,
    rgba(79, 209, 197, 0) 0%,
    rgba(79, 209, 197, 0.2) 30%,
    rgba(79, 209, 197, 0.2) 70%,
    rgba(79, 209, 197, 0) 100%
  ) !important;
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.6);
}
</style>
