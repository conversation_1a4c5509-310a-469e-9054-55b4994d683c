<template>
  <div
    class="compass-container"
    :class="[{ 'compass-hidden': !visible }, `compass-position-${position}`]"
  >
    <div class="compass" ref="compassRef" :style="compassStyle">
      <!-- 指针 -->
      <div
        class="compass-needle"
        :style="{ transform: `rotate(${-rotation}deg)` }"
      >
        <div class="needle-north"></div>
        <div class="needle-south"></div>
      </div>

      <!-- 中心点 -->
      <div class="compass-center"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'

const props = defineProps({
  // 相机旋转角度（弧度）
  cameraRotation: {
    type: Number,
    default: 0,
  },
  // 是否显示指南针
  visible: {
    type: Boolean,
    default: true,
  },
  // 指南针大小
  size: {
    type: Number,
    default: 80,
  },
  // 位置
  position: {
    type: String,
    default: 'top-right', // top-left, top-right, bottom-left, bottom-right
    validator: value =>
      ['top-left', 'top-right', 'bottom-left', 'bottom-right'].includes(value),
  },
})

const compassRef = ref(null)

// 将弧度转换为度数
const rotation = computed(() => {
  return (props.cameraRotation * 180) / Math.PI
})

// 计算指南针样式
const compassStyle = computed(() => {
  const styles = {
    width: `${props.size}px`,
    height: `${props.size}px`,
  }

  // 根据位置设置定位
  const positions = {
    'top-left': { top: '20px', left: '20px' },
    'top-right': { top: '20px', right: '20px' },
    'bottom-left': { bottom: '20px', left: '20px' },
    'bottom-right': { bottom: '20px', right: '20px' },
  }

  return { ...styles, ...positions[props.position] }
})

onMounted(() => {
  if (compassRef.value) {
    Object.assign(compassRef.value.style, compassStyle.value)
  }
})

watch(
  compassStyle,
  newStyle => {
    if (compassRef.value) {
      Object.assign(compassRef.value.style, newStyle)
    }
  },
  { deep: true }
)
</script>

<style lang="scss" scoped>
.compass-container {
  position: fixed;
  top: 12vh;
  right: 2vw;
  z-index: 1000;
  transition: opacity 0.3s ease;

  &.compass-hidden {
    opacity: 0;
    pointer-events: none;
  }
}

.compass {
  position: relative;
  // 移除所有背景和边框样式，保持透明
}

// 移除所有刻度和方向标记样式

.compass-needle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  transform: translate(-50%, -50%);
  transform-origin: center center;
  will-change: transform;
  transition: transform 0.1s ease-out;

  .needle-north {
    position: absolute;
    top: -25px;
    left: -4px;
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 25px solid #ff4444; // 北向改为红色
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.4))
      drop-shadow(0 0 4px rgba(255, 68, 68, 0.6));
  }

  .needle-south {
    position: absolute;
    top: 0;
    left: -4px;
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 25px solid #c0c0c0; // 南向改为浅灰色
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3))
      drop-shadow(0 0 2px rgba(192, 192, 192, 0.4));
  }
}

.compass-center {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6px;
  height: 6px;
  background: #333333; // 中心点改为深灰色
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

// 响应式设计 - 简化版本
@media (max-width: 768px) {
  .compass-container {
    .compass {
      width: 60px !important;
      height: 60px !important;
    }

    .compass-needle {
      .needle-north {
        top: -20px;
        left: -3px;
        border-left: 3px solid transparent;
        border-right: 3px solid transparent;
        border-bottom: 20px solid #ff4444;
      }

      .needle-south {
        top: 0;
        left: -3px;
        border-left: 3px solid transparent;
        border-right: 3px solid transparent;
        border-top: 20px solid #c0c0c0;
      }
    }

    .compass-center {
      width: 4px;
      height: 4px;
    }
  }
}
</style>
