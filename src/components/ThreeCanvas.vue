<template>
  <div class="three-container">
    <div ref="container" class="three-canvas-holder"></div>

    <div class="three-toolbar">
      <button class="three-btn" @click="resetView">重置地图</button>
      <button class="three-btn" @click="toggle2D3D">
        {{ isTopDown2D ? '2D' : '3D' }}
      </button>
    </div>

    <div class="three-compass">
      <div
        class="needle"
        :style="{ transform: `rotate(${-compassRotationDeg}deg)` }"
      >
        <div class="north"></div>
        <div class="south"></div>
      </div>
      <div class="center"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, provide } from 'vue'
import * as THREE from 'three'
import ThreeSceneManager from '@/utils/ThreeSceneManager'

const INITIAL_CONTROLS_TARGET = { x: 6.0, y: 0.0, z: -20.0 }
const INITIAL_CAMERA_POS = { x: 6, y: 3, z: -16.0 }

const props = defineProps({
  backgroundColor: {
    type: Number,
    default: 0x050a14,
  },
  cameraChangeCallback: {
    type: Function,
    default: null,
  },
  groupsFactory: {
    type: Function,
    default: null,
  },
})

const emit = defineEmits([
  'ready',
  'resize',
  'error',
  'pointer-move',
  'pointer-down',
  'pointer-up',
])

const container = ref(null)
let sceneManager = null
let groups = null
let controlsChangeHandler = null

const isTopDown2D = ref(false)
const compassRotationDeg = ref(0)
let savedViewState = null

// 拾取与地面
let raycaster = null
let groundMesh = null

// 创建科技背景
const createTechBackground = scene => {
  // 设置背景色
  scene.background = new THREE.Color(props.backgroundColor)

  // 调整环境光并添加顶光
  const ambientLight = scene.children.find(
    child => child instanceof THREE.AmbientLight
  )
  if (ambientLight) {
    ambientLight.color.setHex(0xc5f6fa)
    ambientLight.intensity = 0.6
  }
  const topLight = new THREE.DirectionalLight(0x4fd1c5, 0.3)
  topLight.position.set(0, 20, 0)
  scene.add(topLight)
}

const updateCompassRotation = () => {
  if (!sceneManager) return
  const camera = sceneManager.getCamera()
  if (!camera) return
  const direction = camera.getWorldDirection(new THREE.Vector3())
  const angle = Math.atan2(direction.x, -direction.z)
  compassRotationDeg.value = (angle * 180) / Math.PI
  if (props.cameraChangeCallback) {
    try {
      props.cameraChangeCallback()
    } catch (_) {}
  }
}

const resetView = () => {
  if (!sceneManager) return
  const controls = sceneManager.getControls()
  const camera = sceneManager.getCamera()
  if (!controls || !camera) return
  controls.target.set(
    INITIAL_CONTROLS_TARGET.x,
    INITIAL_CONTROLS_TARGET.y,
    INITIAL_CONTROLS_TARGET.z
  )
  camera.position.set(
    INITIAL_CAMERA_POS.x,
    INITIAL_CAMERA_POS.y,
    INITIAL_CAMERA_POS.z
  )
  controls.update()
}

const toggle2D3D = () => {
  if (!sceneManager) return
  const controls = sceneManager.getControls()
  const camera = sceneManager.getCamera()
  if (!controls || !camera) return

  if (!isTopDown2D.value) {
    savedViewState = {
      position: camera.position.clone(),
      target: controls.target.clone(),
      minPolarAngle: controls.minPolarAngle,
      maxPolarAngle: controls.maxPolarAngle,
      enableRotate: controls.enableRotate,
    }

    const distance = camera.position.distanceTo(controls.target)
    controls.minPolarAngle = 0
    controls.maxPolarAngle = 0
    controls.enableRotate = false
    camera.position.set(
      controls.target.x,
      controls.target.y + Math.max(distance, 10),
      controls.target.z
    )
    camera.lookAt(controls.target)
    controls.update()
    isTopDown2D.value = true
  } else {
    if (savedViewState) {
      controls.minPolarAngle = savedViewState.minPolarAngle
      controls.maxPolarAngle = savedViewState.maxPolarAngle
      controls.enableRotate = savedViewState.enableRotate
      controls.target.copy(savedViewState.target)
      camera.position.copy(savedViewState.position)
      controls.update()
    }
    isTopDown2D.value = false
  }
}

// 工具：从 DOM 事件计算 NDC
const getNDCFromDOM = evt => {
  const rect = container.value.getBoundingClientRect()
  const domX = evt.clientX
  const domY = evt.clientY
  const ndcX = ((domX - rect.left) / rect.width) * 2 - 1
  const ndcY = -((domY - rect.top) / rect.height) * 2 + 1
  return { ndc: { x: ndcX, y: ndcY }, dom: { x: domX, y: domY } }
}

// 工具：基于 NDC 获取地面交点
const getGroundIntersectionFromNDC = (ndcX, ndcY) => {
  if (!sceneManager || !raycaster) return null
  const camera = sceneManager.getCamera()
  if (!camera) return null
  raycaster.setFromCamera({ x: ndcX, y: ndcY }, camera)
  if (groundMesh) {
    const hits = raycaster.intersectObject(groundMesh, false)
    if (hits && hits.length) {
      const p = hits[0].point
      return { x: p.x, y: p.y, z: p.z }
    }
  }
  return null
}

// 拾取方法：基于 NDC 与目标对象求交
const pick = (targets, options = {}) => {
  const { ndc, dom, recursive = true, line2Threshold, filter } = options
  if (!sceneManager || !raycaster)
    return { world: null, normal: null, intersections: [] }
  const camera = sceneManager.getCamera()
  if (!camera) return { world: null, normal: null, intersections: [] }

  if (!ndc && !dom) return { world: null, normal: null, intersections: [] }
  let ndcX, ndcY
  if (ndc) {
    ndcX = ndc.x
    ndcY = ndc.y
  } else if (dom) {
    const rect = container.value.getBoundingClientRect()
    ndcX = ((dom.x - rect.left) / rect.width) * 2 - 1
    ndcY = -((dom.y - rect.top) / rect.height) * 2 + 1
  }

  // 配置 Line2 阈值
  if (line2Threshold != null) {
    raycaster.params.Line2 = { threshold: line2Threshold }
    raycaster.camera = camera
  }

  raycaster.setFromCamera({ x: ndcX, y: ndcY }, camera)

  let objects = []
  if (Array.isArray(targets)) {
    objects = targets
  } else if (targets && typeof targets === 'object' && targets.isObject3D) {
    if (recursive && targets.children) {
      objects = targets.children
    } else {
      objects = [targets]
    }
  }

  let intersections = []
  if (objects.length) {
    intersections = raycaster.intersectObjects(objects, recursive)
  }
  if (typeof filter === 'function') {
    intersections = intersections.filter(filter)
  }

  let world = null
  if (intersections.length) {
    const p = intersections[0].point
    world = { x: p.x, y: p.y, z: p.z }
  } else {
    world = getGroundIntersectionFromNDC(ndcX, ndcY)
  }

  return { world, normal: null, intersections }
}

// 指针事件派发（始终启用）
const emitPointerEvent = (type, evt) => {
  const { ndc, dom } = getNDCFromDOM(evt)
  const world = getGroundIntersectionFromNDC(ndc.x, ndc.y)
  const payload = {
    ndc,
    dom,
    world,
    normal: world ? { x: 0, y: 1, z: 0 } : null,
    intersections: [],
  }
  emit(type, payload)
}

// 初始化 Three.js
const initThree = () => {
  if (!container.value) {
    emit('error', new Error('容器未找到'))
    return
  }

  try {
    // 创建场景管理器
    sceneManager = new ThreeSceneManager()

    // 初始化场景
    sceneManager.init(
      container.value,
      INITIAL_CAMERA_POS,
      INITIAL_CONTROLS_TARGET,
      null,
      {
        minDistance: 0.1,
        maxDistance: 25,
      }
    )

    // 获取场景
    const scene = sceneManager.getScene()

    // 创建科技背景
    createTechBackground(scene)

    // 创建地面（拾取用，固定默认）
    raycaster = new THREE.Raycaster()
    const planeGeo = new THREE.PlaneGeometry(2000, 2000)
    const planeMat = new THREE.MeshBasicMaterial({
      visible: false,
      side: THREE.DoubleSide,
    })
    groundMesh = new THREE.Mesh(planeGeo, planeMat)
    groundMesh.rotation.x = -Math.PI / 2
    groundMesh.position.y = 0
    scene.add(groundMesh)

    // 根据工厂创建分组
    groups = {}
    if (typeof props.groupsFactory === 'function') {
      try {
        const created = props.groupsFactory(scene)
        groups = created && typeof created === 'object' ? created : {}
      } catch (_) {
        groups = {}
      }
    }

    // 启动渲染循环
    sceneManager.startAnimationLoop()

    // 监听相机变化以更新指南针
    const controls = sceneManager.getControls()
    if (controls) {
      controlsChangeHandler = updateCompassRotation
      controls.addEventListener('change', controlsChangeHandler)
      updateCompassRotation()
    }

    // 绑定指针事件（始终启用）
    const el = sceneManager.getContainer()
    el.addEventListener('pointermove', onPointerMove)
    el.addEventListener('pointerdown', onPointerDown)
    el.addEventListener('pointerup', onPointerUp)

    // 抛出就绪事件
    const picking = {
      pick,
      getGroundIntersectionFromNDC,
      getNDCFromDOM,
    }

    // 提供给子组件使用
    provide('sceneManager', sceneManager)
    provide('groups', groups)
    provide('picking', picking)

    emit('ready', { sceneManager, groups, picking })
  } catch (error) {
    emit('error', error)
  }
}

const onPointerMove = evt => emitPointerEvent('pointer-move', evt)
const onPointerDown = evt => emitPointerEvent('pointer-down', evt)
const onPointerUp = evt => emitPointerEvent('pointer-up', evt)

// 处理窗口尺寸变化
const handleResize = () => {
  const width = container.value.clientWidth
  const height = container.value.clientHeight
  emit('resize', { width, height })
}

onMounted(() => {
  initThree()
  window.addEventListener('resize', handleResize)
})

onBeforeUnmount(() => {
  if (sceneManager) {
    const controls = sceneManager.getControls()
    if (controls && controlsChangeHandler) {
      controls.removeEventListener('change', controlsChangeHandler)
    }
    const el = sceneManager.getContainer()
    if (el) {
      el.removeEventListener('pointermove', onPointerMove)
      el.removeEventListener('pointerdown', onPointerDown)
      el.removeEventListener('pointerup', onPointerUp)
    }
  }
  sceneManager.dispose()
  sceneManager = null
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.three-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
  flex: 1;
}

.three-canvas-holder {
  width: 100%;
  height: 100%;
}

.three-toolbar {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 10;
  display: flex;
  gap: 8px;
}

.three-btn {
  appearance: none;
  border: 1px solid rgba(255, 255, 255, 0.15);
  background: rgba(15, 23, 42, 0.6);
  color: #e2e8f0;
  padding: 6px 10px;
  border-radius: 6px;
  cursor: pointer;
  backdrop-filter: blur(6px);
}

.three-btn:hover {
  background: rgba(15, 23, 42, 0.8);
}

.three-compass {
  position: absolute;
  top: 36px;
  right: 16px;
  width: 80px;
  height: 80px;
  z-index: 10;
  pointer-events: none;
}

.three-compass .needle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  transform: translate(-50%, -50%);
  transform-origin: center center;
}

.three-compass .needle .north {
  position: absolute;
  top: -25px;
  left: -4px;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 25px solid #ff4444;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.4))
    drop-shadow(0 0 4px rgba(255, 68, 68, 0.6));
}

.three-compass .needle .south {
  position: absolute;
  top: 0;
  left: -4px;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 25px solid #c0c0c0;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3))
    drop-shadow(0 0 2px rgba(192, 192, 192, 0.4));
}

.three-compass .center {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6px;
  height: 6px;
  background: #333333;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}
</style>
