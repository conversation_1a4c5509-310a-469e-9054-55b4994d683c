<template>
  <div class="dashboard-manager">
    <div class="dashboard-left-panel">
      <!-- 设备列表看板 -->
      <div class="dashboard-item">
        <DeviceListDashboard
          :devices="devices"
          :position="positions.deviceList"
          @device-click="$emit('deviceClick', $event)"
        />
      </div>

      <!-- 设备控制模式看板 -->
      <div class="dashboard-item">
        <StatsDashboard
          title="设备控制模式"
          :position="positions.controlMode"
          :show-value="false"
          chart-type="horizontalBar"
          :chart-data="controlModeChartData"
        />
      </div>

      <!-- 设备在线率看板 -->
      <div class="dashboard-item">
        <StatsDashboard
          title="设备在线率"
          :position="positions.deviceOnline"
          :value="deviceOnlineRate"
          unit="%"
          chart-type="pie"
          :chart-data="deviceOnlineChartData"
          :show-value="true"
        />
      </div>

      <!-- 检测器在线率看板 -->
      <div class="dashboard-item">
        <StatsDashboard
          title="检测器在线率"
          :position="positions.detectorOnline"
          :value="detectorOnlineRate"
          unit="%"
          chart-type="bar"
          :chart-data="detectorOnlineChartData"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import StatsDashboard from '@/components/StatsDashboard.vue'
import DeviceListDashboard from '@/components/DeviceListDashboard.vue'

// Props
const props = defineProps({
  devices: {
    type: Array,
    default: () => [],
  },
  edgesStatus: {
    type: Object,
    default: () => ({ online: 0, total: 0 }),
  },
  detectorsStatus: {
    type: Object,
    default: () => ({ online: 0, total: 0 }),
  },
  controlModesData: {
    type: Object,
    default: () => ({}),
  },
  positions: {
    type: Object,
    default: () => ({
      deviceList: {
        position: 'relative',
        width: '300px',
      },
      controlMode: {
        position: 'relative',
        width: '300px',
      },
      deviceOnline: {
        position: 'relative',
        width: '300px',
      },
      detectorOnline: {
        position: 'relative',
        width: '300px',
      },
    }),
  },
})

// Emits
defineEmits(['deviceClick'])

// 计算属性
const deviceOnlineRate = computed(() => {
  if (props.edgesStatus.total === 0) {
    return '0'
  }
  return Math.round(
    (props.edgesStatus.online / props.edgesStatus.total) * 100
  ).toString()
})

const deviceOnlineChartData = computed(() => {
  const onlineCount = props.edgesStatus.online
  const offlineCount = props.edgesStatus.total - onlineCount

  return [
    { label: '在线', value: onlineCount, color: '#48bb78' },
    { label: '离线', value: offlineCount, color: '#f56565' },
  ]
})

const detectorOnlineRate = computed(() => {
  if (props.detectorsStatus.total === 0) {
    return '0'
  }
  return Math.round(
    (props.detectorsStatus.online / props.detectorsStatus.total) * 100
  ).toString()
})

const detectorOnlineChartData = computed(() => {
  const onlineCount = props.detectorsStatus.online
  const offlineCount = props.detectorsStatus.total - onlineCount

  return [
    { label: '在线', value: onlineCount, color: '#48bb78' },
    { label: '离线', value: offlineCount, color: '#f56565' },
  ]
})

const controlModeChartData = computed(() => {
  // 颜色映射
  const colorMap = {
    'UTC协调感应Actuated-Coord': '#4299e1', // 恢复原始格式
    方案选择APS: '#ed8936',
    脱机运行Isolate: '#f56565',
    固定时间FixedTime: '#48bb78',
    自适应: '#9f7aea',
    自由感应Free: '#a0aec0',
  }

  // 转换为图表所需数据格式
  return Object.entries(props.controlModesData).map(([mode, count]) => ({
    label: formatLabelForDisplay(mode), // 提取为独立函数
    value: count,
    color: colorMap[mode] || '#a0aec0',
  }))
})

// 优化：提取文本格式化逻辑为独立函数
const formatLabelForDisplay = text => {
  // 只对包含中英文混合的长文本进行处理
  if (
    text.length > 8 &&
    /[\u4e00-\u9fa5]/.test(text) &&
    /[A-Za-z]/.test(text)
  ) {
    return text.replace(/([\u4e00-\u9fa5]+)([A-Za-z-]+)/, '$1\n$2')
  }
  return text
}
</script>

<style scoped lang="scss">
.dashboard-manager {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none; // 允许点击穿透到背景
  z-index: 10;
}

.dashboard-left-panel {
  position: absolute;
  top: 9vh; // 增加top值，确保不会覆盖标题组件
  left: 10px;
  width: 300px;
  height: 85vh; // 相应减少高度，保持总体布局平衡
  display: flex;
  flex-direction: column;
  gap: 10px; // 减小间距
  pointer-events: auto; // 恢复面板内的点击事件
  overflow: hidden; // 隐藏滚动条
}

.dashboard-item {
  flex: 1; // 每个项目平均分配剩余空间，实现25%高度
  min-height: 0; // 允许flex项目缩小到内容以下

  // 确保内部组件填满容器
  :deep(.stats-dashboard),
  :deep(.device-list-dashboard) {
    height: 100%;
    min-height: 0; // 完全移除最小高度限制，允许内容完全自适应
  }
}

// 响应式设计 - 针对小屏幕优化
@media (max-height: 600px) {
  .dashboard-left-panel {
    top: 10vh; // 确保不覆盖标题，即使在小屏幕上
    height: 88vh; // 相应调整高度
    gap: clamp(2px, 0.5vh, 5px); // 动态间距
  }
}

// 针对极小屏幕的额外优化
@media (max-height: 400px) {
  .dashboard-left-panel {
    top: 8vh; // 极小屏幕也要保留标题空间
    height: 90vh; // 相应调整高度
    gap: 2px; // 最小间距
  }

  .dashboard-item {
    // 在极小屏幕上允许更大的压缩
    min-height: 0;

    :deep(.stats-dashboard),
    :deep(.device-list-dashboard) {
      min-height: 0;
    }
  }
}
</style>
