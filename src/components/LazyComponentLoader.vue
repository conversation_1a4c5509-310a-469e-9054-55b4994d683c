<template>
  <div class="lazy-component-wrapper">
    <a-spin v-if="loading" tip="加载组件中..."></a-spin>
    <div v-else-if="error" class="load-error">
      <a-alert
        type="error"
        :message="error.message || '组件加载失败'"
        show-icon
      />
      <a-button
        type="primary"
        size="small"
        @click="retryLoading"
        style="margin-top: 8px"
      >
        重试加载
      </a-button>
    </div>
    <component
      v-else-if="componentInstance"
      :is="componentInstance"
      v-bind="$attrs"
    />
    <div v-else class="component-placeholder">组件未找到</div>
  </div>
</template>

<script>
import {
  defineComponent,
  ref,
  onMounted,
  watch,
  computed,
  onUnmounted,
  markRaw,
} from 'vue'
import axios from 'axios'
import { SERVER_CONFIG } from '../config/server.config'

const COMPONENT_MAPPING = {
  network: {
    path: () => import('../../../sigopt-box/src/views/Network.vue'),
    fallbackPath: null,
  },
  index: {
    path: () => import('../../../sigopt-box/src/views/index/index.vue'),
    fallbackPath: null,
  },
  'intersection-screen': {
    path: () => import('../../../sigopt-box/src/views/IntersectionScreen.vue'),
    fallbackPath: null,
  },
  'device-control-screen': {
    path: () =>
      import('../../../sigopt-box/src/views/IndustrialControlScreen.vue'),
    fallbackPath: null,
  },
  'device-state': {
    path: () => import('../../../sigopt-box/src/views/index/index.vue'),
    fallbackPath: null,
  },
  schedules: {
    path: () => import('../../../sigopt-box/src/views/schedules/index.vue'),
    fallbackPath: null,
  },
  'service-config': {
    path: () => import('../../../sigopt-box/src/views/ServiceConfig.vue'),
    fallbackPath: null,
  },
  'stage-pool': {
    path: () => import('../../../sigopt-box/src/views/StagePool.vue'),
    fallbackPath: null,
  },
  'phase-detector-config': {
    path: () => import('../../../sigopt-box/src/views/PhaseDetectorConfig.vue'),
    fallbackPath: null,
  },
  'detector-config': {
    path: () =>
      import('../../../sigopt-box/src/views/detectorConfig/index.vue'),
    fallbackPath: null,
  },
}

export default defineComponent({
  name: 'LazyComponentLoader',
  inheritAttrs: false,

  props: {
    componentType: {
      type: String,
      required: true,
      validator: value => Object.keys(COMPONENT_MAPPING).includes(value),
    },
    loadOnMount: {
      type: Boolean,
      default: true,
    },
    edgeId: {
      type: String,
      default: '',
    },
    embeddedMode: {
      type: Boolean,
      default: false,
    },
    monitorType: {
      type: String,
      default: 'running',
    },
    componentUrl: {
      type: String,
      default: '',
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },

  emits: ['loading', 'loaded', 'error'],

  setup(props, { emit }) {
    const componentInstance = ref(null)
    const loading = ref(false)
    const error = ref(null)
    let loadAttempts = 0
    const MAX_LOAD_ATTEMPTS = 3
    let requestInterceptor = null

    const shouldInterceptRequests = computed(() => {
      return props.edgeId && props.edgeId.trim() !== ''
    })

    let originalXhrOpen = null
    let originalFetch = null

    const activeTimers = new Map()

    const bufStateRegex = /buf_state|buffer_state/i

    const transformUrl = url => {
      if (
        !url ||
        (!url.includes('/coreapi/') && !url.includes('*************:8082'))
      ) {
        return { transformed: false, newUrl: url }
      }

      let apiPath = ''
      if (url.includes('*************:8082')) {
        const urlObj = new URL(url)
        apiPath = urlObj.pathname.replace(/^\/+/, '')
      } else if (url.includes('/coreapi/')) {
        const coreApiIndex = url.indexOf('coreapi/')
        apiPath = coreApiIndex !== -1 ? url.substring(coreApiIndex) : url
      }

      if (!apiPath.includes('coreapi')) {
        apiPath = `coreapi/${apiPath}`
      }

      const parts = ['/api', '/api-proxy', props.edgeId, apiPath]
      const newUrl = parts.map(p => p.replace(/^\/+|\/+$/g, '')).join('/')
      return { transformed: true, newUrl: `/${newUrl}` }
    }

    const monkeyPatchTimers = () => {
      const originalSetInterval = window.setInterval
      const originalSetTimeout = window.setTimeout
      const originalClearInterval = window.clearInterval
      const originalClearTimeout = window.clearTimeout

      window.setInterval = function (fn, delay, ...args) {
        const id = originalSetInterval.call(this, fn, delay, ...args)
        activeTimers.set(id, {
          type: 'interval',
          createdAt: Date.now(),
        })
        return id
      }

      window.setTimeout = function (fn, delay, ...args) {
        const id = originalSetTimeout.call(
          this,
          (...innerArgs) => {
            activeTimers.delete(id)
            fn.apply(this, innerArgs)
          },
          delay,
          ...args
        )

        activeTimers.set(id, {
          type: 'timeout',
          createdAt: Date.now(),
        })

        return id
      }

      window.clearInterval = function (id) {
        activeTimers.delete(id)
        return originalClearInterval.call(this, id)
      }

      window.clearTimeout = function (id) {
        activeTimers.delete(id)
        return originalClearTimeout.call(this, id)
      }

      window.__LAZY_LOADER_ORIGINAL_SET_INTERVAL__ = originalSetInterval
      window.__LAZY_LOADER_ORIGINAL_SET_TIMEOUT__ = originalSetTimeout
      window.__LAZY_LOADER_ORIGINAL_CLEAR_INTERVAL__ = originalClearInterval
      window.__LAZY_LOADER_ORIGINAL_CLEAR_TIMEOUT__ = originalClearTimeout
    }

    const restoreTimers = () => {
      if (window.__LAZY_LOADER_ORIGINAL_SET_INTERVAL__) {
        window.setInterval = window.__LAZY_LOADER_ORIGINAL_SET_INTERVAL__
        delete window.__LAZY_LOADER_ORIGINAL_SET_INTERVAL__
      }

      if (window.__LAZY_LOADER_ORIGINAL_SET_TIMEOUT__) {
        window.setTimeout = window.__LAZY_LOADER_ORIGINAL_SET_TIMEOUT__
        delete window.__LAZY_LOADER_ORIGINAL_SET_TIMEOUT__
      }

      if (window.__LAZY_LOADER_ORIGINAL_CLEAR_INTERVAL__) {
        window.clearInterval = window.__LAZY_LOADER_ORIGINAL_CLEAR_INTERVAL__
        delete window.__LAZY_LOADER_ORIGINAL_CLEAR_INTERVAL__
      }

      if (window.__LAZY_LOADER_ORIGINAL_CLEAR_TIMEOUT__) {
        window.clearTimeout = window.__LAZY_LOADER_ORIGINAL_CLEAR_TIMEOUT__
        delete window.__LAZY_LOADER_ORIGINAL_CLEAR_TIMEOUT__
      }
    }

    const clearAllTimers = () => {
      activeTimers.forEach((info, id) => {
        if (info.type === 'interval') {
          window.clearInterval(id)
        } else {
          window.clearTimeout(id)
        }
      })

      activeTimers.clear()
    }

    const setupApiInterceptor = () => {
      if (!shouldInterceptRequests.value) return

      removeApiInterceptor()

      requestInterceptor = axios.interceptors.request.use(config => {
        if (typeof config.url === 'string') {
          const { transformed, newUrl } = transformUrl(config.url)
          if (transformed) {
            config.url = newUrl
          }
        }
        return config
      })

      originalXhrOpen = window.XMLHttpRequest.prototype.open
      window.XMLHttpRequest.prototype.open = function (method, url, ...args) {
        if (typeof url === 'string') {
          if (bufStateRegex.test(url)) {
            if (!props.embeddedMode && !props.edgeId) {
              Object.defineProperty(this, 'readyState', {
                value: 4,
                writable: true,
              })
              Object.defineProperty(this, 'status', {
                value: 200,
                writable: true,
              })
              Object.defineProperty(this, 'response', {
                value: JSON.stringify({ status: 'ok', data: {} }),
                writable: true,
              })
              Object.defineProperty(this, 'responseText', {
                value: JSON.stringify({ status: 'ok', data: {} }),
                writable: true,
              })

              const originalSend = this.send
              this.send = function () {
                setTimeout(() => {
                  if (this.onreadystatechange) {
                    this.onreadystatechange()
                  }
                  if (this.onload) {
                    this.onload()
                  }
                }, 10)
                return undefined
              }

              return
            }
          }

          const { transformed, newUrl } = transformUrl(url)
          if (transformed) {
            url = newUrl
          }
        }
        return originalXhrOpen.call(this, method, url, ...args)
      }

      originalFetch = window.fetch
      window.fetch = async function (resource, config) {
        if (typeof resource === 'string') {
          if (bufStateRegex.test(resource)) {
            if (
              !document.querySelector('.ant-modal-root .ant-modal-mask') ||
              !props.edgeId
            ) {
              return Promise.resolve(
                new Response(
                  JSON.stringify({
                    status: 'ok',
                    data: {},
                    message: 'mock response for buf_state request',
                  }),
                  {
                    status: 200,
                    headers: {
                      'Content-Type': 'application/json',
                    },
                  }
                )
              )
            }
          }

          const { transformed, newUrl } = transformUrl(resource)
          if (transformed) {
            resource = newUrl
          }
        }
        return originalFetch.call(this, resource, config)
      }

      monkeyPatchTimers()
    }

    const removeApiInterceptor = () => {
      if (requestInterceptor !== null) {
        axios.interceptors.request.eject(requestInterceptor)
        requestInterceptor = null
      }

      if (originalXhrOpen) {
        window.XMLHttpRequest.prototype.open = originalXhrOpen
        originalXhrOpen = null
      }

      if (originalFetch) {
        window.fetch = originalFetch
        originalFetch = null
      }

      clearAllTimers()
      restoreTimers()
    }

    const loadComponent = async () => {
      if (loadAttempts >= MAX_LOAD_ATTEMPTS) {
        error.value = new Error(
          `组件加载失败: 已超过最大重试次数(${MAX_LOAD_ATTEMPTS})`
        )
        emit('error', error.value)
        return
      }

      loadAttempts++

      loading.value = true
      error.value = null
      emit('loading', true)

      try {
        if (shouldInterceptRequests.value) {
          setupApiInterceptor()
        }

        const componentConfig = COMPONENT_MAPPING[props.componentType]
        if (!componentConfig) {
          throw new Error(`未找到组件类型 "${props.componentType}" 的配置`)
        }

        // 预加载依赖已移除，直接加载组件

        const module = await componentConfig.path()
        componentInstance.value = markRaw(module.default)
        emit('loaded', true)
      } catch (err) {
        const fallbackPath =
          COMPONENT_MAPPING[props.componentType]?.fallbackPath
        if (fallbackPath) {
          try {
            const fallbackModule = await fallbackPath()
            componentInstance.value = markRaw(fallbackModule.default)
            emit('loaded', true)
          } catch (fallbackErr) {
            error.value = err
            emit('error', err)
          }
        } else {
          error.value = err
          emit('error', err)
        }
      } finally {
        loading.value = false
        emit('loading', false)
      }
    }

    const retryLoading = () => {
      loadComponent()
    }

    // 监听设备ID变化，当设备ID改变时重新加载组件
    watch(
      () => props.edgeId,
      (newEdgeId, oldEdgeId) => {
        if (newEdgeId !== oldEdgeId && newEdgeId) {
          console.log(
            `设备ID已变更: ${oldEdgeId} => ${newEdgeId}，重新加载组件...`
          )
          // 在重新加载之前，确保清理掉旧的拦截器和定时器
          removeApiInterceptor()
          // 重置组件实例、加载状态和重试计数器
          componentInstance.value = null
          loadAttempts = 0
          // 延迟重新加载组件，确保DOM更新
          setTimeout(() => {
            loadComponent()
          }, 100)
        }
      }
    )

    // 监听组件类型变化
    watch(
      () => props.componentType,
      () => {
        // 重置组件实例、加载状态和重试计数器
        componentInstance.value = null
        loadAttempts = 0

        // 组件类型变化时加载新组件
        loadComponent()
      }
    )

    onMounted(() => {
      if (props.loadOnMount) {
        loadComponent()
      }

      // 清除localStorage中的backup_channelization数据
      if (props.componentType === 'intersection-screen') {
        try {
          localStorage.removeItem('backup_channelization')
        } catch (err) {
          console.error('清除本地存储的渠化图备份数据失败:', err)
        }
      }

      window.addEventListener('modal-closed', handleModalClosed)
    })

    const handleModalClosed = event => {
      if (event.detail && event.detail.type === props.monitorType) {
        clearAllTimers()
      }
    }

    onUnmounted(() => {
      removeApiInterceptor()
      window.removeEventListener('modal-closed', handleModalClosed)
    })

    return {
      componentInstance,
      loading,
      error,
      retryLoading,
    }
  },
})
</script>

<style scoped>
.lazy-component-wrapper {
  width: 100%;
  position: relative;
}

.load-error {
  padding: 20px;
  text-align: center;
}

.component-placeholder {
  padding: 40px;
  text-align: center;
  color: #999;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
}
</style>
