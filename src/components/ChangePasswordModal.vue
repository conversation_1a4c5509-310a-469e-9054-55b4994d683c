<template>
	<a-modal :visible="visible" title="修改密码" @cancel="handleCancel" :confirmLoading="loading" :maskClosable="false">
		<a-form :model="formState" :rules="rules" ref="formRef" layout="vertical">
			<a-form-item name="oldPassword" label="当前密码">
				<a-input-password v-model:value="formState.oldPassword" placeholder="请输入当前密码" autocomplete="current-password" />
			</a-form-item>

			<a-form-item name="newPassword" label="新密码">
				<a-input-password v-model:value="formState.newPassword" placeholder="请输入新密码" autocomplete="new-password" />
			</a-form-item>

			<a-form-item name="confirmPassword" label="确认新密码">
				<a-input-password v-model:value="formState.confirmPassword" placeholder="请再次输入新密码" autocomplete="new-password" />
			</a-form-item>
		</a-form>

		<template #footer>
			<a-button key="cancel" @click="handleCancel">取消</a-button>
			<a-button key="submit" type="primary" :loading="loading" @click="handleSubmit">确认修改</a-button>
		</template>
	</a-modal>
</template>

<script setup>
	import { ref, reactive } from 'vue';
	import { message } from 'ant-design-vue';
	import { changePassword } from '@/api/auth';
	import { removeToken } from '@/utils/auth';
	import { useRouter } from 'vue-router';

	const props = defineProps({
		visible: {
			type: Boolean,
			default: false,
		},
	});

	const emit = defineEmits(['update:visible', 'success']);

	const formRef = ref();
	const loading = ref(false);
	const router = useRouter();

	// 表单状态
	const formState = reactive({
		oldPassword: '',
		newPassword: '',
		confirmPassword: '',
	});

	// 表单验证规则
	const validateConfirmPassword = async (rule, value) => {
		if (value === '') {
			return Promise.reject('请确认新密码');
		} else if (value !== formState.newPassword) {
			return Promise.reject('两次输入的密码不一致');
		}
		return Promise.resolve();
	};

	const rules = {
		oldPassword: [
			{ required: true, message: '请输入当前密码', trigger: 'blur' },
			{ min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' },
		],
		newPassword: [
			{ required: true, message: '请输入新密码', trigger: 'blur' },
			{ min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' },
		],
		confirmPassword: [
			{ required: true, message: '请确认新密码', trigger: 'blur' },
			{ validator: validateConfirmPassword, trigger: 'blur' },
		],
	};

	// 重置表单
	const resetForm = () => {
		formState.oldPassword = '';
		formState.newPassword = '';
		formState.confirmPassword = '';
		formRef.value?.resetFields();
	};

	// 取消修改密码
	const handleCancel = () => {
		resetForm();
		emit('update:visible', false);
	};

	// 提交修改密码
	const handleSubmit = async () => {
		try {
			// 表单验证
			await formRef.value.validate();

			loading.value = true;

			// 调用修改密码API
			const response = await changePassword(formState.oldPassword, formState.newPassword);
			const { returnCode, msg, data } = response;

			if (returnCode === 0) {
				message.success('密码修改成功，请使用新密码重新登录');
				resetForm();
				emit('success');
				emit('update:visible', false);

				// 清除当前登录token
				removeToken();

				// 跳转到登录页
				setTimeout(() => {
					router.push('/login');
				}, 1500);
			} else {
				message.error(msg || '密码修改失败');
			}
		} catch (error) {
			if (error.message) {
				message.error(error.message);
			} else {
				message.error('表单验证失败或网络错误');
				console.error('修改密码出错:', error);
			}
		} finally {
			loading.value = false;
		}
	};
</script>
