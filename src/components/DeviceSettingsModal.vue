<template>
  <a-modal
    :visible="visible"
    :width="1600"
    @cancel="handleClose"
    :footer="null"
    :destroyOnClose="true"
    wrapClassName="device-settings-modal"
    :bodyStyle="{ maxHeight: '90vh', overflow: 'hidden' }"
  >
    <!-- 自定义标题，包含设备选择器 -->
    <template #title>
      <div class="modal-title-with-selector">
        <span class="modal-title">设备配置</span>
        <device-selector
          v-model="selectedDeviceId"
          :initial-device-id="initialDeviceId"
          @change="handleDeviceChange"
          style="margin-left: 16px"
        />
      </div>
    </template>
    <a-spin :spinning="loading">
      <div v-if="loadError" class="error-container">
        <a-alert type="error" :message="loadError" show-icon />
        <div class="error-actions">
          <a-button type="primary" @click="retryLoading">重试加载</a-button>
          <a-button @click="handleClose">关闭</a-button>
        </div>
      </div>
      <div v-else class="settings-container">
        <div class="settings-header">
          <a-alert
            v-if="importWarnings.length > 0"
            type="warning"
            show-icon
            style="margin-bottom: 16px"
          >
            <template #message>
              <div>组件加载时遇到以下警告：</div>
              <ul style="margin-top: 8px; padding-left: 20px">
                <li v-for="(warning, index) in importWarnings" :key="index">
                  {{ warning }}
                </li>
              </ul>
            </template>
          </a-alert>
        </div>

        <!-- 固定位置的选项卡 -->
        <div class="fixed-tabs">
          <a-tabs
            v-model:activeKey="activeTab"
            @change="handleTabChange"
            type="card"
          >
            <a-tab-pane key="network" tab="基础信息"> </a-tab-pane>
            <a-tab-pane key="detector-config" tab="检测器"> </a-tab-pane>
            <a-tab-pane key="phase-detector-config" tab="相位"> </a-tab-pane>
            <a-tab-pane key="stage-pool" tab="阶段"> </a-tab-pane>
            <a-tab-pane key="service-config" tab="方案"> </a-tab-pane>
            <a-tab-pane key="schedules" tab="调度表"> </a-tab-pane>
          </a-tabs>
        </div>

        <!-- 选项卡内容区域 -->
        <div class="tab-content">
          <div v-if="activeTab === 'network'">
            <lazy-component-loader
              component-type="network"
              :edge-id="selectedDeviceId"
              :embedded-mode="true"
            />
          </div>
          <div v-else-if="activeTab === 'schedules'">
            <lazy-component-loader
              component-type="schedules"
              :edge-id="selectedDeviceId"
              :embedded-mode="true"
            />
          </div>
          <div v-else-if="activeTab === 'service-config'">
            <lazy-component-loader
              component-type="service-config"
              :edge-id="selectedDeviceId"
              :embedded-mode="true"
            />
          </div>
          <div v-else-if="activeTab === 'stage-pool'">
            <lazy-component-loader
              component-type="stage-pool"
              :edge-id="selectedDeviceId"
              :embedded-mode="true"
            />
          </div>
          <div v-else-if="activeTab === 'phase-detector-config'">
            <lazy-component-loader
              component-type="phase-detector-config"
              :edge-id="selectedDeviceId"
              :embedded-mode="true"
            />
          </div>
          <div v-else-if="activeTab === 'detector-config'">
            <lazy-component-loader
              component-type="detector-config"
              :edge-id="selectedDeviceId"
              :embedded-mode="true"
            />
          </div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script setup>
import {
  ref,
  watch,
  defineAsyncComponent,
  onMounted,
  onBeforeUnmount,
} from 'vue'
import { message } from 'ant-design-vue'

import DeviceSelector from './DeviceSelector.vue'
import { useChannelizationStore } from '@s2/stores/channelization'
import edgeDeviceApi from '../api/modules/edgeDevice'

// 惰性加载组件包装器
const LazyComponentLoader = defineAsyncComponent({
  loader: () => import('./LazyComponentLoader.vue'),
  delay: 200,
  timeout: 10000,
  errorComponent: {
    template: `<div class="error-message">组件加载失败</div>`,
  },
  loadingComponent: {
    template: `<div class="loading-placeholder">加载中...</div>`,
  },
})

// 状态
const loading = ref(true)
const activeTab = ref('network')
const loadError = ref('')
const importWarnings = ref([])
const selectedDeviceId = ref('')
const initialDeviceId = ref('')

const props = defineProps({
  visible: { type: Boolean, default: false },
  device: { type: Object, default: null },
  initialTab: { type: String, default: 'network' },
})

const emit = defineEmits(['close', 'update'])

// 加载状态跟踪器
let isMounted = false

// 数据加载函数
const loadDeviceData = async deviceId => {
  if (!deviceId) {
    loadError.value = '无效的设备ID'
    return
  }
  loading.value = true
  loadError.value = ''
  try {
    await edgeDeviceApi.fetchAndStoreDeviceConfig(deviceId)
    // 数据加载成功后，LazyComponentLoader会因edgeId变化而自动加载内部组件
    // 其内部会控制loading状态
  } catch (error) {
    loadError.value = `加载设备配置失败: ${error.message || '未知错误'}`
  } finally {
    // 确保在数据加载完成后，无论成功或失败，都关闭顶层loading
    loading.value = false
  }
}

// 当props.device变化时，更新初始设备ID并加载数据
watch(
  () => props.device,
  newDevice => {
    if (newDevice && newDevice.edge_id) {
      initialDeviceId.value = newDevice.edge_id
      // 如果模态框可见，则立即加载数据
      if (props.visible) {
        loadDeviceData(newDevice.edge_id)
      }
    }
  },
  { immediate: true, deep: true }
)

// 监听可见性变化，当打开模态框时初始化
watch(
  () => props.visible,
  newValue => {
    if (newValue) {
      activeTab.value = props.initialTab
    }
    if (newValue && isMounted) {
      // 模态框打开时，如果 props.device 存在，则加载数据
      if (props.device && props.device.edge_id) {
        loadDeviceData(props.device.edge_id)
      }
    } else if (!newValue) {
      // 模态框关闭时清理
      loadError.value = ''
      importWarnings.value = []
    }
  }
)

// 处理选项卡切换
const handleTabChange = key => {
  activeTab.value = key
}

// 处理关闭
const handleClose = () => {
  emit('close')
}

// 处理设备选择变更
const handleDeviceChange = selectedDevice => {
  if (selectedDevice && selectedDevice.edge_id) {
    // 更新props.device，通知父组件
    emit('update', { device: selectedDevice })

    // 通知channelizationStore更新当前选中的设备ID
    try {
      const channelizationStore = useChannelizationStore()
      channelizationStore.setCurrentEdgeId(selectedDevice.edge_id)
      // 加载新设备的数据
      loadDeviceData(selectedDevice.edge_id)
    } catch (error) {
      console.error('切换设备时发生错误:', error)
      message.error('切换设备失败: ' + (error.message || '未知错误'))
    }
  }
}
// 重试加载
const retryLoading = () => {
  if (selectedDeviceId.value) {
    loadDeviceData(selectedDeviceId.value)
  }
}

// 组件挂载时初始化
onMounted(() => {
  isMounted = true
  if (props.visible) {
    // 模态框打开时，如果 props.device 存在，则更新 initialDeviceId
    if (props.device) {
      initialDeviceId.value = props.device.edge_id
    }
  }
})

// 组件卸载前清理
onBeforeUnmount(() => {
  isMounted = false
})
</script>

<style scoped lang="scss">
.modal-title-with-selector {
  display: flex;
  align-items: center;
}

.modal-title {
  font-size: 16px;
  font-weight: 500;
  color: #000;
}

.settings-container {
  height: 75vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.fixed-tabs {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.tab-content {
  flex: 1;
  overflow: auto;
  padding-top: 8px;
}

.error-container {
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .error-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
  }
}

.settings-header {
  margin-bottom: 16px;
}

/* 自定义模态框样式 */
:deep(.ant-tabs-nav) {
  margin-bottom: 0;
}

:deep(.device-settings-modal .ant-tabs-tab) {
  background-color: #f0f2f5;
  border-color: #d9d9d9;
}

:deep(.device-settings-modal .ant-tabs-tab-active) {
  background-color: #fff;
  border-bottom-color: #fff;
}

:deep(.settings-container .device-settings-content) {
  margin-top: 0;
  padding-top: 0;
}
</style>
