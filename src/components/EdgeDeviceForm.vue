<template>
	<div class="edge-device-form">
		<h3 class="form-title">{{ isEditMode ? '编辑设备' : '新增设备' }}</h3>
		<a-form ref="formRef" :model="formState" :rules="rules" layout="vertical">
			<a-form-item label="设备ID" name="edge_id">
				<a-input v-model:value="formState.edge_id" />
			</a-form-item>
			<a-form-item label="设备名称" name="edge_name">
				<a-input v-model:value="formState.edge_name" />
			</a-form-item>
			<a-form-item label="设备分类" name="classification">
				<a-select v-model:value="formState.classification" placeholder="请选择设备分类">
					<a-select-option v-for="opt in iconClassificationOptions" :key="opt.value" :value="opt.value">
						{{ opt.label }}
					</a-select-option>
				</a-select>
			</a-form-item>
			<a-form-item label="设备IP" name="ip_address">
				<a-input v-model:value="formState.ip_address" />
			</a-form-item>
			<a-form-item label="设备端口" name="api_port">
				<a-input-number v-model:value="formState.api_port" style="width: 100%" />
			</a-form-item>
			<a-form-item label="路口ID" name="jnc_id">
				<a-input v-model:value="formState.jnc_id" />
			</a-form-item>
			<a-form-item label="路口名称" name="jnc_name">
				<a-input v-model:value="formState.jnc_name" />
			</a-form-item>
			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="路口经度">
						<a-input-number v-model:value="formState.jnc_coord.lng" style="width: 100%" :precision="6" @change="syncCoordinates" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="路口纬度">
						<a-input-number v-model:value="formState.jnc_coord.lat" style="width: 100%" :precision="6" @change="syncCoordinates" />
					</a-form-item>
				</a-col>
			</a-row>
			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="设备经度">
						<a-input-number v-model:value="formState.edge_coord.lng" style="width: 100%" :precision="6" @change="syncDeviceToJnc" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="设备纬度">
						<a-input-number v-model:value="formState.edge_coord.lat" style="width: 100%" :precision="6" @change="syncDeviceToJnc" />
					</a-form-item>
				</a-col>
			</a-row>
			<a-row :gutter="16">
				<a-col :span="24">
					<a-button type="primary" @click="handleLocationPick" :disabled="isLocationPicking">
						<template v-if="!isLocationPicking">选择设备位置</template>
						<template v-else>点击场景中的位置...</template>
					</a-button>
				</a-col>
			</a-row>

			<div class="form-actions">
				<a-button @click="handleCancel">取消</a-button>
				<a-button type="primary" @click="handleSubmit" :loading="isLoading">保存</a-button>
			</div>
		</a-form>
	</div>
</template>

<script setup>
	import { ref, reactive, watch, computed, onMounted, onUnmounted } from 'vue';
	import { cloneDeep } from 'lodash';
	import { message } from 'ant-design-vue';
	import { useSceneStateStore } from '@/stores/sceneState';
	import eventBus from '@/utils/eventBus';

	const props = defineProps({
		visible: { type: Boolean, required: true },
		device: { type: Object, default: null }, // Pass device data for editing
		isLoading: { type: Boolean, default: false },
		selectedCoordinates: { type: Object, default: null }, // 从父组件接收的坐标数据
	});

	const emit = defineEmits(['submit', 'cancel', 'location-pick']);

	const formRef = ref(null);
	const isEditMode = computed(() => !!props.device);
	const isLocationPicking = ref(false);
	const sceneStateStore = useSceneStateStore();

	const iconClassificationOptions = [
		{ value: '10', label: '边缘控制器' },
		{ value: '20', label: '信号机' },
		{ value: '30', label: '视频检测器' },
		// { value: '90', label: '路口' }, // 已移除路口选项
	];

	const createInitialFormState = () => ({
		edge_id: '',
		edge_name: '',
		ip_address: '',
		api_port: 8082,
		classification: '10', // 默认分类为边缘控制器
		jnc_id: '',
		jnc_name: '',
		jnc_coord: { lat: 0, lng: 0 },
		edge_coord: { lat: 0, lng: 0 },
	});

	const formState = reactive(createInitialFormState());

	// 仅对简单字段验证，复杂对象在提交时手动验证
	const rules = {
		edge_id: [{ required: true, message: '请输入设备ID' }],
		edge_name: [{ required: true, message: '请输入设备名称' }],
		ip_address: [{ required: true, message: '请输入IP地址' }],
		api_port: [{ required: true, message: '请输入API端口' }],
		classification: [{ required: true, message: '请选择设备分类' }],
	};

	// 监听外部传入的设备数据，确保表单状态同步
	watch(
		() => props.device,
		(newDevice) => {
			if (formRef.value) {
				formRef.value.resetFields();
			}
			if (newDevice) {
				// 编辑模式：深度克隆新数据以避免意外的副作用
				Object.assign(formState, cloneDeep(newDevice));
			} else {
				// 新增模式：重置为初始状态
				Object.assign(formState, createInitialFormState());
			}
		},
		{
			immediate: true, // 立即执行一次以初始化表单
			deep: true, // 深度监听以处理所有情况
		}
	);

	// 监听外部传入的坐标变化，优先级高于位置选择
	watch(
		() => props.selectedCoordinates,
		(newCoordinates) => {
			if (newCoordinates && isLocationPicking.value) {
				// 坐标有效且处于位置选择模式时更新表单
				updateFormCoordinates(newCoordinates);
			}
		},
		{ immediate: true } // 立即检查初始值
	);

	// 封装坐标更新逻辑
	const updateFormCoordinates = (coordinates) => {
		if (!coordinates || coordinates.lng === undefined || coordinates.lat === undefined) {
			return false;
		}

		// 使用新对象更新表单状态
		formState.edge_coord = { ...coordinates };

		// 如果路口坐标未设置，也一并更新
		if (formState.jnc_coord.lng === 0 && formState.jnc_coord.lat === 0) {
			formState.jnc_coord = { ...coordinates };
		}

		// 确保数字类型
		formState.edge_coord.lng = Number(formState.edge_coord.lng);
		formState.edge_coord.lat = Number(formState.edge_coord.lat);
		formState.jnc_coord.lng = Number(formState.jnc_coord.lng);
		formState.jnc_coord.lat = Number(formState.jnc_coord.lat);

		return true;
	};

	// 开始位置选择
	const handleLocationPick = () => {
		// 设置正在选择位置的状态
		isLocationPicking.value = true;

		// 临时创建一个设备对象用于位置选择
		const tempDevice = {
			edge_id: formState.edge_id || 'temp_' + Date.now(),
			edge_name: formState.edge_name || '新设备',
		};

		// 通知场景进入位置选择模式
		sceneStateStore.setDeviceForPlacement(tempDevice);

		// 发送事件通知父组件暂时隐藏模态框
		emit('location-pick', true);
	};

	// 使用事件总线监听位置选择
	onMounted(() => {
		// 监听位置选择事件
		eventBus.on('location-selected', handleLocationSelected);
	});

	// 组件卸载时移除监听
	onUnmounted(() => {
		// 移除事件监听
		eventBus.off('location-selected', handleLocationSelected);

		// 清理位置选择状态
		if (isLocationPicking.value) {
			sceneStateStore.setDeviceForPlacement(null);
		}
	});

	// 处理位置选择结果
	const handleLocationSelected = (coordinates) => {
		// 仅在位置选择模式下处理
		if (isLocationPicking.value && coordinates) {
			if (updateFormCoordinates(coordinates)) {
				// 退出位置选择模式
				isLocationPicking.value = false;

				// 显示成功消息
				message.success('设备位置已更新');

				// 通知父组件位置选择已完成
				emit('location-pick', false);
			}
		}
	};

	// 手动验证复杂对象字段
	const validateCoordinates = () => {
		// 验证坐标是否有效
		if (!formState.jnc_coord || typeof formState.jnc_coord.lat !== 'number' || typeof formState.jnc_coord.lng !== 'number') {
			message.error('请输入有效的路口坐标');
			return false;
		}

		if (!formState.edge_coord || typeof formState.edge_coord.lat !== 'number' || typeof formState.edge_coord.lng !== 'number') {
			message.error('请输入有效的设备坐标');
			return false;
		}

		return true;
	};

	const handleSubmit = async () => {
		try {
			// 先验证表单基本字段
			await formRef.value.validate();

			// 再手动验证复杂对象字段
			if (!validateCoordinates()) {
				return;
			}

			// 验证通过，创建表单数据副本并提交
			const formData = JSON.parse(JSON.stringify(formState));
			emit('submit', formData);
		} catch (error) {
			console.log('表单验证失败:', error);
		}
	};

	const handleCancel = () => {
		// 如果正在选择位置，取消选择
		if (isLocationPicking.value) {
			isLocationPicking.value = false;
			sceneStateStore.setDeviceForPlacement(null);
		}
		emit('cancel');
	};

	// 获取当前表单状态的副本，供外部存储
	const getFormState = () => {
		return JSON.parse(JSON.stringify(formState));
	};

	// 设置表单状态（恢复保存的值）
	const setFormState = (state) => {
		if (state) {
			Object.assign(formState, state);
		}
	};

	// 导出方法供父组件使用
	defineExpose({
		getFormState,
		setFormState,
	});

	// 添加同步坐标逻辑
	const syncCoordinates = () => {
		// 从路口坐标同步到设备坐标
		formState.edge_coord.lng = formState.jnc_coord.lng;
		formState.edge_coord.lat = formState.jnc_coord.lat;
	};

	// 添加设备到路口同步逻辑
	const syncDeviceToJnc = () => {
		// 从设备坐标同步到路口坐标
		formState.jnc_coord.lng = formState.edge_coord.lng;
		formState.jnc_coord.lat = formState.edge_coord.lat;
	};
</script>

<style scoped lang="scss">
	.edge-device-form {
		padding: 0 16px;
		height: 100%;
		overflow-y: auto;
	}

	.form-title {
		margin-bottom: 24px;
		font-weight: 600;
		font-size: 18px;
	}

	.form-actions {
		margin-top: 24px;
		display: flex;
		justify-content: flex-end;
		gap: 8px;
	}
</style>
