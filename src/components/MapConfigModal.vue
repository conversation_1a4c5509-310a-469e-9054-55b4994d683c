<template>
  <a-modal
    :open="
      visible &&
      !isTrunkLineCreationMode &&
      !isDrawingMode &&
      !isDrawingAreaMode
    "
    title="地图配置"
    @update:open="handleVisibleChange"
    :width="1200"
    :destroyOnClose="false"
    :footer="null"
    :bodyStyle="{ maxHeight: '90vh', overflow: 'hidden' }"
  >
    <a-tabs v-model:activeKey="activeTabKey" @change="onTabChange">
      <a-tab-pane key="data" tab="数据管理">
        <div class="panel-content">
          <div>
            <h4>路网与行政区划数据管理</h4>

            <div class="setting-item">
              <label for="city-file">上传行政区划轮廓:</label>
              <input
                type="file"
                id="city-file"
                ref="cityFileInput"
                @change="handleFileChange($event, 'city')"
                accept=".json,.geojson"
                style="display: none"
              />
              <a-button @click="cityFileInput.click()">选择文件</a-button>
            </div>

            <div class="setting-item">
              <label for="road-file">上传路网数据:</label>
              <input
                type="file"
                id="road-file"
                ref="roadFileInput"
                @change="handleFileChange($event, 'road')"
                accept=".json,.geojson"
                style="display: none"
              />
              <a-button @click="roadFileInput.click()">选择文件</a-button>
            </div>

            <div class="setting-item">
              <label for="highlight-city-select">高亮城市名称:</label>
              <a-select
                id="highlight-city-select"
                v-model:value="highlightCityInput"
                @change="triggerHighlightCityUpdate"
                placeholder="选择城市"
                style="width: 200px"
                allowClear
                show-search
                :filter-option="filterCityOption"
              >
                <a-select-option
                  v-for="cityName in availableCityNames"
                  :key="cityName"
                  :value="cityName"
                >
                  {{ cityName }}
                </a-select-option>
              </a-select>
            </div>

            <div>
              <h4>单个道路导入 (用户绘制层)</h4>
              <div class="setting-item">
                <label for="single-road-file">上传 GeoJSON 道路文件:</label>
                <p class="description">
                  选择一个 GeoJSON 文件。文件可包含单个 LineString Feature，
                  或一个包含多个 LineString Feature 的 FeatureCollection。
                  坐标应为WGS84 (经纬度)。
                </p>
                <input
                  type="file"
                  id="single-road-file"
                  @change="handleSingleRoadFileUpload"
                  accept=".geojson,.json"
                  ref="singleRoadFileInput"
                  style="display: none"
                />
                <a-button @click="singleRoadFileInput.click()"
                  >选择文件</a-button
                >
                <a-button
                  @click="clearSingleRoadFileInput"
                  v-if="singleRoadFileInput && singleRoadFileInput.value"
                  style="margin-left: 8px"
                  >清除已选</a-button
                >
              </div>
              <div class="setting-item">
                <label for="geojson-paste-area"
                  >粘贴 GeoJSON LineString Feature:</label
                >
                <p class="description">
                  在此处粘贴单个 GeoJSON LineString Feature
                  的文本内容。坐标应为WGS84 (经纬度)。
                </p>
                <a-textarea
                  id="geojson-paste-area"
                  v-model:value="geoJsonText"
                  placeholder="在此处粘贴单条道路的 GeoJSON Feature 数据 (LineString 类型, 地理坐标)"
                  :rows="6"
                />
                <a-button @click="processPastedGeoJson" style="margin-top: 8px"
                  >添加粘贴的道路</a-button
                >
              </div>
            </div>
            <p v-if="statusMessage" class="status-message">
              {{ statusMessage }}
            </p>
          </div>
        </div>
      </a-tab-pane>
      <a-tab-pane key="drawing" tab="绘制工具">
        <div class="panel-content">
          <a-tabs v-model:activeKey="drawingTabKey" type="card">
            <a-tab-pane key="road" tab="道路绘制">
              <div class="panel-content">
                <h4>道路绘制</h4>

                <div class="setting-item">
                  <label>绘制说明:</label>
                  <a-alert
                    message="道路绘制说明"
                    description="点击开始绘制后，将关闭此面板并在地图上显示绘制工具。在地图上点击添加路径点，完成后可以设置道路信息。"
                    type="info"
                    show-icon
                    style="margin-bottom: 16px"
                  />
                </div>

                <div class="setting-item">
                  <label>开始绘制:</label>
                  <a-button
                    @click="handleStartRoadDrawing"
                    :disabled="props.isDrawingAreaMode"
                    type="primary"
                  >
                    开始绘制道路
                  </a-button>
                </div>

                <!-- 道路管理列表 -->
                <div class="setting-item">
                  <label>道路管理:</label>
                  <div class="road-network-manager">
                    <!-- 搜索框和刷新按钮 -->
                    <div style="display: flex; gap: 8px; margin-bottom: 12px">
                      <a-input
                        v-model:value="roadNetworkSearchKeyword"
                        placeholder="搜索道路名称、类型或ID..."
                        allow-clear
                        style="flex: 1"
                      >
                        <template #prefix>
                          <SearchOutlined />
                        </template>
                      </a-input>
                      <a-button @click="loadRoadNetworkList" size="small">
                        刷新
                      </a-button>
                    </div>

                    <!-- 道路列表 -->
                    <div
                      class="list-container"
                      style="width: 100%; max-width: 90vw; min-width: 600px"
                    >
                      <a-list
                        :data-source="filteredRoadNetworks"
                        size="large"
                        :pagination="false"
                        class="road-network-list"
                      >
                        <template #renderItem="{ item: road }">
                          <a-list-item class="road-network-item">
                            <template #actions>
                              <a-button
                                size="small"
                                @click="handleEditRoadNotImplemented"
                                :disabled="
                                  props.isDrawingMode || props.isDrawingAreaMode
                                "
                              >
                                修改
                              </a-button>
                              <a-popconfirm
                                title="确定要删除这条道路吗？"
                                ok-text="确定"
                                cancel-text="取消"
                                @confirm="handleDeleteRoad(road)"
                              >
                                <a-button
                                  size="small"
                                  danger
                                  :disabled="
                                    props.isDrawingMode ||
                                    props.isDrawingAreaMode
                                  "
                                >
                                  删除
                                </a-button>
                              </a-popconfirm>
                            </template>
                            <a-list-item-meta>
                              <template #title>
                                <div class="road-info-horizontal">
                                  <span class="road-name">{{ road.name }}</span>
                                  <span class="road-type">{{
                                    road.typeDisplay
                                  }}</span>
                                  <span class="segment-count"
                                    >{{ road.segmentCount }} 个路段</span
                                  >
                                </div>
                              </template>
                            </a-list-item-meta>
                          </a-list-item>
                        </template>
                        <template #empty>
                          <a-empty description="暂无道路数据" />
                        </template>
                      </a-list>
                    </div>
                  </div>
                </div>
              </div>
            </a-tab-pane>

            <a-tab-pane key="area" tab="区域绘制">
              <div class="panel-content">
                <h4>区域绘制</h4>

                <div class="setting-item">
                  <label>绘制说明:</label>
                  <a-alert
                    message="区域绘制说明"
                    description="点击开始绘制后，将关闭此面板并在地图上显示绘制工具。在地图上点击添加区域边界点，完成后可以设置区域信息。"
                    type="success"
                    show-icon
                    style="margin-bottom: 16px"
                  />
                </div>

                <div class="setting-item">
                  <label>开始绘制:</label>
                  <a-button
                    @click="handleStartAreaDrawing"
                    :disabled="props.isDrawingMode"
                    type="primary"
                  >
                    开始绘制区域
                  </a-button>
                </div>

                <div class="setting-item">
                  <label>区域列表:</label>
                  <div style="margin-top: 8px; width: 100%">
                    <a-button
                      @click="refreshRegionsList"
                      :loading="regionsStore.loading"
                      style="margin-bottom: 8px"
                    >
                      刷新列表
                    </a-button>
                    <a-spin :spinning="regionsStore.loading">
                      <div
                        class="list-container"
                        style="width: 70vw; min-width: 100%"
                      >
                        <a-list
                          :data-source="filteredRegions"
                          size="small"
                          :pagination="false"
                        >
                          <template #renderItem="{ item }">
                            <a-list-item
                              :class="{
                                'selected-region':
                                  selectedAreaName === item.region_name,
                              }"
                              @click="handleSelectRegion(item)"
                              style="cursor: pointer"
                            >
                              <template #actions>
                                <a-tooltip title="删除">
                                  <a-popconfirm
                                    title="确定要删除这个区域吗？"
                                    ok-text="确定"
                                    cancel-text="取消"
                                    @confirm="handleDeleteRegion(item)"
                                  >
                                    <a-button type="text" size="small" danger>
                                      <DeleteOutlined />
                                    </a-button>
                                  </a-popconfirm>
                                </a-tooltip>
                              </template>
                              <a-list-item-meta>
                                <template #title>
                                  <div class="region-title">
                                    <span class="region-name">{{
                                      item.region_name
                                    }}</span>
                                  </div>
                                </template>
                              </a-list-item-meta>
                            </a-list-item>
                          </template>
                        </a-list>
                      </div>
                    </a-spin>
                  </div>
                </div>
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
      </a-tab-pane>

      <a-tab-pane key="regions" tab="区域管理">
        <div class="panel-content">
          <h4>区域管理</h4>

          <div class="setting-item">
            <label>刷新列表:</label>
            <a-button
              @click="refreshRegionsList"
              :loading="regionsStore.loading"
            >
              刷新列表
            </a-button>
          </div>

          <div class="setting-item">
            <label>搜索区域:</label>
            <a-input
              v-model:value="regionSearchKeyword"
              placeholder="搜索区域名称"
              style="width: 200px; margin-right: 8px"
            >
              <template #prefix>
                <SearchOutlined />
              </template>
            </a-input>
            <a-select
              v-model:value="selectedRegionClassification"
              placeholder="选择分类"
              style="width: 120px"
              allowClear
            >
              <a-select-option value="A">一级区域</a-select-option>
              <a-select-option value="B">二级区域</a-select-option>
              <a-select-option value="C">三级区域</a-select-option>
            </a-select>
          </div>

          <div class="setting-item">
            <label>区域统计:</label>
            <a-statistic title="区域总数" :value="regionStats.total" />
          </div>

          <div class="setting-item">
            <label>区域列表:</label>
            <div style="margin-top: 8px; width: 100%">
              <a-spin :spinning="regionsStore.loading">
                <div
                  class="list-container"
                  style="width: 100%; min-width: 100%"
                >
                  <a-list
                    :data-source="filteredRegions"
                    size="small"
                    :pagination="false"
                  >
                    <template #renderItem="{ item }">
                      <a-list-item
                        :class="{
                          'selected-region':
                            selectedAreaName === item.region_name,
                        }"
                        @click="handleSelectRegion(item)"
                        style="cursor: pointer"
                      >
                        <template #actions>
                          <a-tooltip title="删除">
                            <a-popconfirm
                              title="确定要删除这个区域吗？"
                              ok-text="确定"
                              cancel-text="取消"
                              @confirm="handleDeleteRegion(item)"
                            >
                              <a-button type="text" size="small" danger>
                                <DeleteOutlined />
                              </a-button>
                            </a-popconfirm>
                          </a-tooltip>
                        </template>
                        <a-list-item-meta>
                          <template #title>
                            <div class="region-title">
                              <span class="region-name">{{
                                item.region_name
                              }}</span>
                            </div>
                          </template>
                        </a-list-item-meta>
                      </a-list-item>
                    </template>
                  </a-list>
                </div>
              </a-spin>
            </div>
          </div>
        </div>
      </a-tab-pane>

      <a-tab-pane key="icons" tab="图标管理">
        <div class="panel-content">
          <h4>图标库管理</h4>

          <div class="setting-item">
            <label>图标操作:</label>
            <a-button @click="handleAddIcon">
              <template #icon><UploadOutlined /></template>
              新增图标
            </a-button>
          </div>

          <div class="setting-item">
            <label>图标列表:</label>
            <div style="margin-top: 8px; width: 100%">
              <div class="list-container" style="width: 100%; min-width: 100%">
                <a-list
                  :data-source="icons"
                  :loading="!icons"
                  class="icon-list-horizontal"
                  :pagination="false"
                >
                  <template #renderItem="{ item }">
                    <a-list-item class="icon-list-item">
                      <template #actions>
                        <a-tooltip title="编辑">
                          <edit-outlined
                            key="edit"
                            @click="handleEditIcon(item)"
                          />
                        </a-tooltip>
                        <a-tooltip title="删除">
                          <a-popconfirm
                            title="确定删除这个图标吗?"
                            ok-text="确定"
                            cancel-text="取消"
                            @confirm="handleDeleteIcon(item.icon_id)"
                          >
                            <a-button type="text" size="small" danger>
                              <DeleteOutlined />
                            </a-button>
                          </a-popconfirm>
                        </a-tooltip>
                      </template>
                      <a-list-item-meta>
                        <template #title>
                          <span :title="item.icon_name">{{
                            item.icon_name
                          }}</span>
                        </template>
                        <template #avatar>
                          <div
                            class="icon-preview-container"
                            v-if="item.icon && item.icon.data"
                            v-html="item.icon.data"
                          ></div>
                          <div
                            class="icon-preview-container placeholder"
                            v-else
                          >
                            无预览
                          </div>
                        </template>
                      </a-list-item-meta>
                    </a-list-item>
                  </template>
                </a-list>
              </div>
            </div>
          </div>
        </div>
      </a-tab-pane>
      <a-tab-pane key="trunklines" tab="干线管理">
        <div class="panel-content">
          <div v-if="isCreatingTrunkLine" class="creation-mode-hint">
            <div class="hint-content">
              <InfoCircleOutlined class="hint-icon" />
              <div class="hint-text">
                <h4>干线创建模式已激活</h4>
                <p>
                  请在地图上点击路线来选择干线路径，使用浮动面板完成干线创建。
                </p>
              </div>
            </div>
            <a-button
              type="primary"
              danger
              @click="handleCancelTrunkLineCreation"
              class="exit-creation-btn"
            >
              退出创建模式
            </a-button>
          </div>

          <div v-else>
            <h4>干线管理</h4>

            <div class="setting-item">
              <label>干线操作:</label>
              <a-button
                type="primary"
                @click="handleCreateTrunkLine"
                :loading="loadingTrunkLines"
                style="margin-right: 8px"
              >
                创建新干线
              </a-button>
              <a-button
                @click="handleRefreshTrunkLines"
                :loading="loadingTrunkLines"
              >
                刷新列表
              </a-button>
            </div>

            <div class="setting-item">
              <label>搜索过滤:</label>
              <a-input
                v-model:value="searchKeyword"
                placeholder="搜索干线名称或分类"
                style="width: 200px; margin-right: 8px"
                @change="handleSearch"
              >
                <template #prefix>
                  <SearchOutlined />
                </template>
              </a-input>
              <a-select
                v-model:value="selectedClassification"
                placeholder="选择分类"
                style="width: 120px"
                @change="handleClassificationFilter"
                allowClear
              >
                <a-select-option
                  v-for="classification in availableClassifications"
                  :key="classification"
                  :value="classification"
                >
                  {{ classification }}
                </a-select-option>
              </a-select>
            </div>

            <div class="setting-item">
              <label>干线统计:</label>
              <a-statistic title="干线总数" :value="trunkLineStats.total" />
            </div>

            <div class="setting-item">
              <label>干线列表:</label>
              <div style="margin-top: 8px; width: 100%">
                <a-spin :spinning="loadingTrunkLines">
                  <div
                    class="list-container"
                    style="width: 100%; min-width: 100%"
                  >
                    <a-list
                      :data-source="filteredTrunkLines"
                      size="small"
                      :pagination="false"
                    >
                      <template #renderItem="{ item }">
                        <a-list-item>
                          <template #actions>
                            <a-tooltip title="编辑">
                              <a-button
                                type="text"
                                size="small"
                                @click="handleEditTrunkLine(item)"
                              >
                                <EditOutlined />
                              </a-button>
                            </a-tooltip>
                            <a-tooltip title="删除">
                              <a-popconfirm
                                title="确定要删除这条干线吗？"
                                ok-text="确定"
                                cancel-text="取消"
                                @confirm="handleDeleteTrunkLine(item.route_id)"
                              >
                                <a-button type="text" size="small" danger>
                                  <DeleteOutlined />
                                </a-button>
                              </a-popconfirm>
                            </a-tooltip>
                          </template>
                          <a-list-item-meta>
                            <template #title>
                              <div class="trunk-line-title">
                                <span class="trunk-line-name">{{
                                  item.route_name
                                }}</span>
                              </div>
                            </template>
                          </a-list-item-meta>
                        </a-list-item>
                      </template>
                      <template #empty>
                        <a-empty description="暂无干线数据" />
                      </template>
                    </a-list>
                  </div>
                </a-spin>
              </div>
            </div>
          </div>
        </div>
      </a-tab-pane>
      <a-tab-pane key="settings" tab="显示设置">
        <div class="panel-content">
          <h4>道路显示配置</h4>

          <div class="setting-item">
            <label>配置说明:</label>
            <div class="description">
              配置不同距离下的道路显示级别，数值越小显示越详细
            </div>
          </div>

          <div class="setting-item">
            <label>道路显示级别:</label>
            <a-select
              :value="roadDisplayMode"
              @change="handleRoadDisplayModeChange"
              style="width: 280px"
            >
              <a-select-option value="auto">自动 (根据距离)</a-select-option>
              <a-select-option value="primary">仅显示主干道</a-select-option>
              <a-select-option value="primary_secondary"
                >主干道和次干道</a-select-option
              >
              <a-select-option value="all">显示所有道路</a-select-option>
            </a-select>
          </div>

          <div v-if="roadDisplayMode === 'auto'">
            <div class="setting-item">
              <label>距离阈值说明:</label>
              <a-alert
                message="距离阈值说明"
                :description="thresholdDescription"
                type="info"
                show-icon
                style="margin-bottom: 16px"
              />
            </div>

            <div class="setting-item">
              <label>隐藏阈值 (m):</label>
              <a-input-number
                :value="hideThreshold"
                @update:value="$emit('update:hideThreshold', $event)"
                :min="0"
                :max="50"
                :step="1"
                placeholder="推荐: 10"
              />
              <div class="threshold-hint">
                距离 > {{ hideThreshold }}m 时隐藏所有道路
              </div>
            </div>

            <div class="setting-item">
              <label>主干道阈值 (m):</label>
              <a-input-number
                :value="primaryThreshold"
                @update:value="$emit('update:primaryThreshold', $event)"
                :min="0"
                :max="hideThreshold || 50"
                :step="1"
                placeholder="推荐: 5"
              />
              <div class="threshold-hint">
                距离 {{ primaryThreshold }}m - {{ hideThreshold }}m 时显示主干道
              </div>
            </div>

            <div class="setting-item">
              <label>次干道/支线阈值 (m):</label>
              <a-input-number
                :value="secondaryThreshold"
                @update:value="$emit('update:secondaryThreshold', $event)"
                :min="0"
                :max="primaryThreshold || 50"
                :step="1"
                placeholder="推荐: 2"
              />
              <div class="threshold-hint">
                距离 &lt; {{ secondaryThreshold }}m 时显示所有道路
              </div>
            </div>

            <div class="setting-item">
              <label>阈值操作:</label>
              <a-button
                type="primary"
                @click="applyDistanceThresholds"
                style="margin-right: 8px"
              >
                保存阈值设置
              </a-button>
              <a-button @click="resetToDefaultThresholds">
                重置为默认值
              </a-button>
            </div>
          </div>
        </div>
      </a-tab-pane>
    </a-tabs>
  </a-modal>

  <a-modal
    v-model:open="isIconModalVisible"
    :title="isEditingIcon ? '编辑图标' : '新增图标'"
    @ok="handleSaveIcon"
    ok-text="确定"
    cancel-text="取消"
  >
    <a-form :model="currentIcon" layout="vertical">
      <a-form-item label="图标名称" required>
        <a-select
          v-model:value="currentIcon.icon_name"
          placeholder="请选择图标名称"
        >
          <a-select-option
            v-for="opt in iconClassificationOptions"
            :key="opt.value"
            :value="opt.label"
          >
            {{ opt.label }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="SVG 图标" required>
        <a-radio-group
          v-model:value="iconUploadMethod"
          style="margin-bottom: 16px"
        >
          <a-radio value="file">上传文件</a-radio>
          <a-radio value="code">手动输入代码</a-radio>
        </a-radio-group>

        <div v-if="iconUploadMethod === 'file'" class="icon-file-upload">
          <input
            type="file"
            ref="iconFileInput"
            @change="handleIconFileChange"
            accept=".svg"
            style="display: none"
          />
          <a-button @click="iconFileInput?.click()" style="margin-bottom: 8px">
            <template #icon><UploadOutlined /></template>
            选择SVG文件
          </a-button>
          <div v-if="selectedIconFileName" class="selected-file-info">
            <span>已选择文件: {{ selectedIconFileName }}</span>
            <a-button type="link" size="small" @click="clearIconFile">
              清除
            </a-button>
          </div>
        </div>

        <a-textarea
          v-if="iconUploadMethod === 'code'"
          v-model:value="currentIcon.icon.data"
          placeholder="请粘贴完整的SVG代码"
          :rows="8"
        />

        <div v-if="currentIcon.icon.data" class="icon-preview-section">
          <label>预览:</label>
          <div
            class="icon-preview-container preview-large"
            v-html="currentIcon.icon.data"
          ></div>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>

  <div v-if="isDrawingMode" class="drawing-tool-panel">
    <div class="panel-header">
      <div class="panel-title">
        <h3>道路绘制工具</h3>
      </div>
      <a-button
        type="text"
        danger
        @click="handleCancelRoadDrawing"
        class="close-btn"
      >
        ✕
      </a-button>
    </div>

    <div class="panel-content">
      <div class="drawing-info">
        <a-alert
          message="道路绘制模式"
          description="在地图上点击添加路径点，至少需要2个点才能完成道路绘制"
          type="info"
          show-icon
          :closable="false"
        />
      </div>

      <div class="drawing-params">
        <div class="param-row">
          <label>道路等级:</label>
          <a-select
            :value="selectedRoadLevel"
            @update:value="$emit('update:selectedRoadLevel', $event)"
            style="width: 140px"
          >
            <a-select-option
              v-for="level in roadLevelOptions"
              :key="level.value"
              :value="level.value"
            >
              {{ level.text }}
            </a-select-option>
          </a-select>
        </div>

        <div class="param-row">
          <label>道路名称:</label>
          <a-input
            :value="currentRoadName"
            @input="$emit('update:currentRoadName', $event.target.value)"
            placeholder="输入道路名称"
            style="width: 140px"
          />
        </div>
      </div>

      <div class="drawing-actions">
        <a-button
          type="primary"
          @click="handleFinishRoadDrawing"
          :disabled="currentDrawingPointsCount < 2"
          size="small"
        >
          完成道路 ({{ currentDrawingPointsCount }}点)
        </a-button>
        <a-button @click="handleCancelRoadDrawing" size="small">
          取消绘制
        </a-button>
        <a-button @click="handleEndRoadDrawing" size="small" type="dashed">
          结束绘制
        </a-button>
      </div>
    </div>
  </div>

  <div v-if="isDrawingAreaMode" class="drawing-tool-panel">
    <div class="panel-header">
      <div class="panel-title">
        <h3>区域绘制工具</h3>
      </div>
      <a-button
        type="text"
        danger
        @click="handleCancelAreaDrawing"
        class="close-btn"
      >
        ✕
      </a-button>
    </div>

    <div class="panel-content">
      <div class="drawing-info">
        <a-alert
          message="区域绘制模式"
          description="请先填写区域名称和选择分类，然后在地图上点击添加区域边界点，至少需要3个点才能完成区域绘制"
          type="success"
          show-icon
          :closable="false"
        />
      </div>

      <div class="drawing-params">
        <div class="param-row">
          <label>区域名称:</label>
          <a-input
            :value="selectedAreaNameForToolbar || ''"
            @input="
              $emit('update:selectedAreaNameForToolbar', $event.target.value)
            "
            placeholder="输入区域名称"
            style="width: 140px"
          />
        </div>

        <div class="param-row">
          <label>区域分类:</label>
          <a-select
            :value="selectedAreaClassification || 'A'"
            @update:value="$emit('update:selectedAreaClassification', $event)"
            placeholder="选择区域分类"
            style="width: 140px"
          >
            <a-select-option value="A">一级区域</a-select-option>
            <a-select-option value="B">二级区域</a-select-option>
            <a-select-option value="C">三级区域</a-select-option>
          </a-select>
        </div>
      </div>

      <div class="drawing-actions">
        <a-button
          type="primary"
          @click="handleFinishAreaDrawing"
          :disabled="
            currentDrawingAreaPointsCount < 3 || !selectedAreaNameForToolbar
          "
          size="small"
        >
          完成区域 ({{ currentDrawingAreaPointsCount }}点)
        </a-button>
        <a-button @click="handleCancelAreaDrawing" size="small">
          取消绘制
        </a-button>
        <a-button @click="handleEndAreaDrawing" size="small" type="dashed">
          结束绘制
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, onUnmounted, computed } from 'vue'
import {
  UploadOutlined,
  SearchOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import iconsApi from '@/api/modules/icons.js' // 导入图标API
import axios from 'axios' // 导入axios进行API请求
import { getBaseUrl } from '@/config/server.config' // 导入API基础URL配置
import eventBus from '@/utils/eventBus' // 导入事件总线
import { useTrunkLinesStore } from '@/stores/trunkLines' // 导入干线Store
import { useRegionsStore } from '@/stores/regions' // 导入区域Store

import { storeToRefs } from 'pinia'

const cityFileInput = ref(null)
const roadFileInput = ref(null)
const singleRoadFileInput = ref(null)

// 路网管理相关
const roadNetworkSearchKeyword = ref('')
const roadNetworkList = ref([])

// 图标库管理
const icons = ref([])
const isIconModalVisible = ref(false)
const currentIcon = ref({ icon: { type: 'svg', data: '' } }) // 初始化，防止v-model绑定出错
const isEditingIcon = ref(false)

// 图标文件上传相关
const iconFileInput = ref(null)
const iconUploadMethod = ref('code') // 默认使用手动输入代码方式
const selectedIconFileName = ref('')

const iconClassificationOptions = [
  { value: '10', label: '边缘控制器' },
  { value: '20', label: '信号机' },
  { value: '30', label: '视频检测器' },
]

// 获取图标列表
const fetchIcons = async () => {
  try {
    const response = await iconsApi.getIcons()
    icons.value = response
  } catch (error) {
    console.error('获取图标列表失败:', error)
  }
}

// 新增图标
const handleAddIcon = () => {
  isEditingIcon.value = false
  currentIcon.value = {
    icon_name: '',
    classification: '',
    icon: { type: 'svg', data: '' },
  }
  // 重置上传方式和文件选择
  iconUploadMethod.value = 'code'
  selectedIconFileName.value = ''
  if (iconFileInput.value) {
    iconFileInput.value.value = ''
  }
  isIconModalVisible.value = true
}

// 编辑图标
const handleEditIcon = icon => {
  isEditingIcon.value = true
  // 深拷贝一个对象，避免直接修改列表中的数据
  currentIcon.value = JSON.parse(JSON.stringify(icon))
  // 编辑时默认使用代码输入方式
  iconUploadMethod.value = 'code'
  selectedIconFileName.value = ''
  if (iconFileInput.value) {
    iconFileInput.value.value = ''
  }
  isIconModalVisible.value = true
}

// 处理图标文件上传
const handleIconFileChange = async event => {
  const file = event.target.files[0]
  if (!file) {
    selectedIconFileName.value = ''
    return
  }

  // 检查文件类型
  if (!file.type.includes('svg') && !file.name.toLowerCase().endsWith('.svg')) {
    message.error('请选择SVG格式的图标文件')
    event.target.value = ''
    selectedIconFileName.value = ''
    return
  }

  // 检查文件大小（限制为1MB）
  if (file.size > 1024 * 1024) {
    message.error('图标文件大小不能超过1MB')
    event.target.value = ''
    selectedIconFileName.value = ''
    return
  }

  selectedIconFileName.value = file.name

  try {
    // 读取文件内容
    const text = await file.text()

    // 验证SVG格式
    if (!text.trim().startsWith('<svg') || !text.trim().includes('</svg>')) {
      message.error('文件内容不是有效的SVG格式')
      event.target.value = ''
      selectedIconFileName.value = ''
      return
    }

    // 将SVG内容设置到图标数据中
    currentIcon.value.icon.data = text.trim()
    message.success('SVG文件读取成功')
  } catch (error) {
    console.error('读取SVG文件失败:', error)
    message.error('读取文件失败，请检查文件格式')
    event.target.value = ''
    selectedIconFileName.value = ''
  }
}

// 清除选择的图标文件
const clearIconFile = () => {
  if (iconFileInput.value) {
    iconFileInput.value.value = ''
  }
  selectedIconFileName.value = ''
  currentIcon.value.icon.data = ''
}

// 删除图标
const handleDeleteIcon = async iconId => {
  try {
    await iconsApi.deleteIcon(iconId)
    message.success('图标删除成功')
    // 乐观更新：直接从本地数组中移除
    const index = icons.value.findIndex(icon => icon.icon_id === iconId)
    if (index !== -1) {
      icons.value.splice(index, 1)
    }
    // 通知父组件刷新设备图标
    emit('refresh-device-icons')
  } catch (error) {
    message.error('图标删除失败')
    console.error('图标删除失败:', error)
  }
}

// 保存图标（新增或更新）
const handleSaveIcon = async () => {
  try {
    if (isEditingIcon.value) {
      // 更新
      const updatedIcon = await iconsApi.updateIcon(
        currentIcon.value.icon_id,
        currentIcon.value
      )
      message.success('图标更新成功')
      // 乐观更新：在本地数组中找到并更新该图标
      const index = icons.value.findIndex(
        icon => icon.icon_id === updatedIcon.icon_id
      )
      if (index !== -1) {
        icons.value.splice(index, 1, updatedIcon)
      }
      // 通知父组件刷新设备图标
      emit('refresh-device-icons')
    } else {
      // 新增
      const newIcon = await iconsApi.createIcon(currentIcon.value)
      message.success('图标创建成功')
      // 乐观更新：将新图标添加到列表的开头，以便立即看到
      icons.value.unshift(newIcon)
      // 如果是新增图标，也通知父组件刷新设备图标
      emit('refresh-device-icons')
    }
    isIconModalVisible.value = false
    // 由于我们已经对本地数据进行了精确的乐观更新，不再需要重新拉取整个列表
    // fetchIcons();
  } catch (error) {
    message.error(`保存图标失败: ${error.message || '请检查网络或联系管理员'}`)
    console.error('保存图标失败:', error)
  }
}

// 监视图标名称的变化，自动更新分类
watch(
  () => currentIcon.value.icon_name,
  newName => {
    if (newName) {
      const selectedOption = iconClassificationOptions.find(
        opt => opt.label === newName
      )
      if (selectedOption) {
        currentIcon.value.classification = selectedOption.value
      }
    }
  }
)

// --- Props ---
const props = defineProps({
  visible: { type: Boolean, default: false },
  initialPanel: { type: String, default: 'data' },
  // from DrawingToolbar
  isDrawingMode: Boolean,
  isDrawingAreaMode: Boolean,
  currentDrawingPointsCount: Number,
  currentDrawingAreaPointsCount: Number,
  selectedRoadLevel: String,
  currentRoadName: String,
  roadLevelOptions: {
    type: Array,
    default: () => [],
  },
  selectedAreaName: { type: String, default: null },
  selectedAreaNameForToolbar: { type: String, default: '' },
  selectedAreaClassification: { type: String, default: 'A' },
  // Settings related props
  roadDisplayMode: { type: String, default: 'auto' },
  hideThreshold: { type: Number, default: 10 },
  primaryThreshold: { type: Number, default: 6 },
  secondaryThreshold: { type: Number, default: 3 },
  // Trunk line related props
  isCreatingTrunkLine: {
    type: Boolean,
    default: false,
  },
  // City highlight related props
  availableCityNames: {
    type: Array,
    default: () => [],
  },
  highlightedCityName: {
    type: String,
    default: '',
  },
})

// --- Emits ---
const emit = defineEmits([
  'update:visible',
  // from DataManager
  'cityDataLoaded',
  'roadDataLoaded',
  'highlight-city-name-updated',
  'add-geojson-feature',
  // from DrawingToolbar
  'toggle-drawing-mode',
  'finalize-current-road',
  'cancel-current-drawing',
  'update:selectedRoadLevel',
  'update:currentRoadName',
  'toggle-draw-area-mode',
  'finalize-current-area',
  'cancel-current-area-drawing',
  'select-region',
  'update:selectedAreaNameForToolbar',
  'update:selectedAreaClassification',
  // Settings related emits
  'update:roadDisplayMode',
  'update:hideThreshold',
  'update:primaryThreshold',
  'update:secondaryThreshold',
  'apply-distance-thresholds',
  'reset-distance-thresholds',
  // Trunk line related emits
  'start-trunk-line-creation',
  'cancel-trunk-line-creation',
  'start-trunk-line-editing',
  'refresh-device-icons',
  // Drawing related emits (new mode)
  'start-road-drawing',
  'cancel-road-drawing',
  'finish-road-drawing',
  'start-area-drawing',
  'cancel-area-drawing',
  'finish-area-drawing',
])

// Modal and Tabs logic
const activeTabKey = ref('data')
const drawingTabKey = ref('road') // 绘制工具子标签页状态

watch(
  () => props.initialPanel,
  newPanel => {
    if (newPanel) {
      activeTabKey.value = newPanel
    }
  }
)

// --- 干线管理相关逻辑 ---
const isCreatingTrunkLine = computed(() => props.isCreatingTrunkLine)
const trunkLineName = ref('')

// 干线Store
const trunkLinesStore = useTrunkLinesStore()
const { loading: loadingTrunkLines } = storeToRefs(trunkLinesStore)

// 区域Store
const regionsStore = useRegionsStore()

// 移除useDataManager的使用，直接使用IndexedDB操作

// 搜索和过滤状态
const searchKeyword = ref('')
const selectedClassification = ref(undefined)

// 区域搜索和过滤状态
const regionSearchKeyword = ref('')
const selectedRegionClassification = ref(undefined)

// 计算属性
const availableClassifications = computed(() => {
  return trunkLinesStore.getAllClassifications
})

const trunkLineStats = computed(() => {
  return trunkLinesStore.trunkLinesStats
})

const filteredTrunkLines = computed(() => {
  return trunkLinesStore.searchTrunkLinesAdvanced({
    keyword: searchKeyword.value,
    classification: selectedClassification.value,
  })
})

// 区域相关计算属性
const regionStats = computed(() => {
  return {
    total: regionsStore.regions.length,
  }
})

const filteredRegions = computed(() => {
  let filtered = regionsStore.regions

  // 按关键词过滤
  if (regionSearchKeyword.value) {
    const keyword = regionSearchKeyword.value.toLowerCase()
    filtered = filtered.filter(region =>
      region.region_name.toLowerCase().includes(keyword)
    )
  }

  // 按分类过滤
  if (selectedRegionClassification.value) {
    filtered = filtered.filter(
      region => region.classification === selectedRegionClassification.value
    )
  }

  return filtered
})

// 移除分页配置，统一使用滚动方式

// 开始创建干线
const handleCreateTrunkLine = () => {
  if (isCreatingTrunkLine.value) {
    // 取消创建
    handleCancelTrunkLineCreation()
  } else {
    // 开始创建
    trunkLineName.value = ''
    emit('start-trunk-line-creation')
    // 隐藏地图配置面板，进入干线创建模式
    emit('update:visible', false)
  }
}

// 取消创建干线
const handleCancelTrunkLineCreation = () => {
  trunkLineName.value = ''
  emit('cancel-trunk-line-creation')
  // 退出干线创建模式，重新显示地图配置面板，并切换到干线管理标签页
  emit('update:visible', true)
  activeTabKey.value = 'trunklines'
}

// 干线管理方法
const handleRefreshTrunkLines = async () => {
  try {
    await trunkLinesStore.loadTrunkLinesFromServer()
    message.success('干线列表已刷新')
  } catch (error) {
    console.error('刷新干线列表失败:', error)
    message.error('刷新失败')
  }
}

// 移除干线显示隐藏功能，不再需要

const handleEditTrunkLine = trunkLine => {
  // 开始编辑干线
  trunkLinesStore.startEditing(trunkLine.route_id)
  console.log('编辑干线:', trunkLine.route_name)

  // 发射编辑事件到父组件，传递干线数据
  emit('start-trunk-line-editing', trunkLine)

  // 隐藏地图配置面板，进入干线编辑模式
  emit('update:visible', false)
}

const handleDeleteTrunkLine = async routeId => {
  try {
    const success = await trunkLinesStore.deleteTrunkLine(routeId)
    if (success) {
      // 通知父组件移除渲染
      emit('delete-trunk-line', routeId)
    }
  } catch (error) {
    console.error('删除干线失败:', error)
    message.error('删除失败')
  }
}

watch(
  () => props.visible,
  newVal => {
    if (newVal && props.initialPanel) {
      activeTabKey.value = props.initialPanel
    }
  }
)

const handleVisibleChange = val => {
  emit('update:visible', val)
}

// --- Logic from DataManager ---
const highlightCityInput = ref('')
const statusMessage = ref('')
const geoJsonText = ref('')

// 同步高亮城市名称
watch(
  () => props.highlightedCityName,
  newValue => {
    highlightCityInput.value = newValue || ''
  },
  { immediate: true }
)

// MongoDB API URL从配置文件获取
const API_BASE_URL = getBaseUrl('geoData')

// 引入统一的IndexedDB配置
import {
  DB_CONFIG,
  initIndexedDB as initSharedDB,
} from '@/utils/indexedDBConfig'

// 使用统一配置中的存储名
const BOUNDARY_STORE = DB_CONFIG.STORES.BOUNDARY
const ROAD_NETWORK_STORE = DB_CONFIG.STORES.ROAD_NETWORK
let db = null

// 初始化 IndexedDB
const initIndexedDB = async () => {
  try {
    db = await initSharedDB()
    return db
  } catch (error) {
    console.error('IndexedDB 初始化失败:', error)
    statusMessage.value = 'IndexedDB 打开失败，无法缓存地理数据'
    throw error
  }
}

// 引入统一的数据库操作函数
import { saveToIndexedDB as saveShared } from '@/utils/indexedDBConfig'
import roadDeleteService from '@/utils/RoadDeleteService'

// 保存边界数据到 IndexedDB
const saveBoundaryToIndexedDB = async (id, data) => {
  try {
    // 保存到边界存储
    await saveShared(db, BOUNDARY_STORE, id, data)

    // 同时保存到CityModel使用的cityData存储，使用default作为key
    await saveShared(db, DB_CONFIG.STORES.CITY_DATA, 'default', data)

    return true
  } catch (error) {
    console.error('保存行政区划轮廓失败:', error)
    return false
  }
}

// 保存路网数据到 IndexedDB
const saveRoadNetworkToIndexedDB = async (id, data) => {
  try {
    // 保存到路网存储
    await saveShared(db, ROAD_NETWORK_STORE, id, data)

    // 同时保存到CityModel使用的roadData存储，使用default作为key
    await saveShared(db, DB_CONFIG.STORES.ROAD_DATA, 'default', data)

    return true
  } catch (error) {
    console.error('保存路网数据失败:', error)
    return false
  }
}

// --- 文件上传处理 ---
const handleFileChange = async (event, type) => {
  const file = event.target.files[0]
  if (!file) {
    statusMessage.value = '未选择文件。'
    return
  }
  statusMessage.value = `正在处理 ${file.name}...`
  try {
    const text = await file.text()
    const jsonData = JSON.parse(text)

    if (type === 'city') {
      // 上传行政区划轮廓数据，固定使用ID为1
      uploadBoundaryData(jsonData)
    } else if (type === 'road') {
      // 上传路网数据，固定使用ID为1
      uploadRoadNetworkData(jsonData)
    }
  } catch (error) {
    console.error('文件处理错误:', error)
    statusMessage.value = `处理文件 ${file.name} 失败: ${error.message}`
  }
}

// --- 上传行政区划轮廓数据 ---
const uploadBoundaryData = async boundaryData => {
  const id = 1 // 固定使用ID为1
  statusMessage.value = `正在上传行政区划轮廓...`

  try {
    const requestPayload = {
      boundary_id: id,
      boundary: boundaryData,
    }

    const payloadStr = JSON.stringify(requestPayload)
    const payloadSize = new Blob([payloadStr]).size
    console.log(`完整请求负载大小: ${(payloadSize / 1024 / 1024).toFixed(2)}MB`)

    statusMessage.value = `正在上传行政区划轮廓 (${(
      payloadSize /
      1024 /
      1024
    ).toFixed(2)}MB)...`

    const response = await axios.post(
      `${API_BASE_URL}/boundarys`,
      requestPayload,
      {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 60000, // 60秒超时
        maxContentLength: Infinity,
        maxBodyLength: Infinity,
      }
    )

    if (response.data) {
      // 保存到 IndexedDB
      await saveBoundaryToIndexedDB(id, boundaryData)

      // 加载到地图
      emit('cityDataLoaded', boundaryData)
      statusMessage.value = `行政区划轮廓上传成功: ${'数据已上传'}`
    } else {
      statusMessage.value = '上传成功，但服务器响应格式不正确'
    }
  } catch (error) {
    console.error('上传行政区划轮廓失败:', error)
    console.error('错误详情:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      headers: error.response?.headers,
    })

    if (error.response?.status === 413) {
      statusMessage.value = `上传失败: 数据过大 (${error.response.status})`
    } else if (error.code === 'ECONNABORTED') {
      statusMessage.value = '上传失败: 请求超时，数据可能过大'
    } else {
      statusMessage.value = `上传行政区划轮廓失败: ${
        error.response?.data?.error || error.message
      }`
    }
  }
}

// --- 上传路网数据 ---
const uploadRoadNetworkData = async roadNetworkData => {
  const id = 1 // 固定使用ID为1
  statusMessage.value = `正在上传路网数据...`

  try {
    const requestPayload = {
      network_id: id,
      road_network: roadNetworkData,
    }

    const payloadStr = JSON.stringify(requestPayload)
    const payloadSize = new Blob([payloadStr]).size

    statusMessage.value = `正在上传路网数据 (${(
      payloadSize /
      1024 /
      1024
    ).toFixed(2)}MB)...`

    const response = await axios.post(
      `${API_BASE_URL}/road_networks`,
      requestPayload,
      {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 60000, // 60秒超时
        maxContentLength: Infinity,
        maxBodyLength: Infinity,
      }
    )

    if (response.data) {
      // 保存到 IndexedDB
      await saveRoadNetworkToIndexedDB(id, roadNetworkData)

      // 加载到地图
      emit('roadDataLoaded', roadNetworkData)
      statusMessage.value = `路网数据上传成功: ${
        response.data.message || '数据已上传'
      }`
    } else {
      statusMessage.value = '上传成功，但服务器响应格式不正确'
    }
  } catch (error) {
    console.error('上传路网数据失败:', error)
    console.error('错误详情:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      headers: error.response?.headers,
    })

    if (error.response?.status === 413) {
      statusMessage.value = `上传失败: 数据过大 (${error.response.status})`
    } else if (error.code === 'ECONNABORTED') {
      statusMessage.value = '上传失败: 请求超时，数据可能过大'
    } else {
      statusMessage.value = `上传路网数据失败: ${
        error.response?.data?.error || error.message
      }`
    }
  }
}

// 城市选择过滤方法
const filterCityOption = (input, option) => {
  return option.value.toLowerCase().includes(input.toLowerCase())
}

// 修改triggerHighlightCityUpdate - 现在通过选择器触发
const triggerHighlightCityUpdate = () => {
  emit('highlight-city-name-updated', highlightCityInput.value || '')
  statusMessage.value = `高亮城市已更新为: ${
    highlightCityInput.value || '无(清除操作)'
  }`
}

// 保留用于单个道路处理的函数
const isValidGeoJsonFeature = json => {
  return (
    json &&
    json.type === 'Feature' &&
    json.geometry &&
    json.geometry.type === 'LineString' &&
    Array.isArray(json.geometry.coordinates)
  )
}

const isValidGeoJsonFeatureCollection = json => {
  return (
    json && json.type === 'FeatureCollection' && Array.isArray(json.features)
  )
}

const handleSingleRoadFileUpload = event => {
  const file = event.target.files[0]
  if (!file) {
    return
  }
  statusMessage.value = `正在处理单个道路文件: ${file.name}...`
  // eslint-disable-next-line no-undef
  const reader = new FileReader()
  reader.onload = e => {
    try {
      const content = e.target.result
      const jsonData = JSON.parse(content)
      let featuresEmitted = 0
      if (isValidGeoJsonFeature(jsonData)) {
        emitGeoJsonFeature(jsonData, 'geojson-file-single')
        featuresEmitted = 1
      } else if (isValidGeoJsonFeatureCollection(jsonData)) {
        jsonData.features.forEach(feature => {
          if (isValidGeoJsonFeature(feature)) {
            emitGeoJsonFeature(feature, 'geojson-file-collection-item')
            featuresEmitted++
          }
        })
        if (featuresEmitted === 0) {
          statusMessage.value = `文件 '${file.name}' 中的 FeatureCollection 不包含有效的 LineString Feature。`
        }
      } else {
        statusMessage.value = `文件 '${file.name}' 不是有效的 GeoJSON LineString Feature 或 FeatureCollection。`
      }
      if (featuresEmitted > 0) {
        statusMessage.value = `从文件 '${file.name}' 成功导入 ${featuresEmitted} 条道路。`
      }
    } catch (error) {
      statusMessage.value = `解析 GeoJSON 文件 '${file.name}' 失败: ${error.message}`
    } finally {
      if (singleRoadFileInput.value) {
        singleRoadFileInput.value.value = ''
      }
    }
  }
  reader.readAsText(file)
}

const processPastedGeoJson = () => {
  if (!geoJsonText.value.trim()) {
    return
  }
  statusMessage.value = '正在处理粘贴的 GeoJSON 数据...'
  try {
    const jsonData = JSON.parse(geoJsonText.value)
    let featuresEmitted = 0
    if (isValidGeoJsonFeature(jsonData)) {
      emitGeoJsonFeature(jsonData, 'geojson-pasted-single')
      featuresEmitted = 1
    } else if (isValidGeoJsonFeatureCollection(jsonData)) {
      jsonData.features.forEach(feature => {
        if (isValidGeoJsonFeature(feature)) {
          emitGeoJsonFeature(feature, 'geojson-pasted-collection-item')
          featuresEmitted++
        }
      })
      if (featuresEmitted === 0) {
        statusMessage.value =
          '粘贴的 FeatureCollection 不包含有效的 LineString Feature。'
      }
    } else {
      statusMessage.value =
        '粘贴的数据不是有效的 GeoJSON LineString Feature 或 FeatureCollection。'
    }
    if (featuresEmitted > 0) {
      statusMessage.value = `成功从粘贴内容中导入 ${featuresEmitted} 条道路。`
      geoJsonText.value = ''
    }
  } catch (error) {
    statusMessage.value = `解析粘贴的 GeoJSON 数据失败: ${error.message}`
  }
}

const emitGeoJsonFeature = (featureData, sourceIdentifier) => {
  const featureToEmit = JSON.parse(JSON.stringify(featureData))
  if (!featureToEmit.properties) {
    featureToEmit.properties = {}
  }
  featureToEmit.properties.source = sourceIdentifier
  emit('add-geojson-feature', featureToEmit)
}

const clearSingleRoadFileInput = () => {
  if (singleRoadFileInput.value) {
    singleRoadFileInput.value.value = ''
  }
}

// --- 绘制工具逻辑 (新模式) ---
// 开始道路绘制
const handleStartRoadDrawing = () => {
  drawingTabKey.value = 'road'
  emit('toggle-drawing-mode')
  // 隐藏地图配置面板，进入道路绘制模式
  emit('update:visible', false)
}

// 开始区域绘制
const handleStartAreaDrawing = () => {
  drawingTabKey.value = 'area'
  emit('toggle-draw-area-mode')
  // 隐藏地图配置面板，进入区域绘制模式
  emit('update:visible', false)
}

// 取消道路绘制
const handleCancelRoadDrawing = () => {
  emit('cancel-current-drawing')
  // 退出道路绘制模式，重新显示地图配置面板，并切换到绘制工具标签页
  emit('update:visible', true)
  activeTabKey.value = 'drawing'
  drawingTabKey.value = 'road'
}

// 取消区域绘制
const handleCancelAreaDrawing = () => {
  emit('cancel-current-area-drawing')
  // 退出区域绘制模式，重新显示地图配置面板，并切换到绘制工具标签页
  emit('update:visible', true)
  activeTabKey.value = 'drawing'
  drawingTabKey.value = 'area'
}

// 完成道路绘制
const handleFinishRoadDrawing = () => {
  emit('finalize-current-road')
  // 完成后重新显示地图配置面板，停留在绘制工具标签页
  emit('update:visible', true)
  activeTabKey.value = 'drawing'
  drawingTabKey.value = 'road'
}

// 完成区域绘制
const handleFinishAreaDrawing = () => {
  emit('finalize-current-area')
  // 完成后重新显示地图配置面板，停留在绘制工具标签页
  emit('update:visible', true)
  activeTabKey.value = 'drawing'
  drawingTabKey.value = 'area'
}

// 结束道路绘制模式（不保存当前绘制）
const handleEndRoadDrawing = () => {
  emit('toggle-drawing-mode') // 结束绘制模式
  // 返回到绘制工具面板
  emit('update:visible', true)
  activeTabKey.value = 'drawing'
  drawingTabKey.value = 'road'
}

// 结束区域绘制模式（不保存当前绘制）
const handleEndAreaDrawing = () => {
  emit('toggle-draw-area-mode') // 结束绘制模式
  // 返回到绘制工具面板
  emit('update:visible', true)
  activeTabKey.value = 'drawing'
  drawingTabKey.value = 'area'
}

// --- 区域管理方法 ---
// 刷新区域列表
const refreshRegionsList = async () => {
  try {
    await regionsStore.loadRegionsFromServer()
    message.success('区域列表已刷新')
  } catch (error) {
    console.error('刷新区域列表失败:', error)
    message.error('刷新失败')
  }
}

// 选择区域
const handleSelectRegion = region => {
  // 通过emit通知父组件选择了区域
  emit('select-region', region)
}

// 删除区域
const handleDeleteRegion = async region => {
  try {
    await regionsStore.removeRegion(region.region_id)
    message.success(`区域 "${region.region_name}" 已删除`)
  } catch (error) {
    console.error('删除区域失败:', error)
    message.error('删除失败')
  }
}

// 道路显示模式变更处理
const handleRoadDisplayModeChange = value => {
  emit('update:roadDisplayMode', value)
}

// 应用距离阈值设置
const applyDistanceThresholds = () => {
  emit('apply-distance-thresholds', {
    hide: props.hideThreshold,
    primary: props.primaryThreshold,
    secondary: props.secondaryThreshold,
  })
}

// 重置为默认阈值
const resetToDefaultThresholds = () => {
  emit('reset-distance-thresholds')
}

// 计算属性：动态阈值描述
const thresholdDescription = computed(() => {
  return `当前设置：主干道在${props.hideThreshold || 10}以下显示，次干道在${
    props.primaryThreshold || 5
  }以下显示，支线在${props.secondaryThreshold || 2}以下显示`
})

// 道路管理计算属性
const filteredRoadNetworks = computed(() => {
  if (!roadNetworkSearchKeyword.value) {
    return roadNetworkList.value
  }

  const keyword = roadNetworkSearchKeyword.value.toLowerCase()
  return roadNetworkList.value.filter(road => {
    // 支持按道路名称、类型、ID搜索
    const name = road.name?.toLowerCase() || ''
    const type = road.typeDisplay?.toLowerCase() || ''
    const id = road.id?.toString().toLowerCase() || ''

    return (
      name.includes(keyword) || type.includes(keyword) || id.includes(keyword)
    )
  })
})

// 道路类型显示映射
const getRoadTypeDisplay = roadType => {
  const typeMap = {
    motorway: '高速公路',
    trunk: '快速路',
    primary: '主干道',
    secondary: '次干道',
    tertiary: '支路',
    residential: '住宅道路',
    service: '服务道路',
    unclassified: '未分类道路',
    footway: '人行道',
    cycleway: '自行车道',
    path: '小径',
    track: '土路',
    living_street: '生活街道',
    pedestrian: '步行街',
    road: '一般道路',
  }
  return typeMap[roadType] || roadType || '未知类型'
}

// 按道路名称合并路段features（复用SmartRoadLabelManager的逻辑）
const mergeRoadFeaturesByName = roadFeatures => {
  const roadsByName = new Map()

  // 按道路名称分组
  roadFeatures.forEach(feature => {
    const roadName = feature.properties?.name
    if (!roadName) {
      return // 跳过没有名称的道路
    }

    const roadType = feature.properties?.highway || 'unclassified'

    if (!roadsByName.has(roadName)) {
      roadsByName.set(roadName, {
        name: roadName,
        segments: [],
        roadType: roadType,
      })
    }

    const roadData = roadsByName.get(roadName)
    roadData.segments.push({
      feature,
      coordinates: feature.geometry.coordinates,
    })

    // 更新道路类型（使用第一个遇到的类型）
    if (!roadData.roadType) {
      roadData.roadType = roadType
    }
  })

  return roadsByName
}

// 路网管理功能函数
const loadRoadNetworkList = async () => {
  try {
    if (!db) {
      console.warn('数据库未初始化')
      return
    }

    // 获取所有路网数据
    const transaction = db.transaction(['roadNetworks'], 'readonly')
    const store = transaction.objectStore('roadNetworks')
    const request = store.getAll()

    request.onsuccess = () => {
      const networks = request.result || []
      console.log('原始路网数据:', networks)

      // 提取并合并道路数据
      const mergedRoads = []

      networks.forEach(networkItem => {
        const actualData = networkItem.data || networkItem
        console.log('路网数据结构:', actualData)

        if (actualData?.features && Array.isArray(actualData.features)) {
          console.log('发现features数组，长度:', actualData.features.length)

          // 使用与SmartRoadLabelManager相同的合并逻辑
          const roadsByName = mergeRoadFeaturesByName(actualData.features)

          // 转换为道路列表格式
          roadsByName.forEach((roadData, roadName) => {
            mergedRoads.push({
              id: roadName, // 使用道路名称作为ID
              name: roadName,
              type: roadData.roadType,
              typeDisplay: getRoadTypeDisplay(roadData.roadType),
              segmentCount: roadData.segments.length,
              segments: roadData.segments,
              networkId: networkItem.id,
              networkData: actualData,
            })
          })
        }
      })

      roadNetworkList.value = mergedRoads
      console.log('合并后的道路列表:', mergedRoads.length, '条道路')
      console.log('道路详情:', mergedRoads)
    }

    request.onerror = () => {
      console.error('加载路网列表失败:', request.error)
      message.error('加载路网列表失败')
    }
  } catch (error) {
    console.error('加载路网列表失败:', error)
    message.error('加载路网列表失败')
  }
}

// 道路修改功能暂未实现的提示
const handleEditRoadNotImplemented = () => {
  message.info('道路修改功能暂未实现')
}

// 操作状态管理
const isRoadOperationInProgress = ref(false)

// 删除道路（使用道路删除服务）
const handleDeleteRoad = async road => {
  try {
    // 防止并发操作
    if (isRoadOperationInProgress.value) {
      message.warning('道路操作正在进行中，请稍候...')
      return
    }

    if (!db) {
      message.error('数据库未初始化')
      return
    }

    isRoadOperationInProgress.value = true

    // 从IndexedDB获取路网数据
    const networkItem = await new Promise((resolve, reject) => {
      const transaction = db.transaction(['roadNetworks'], 'readonly')
      const store = transaction.objectStore('roadNetworks')
      const getRequest = store.get(road.networkId)

      getRequest.onsuccess = () => {
        const networkItem = getRequest.result
        if (!networkItem) {
          reject(new Error('找不到对应的路网数据'))
          return
        }
        resolve(networkItem)
      }

      getRequest.onerror = () => {
        reject(new Error('获取路网数据失败'))
      }
    })

    // 提取路网数据
    let roadNetworkData = networkItem.data || networkItem
    let roadNetworkGeoJson = roadNetworkData

    if (
      roadNetworkData &&
      roadNetworkData.data &&
      roadNetworkData.data.type === 'FeatureCollection'
    ) {
      roadNetworkGeoJson = roadNetworkData.data
    }

    // 确保有有效的GeoJSON结构
    if (
      !roadNetworkGeoJson ||
      roadNetworkGeoJson.type !== 'FeatureCollection'
    ) {
      roadNetworkGeoJson = {
        type: 'FeatureCollection',
        features: [],
      }
    }

    // 使用道路删除服务删除道路
    const result = await roadDeleteService.deleteRoad({
      roadId: road.id,
      roadName: road.name,
      object3D: null, // MapConfigModal中没有3D对象引用
      roadNetworkGroup: null, // MapConfigModal中没有路网组引用
      dynamicRoadLoader: null, // MapConfigModal中没有动态加载器引用
      roadNetworkData: roadNetworkGeoJson,
      sceneManager: null, // MapConfigModal中没有场景管理器引用
      saveToIndexedDB: async (storeName, key, data) => {
        // 更新IndexedDB
        const updateTransaction = db.transaction(['roadNetworks'], 'readwrite')
        const updateStore = updateTransaction.objectStore('roadNetworks')

        const updatedNetworkItem = {
          ...networkItem,
          data: data,
          timestamp: Date.now(),
        }

        return new Promise((resolve, reject) => {
          const putRequest = updateStore.put(updatedNetworkItem)
          putRequest.onsuccess = () => resolve()
          putRequest.onerror = () => reject(new Error('更新IndexedDB失败'))
        })
      },
      eventBus,
      onProgress: () => {},
    })

    if (result.success) {
      // 更新本地状态 - 从道路列表中移除已删除的道路
      roadNetworkList.value = roadNetworkList.value.filter(
        r => r.id !== road.id
      )

      // 立即触发地图重新加载
      eventBus.emit('roadNetworkUpdated')

      // 显示删除结果消息
      if (result.serverUpdateSuccess) {
        message.success(
          `道路 "${road.name}" 及其 ${result.deletedCount} 个路段删除成功`
        )
      } else {
        message.warning(
          `道路 "${road.name}" 及其 ${result.deletedCount} 个路段本地删除成功，但服务器同步可能存在问题`
        )
      }
    }
  } catch (error) {
    console.error('删除道路失败:', error)
    message.error(`删除道路失败: ${error.message}`)
  } finally {
    // 重置操作状态
    isRoadOperationInProgress.value = false
  }
}

// 修改 onMounted - 初始化IndexedDB和各种Store
onMounted(async () => {
  try {
    await initIndexedDB()
    // 初始化区域Store
    await regionsStore.initialize()
    // 等待一小段时间确保数据库完全初始化
    await new Promise(resolve => setTimeout(resolve, 100))
    // 加载路网数据列表
    await loadRoadNetworkList()
  } catch (error) {
    console.error('初始化失败:', error)
    statusMessage.value = 'IndexedDB 初始化失败，将无法缓存地理数据'
  }
})

// 组件卸载时清理
onUnmounted(() => {
  // 关闭数据库连接
  if (db) {
    db.close()
  }

  // 清理事件监听
  eventBus.off('mapConfig-destroyed', resetComponentState)
})

// 标记是否已加载图标，避免重复请求
const iconsLoaded = ref(false)

// 直接响应标签页切换事件
const onTabChange = key => {
  if (key === 'icons' && !iconsLoaded.value) {
    fetchIcons()
    iconsLoaded.value = true
  }
}

// 监听visible和activeTabKey，确保面板显示且为图标管理时自动加载图标库
watch([() => props.visible, activeTabKey], ([visible, tabKey]) => {
  if (visible && tabKey === 'icons' && !iconsLoaded.value) {
    fetchIcons()
    iconsLoaded.value = true
  }
})

// 监听visible和activeTabKey，确保面板显示且为道路管理时自动加载道路列表
watch([() => props.visible, activeTabKey], ([visible, tabKey]) => {
  if (visible && tabKey === 'roadDraw' && roadNetworkList.value.length === 0) {
    loadRoadNetworkList()
  }
})

// 重置组件所有状态到初始值
const resetComponentState = () => {
  // 重置所有内部状态变量到初始值
  activeTabKey.value = 'data'
  highlightCityInput.value = ''
  geoJsonText.value = ''
  statusMessage.value = ''
  // 清除文件输入
  if (cityFileInput.value) {
    cityFileInput.value.value = ''
  }
  if (roadFileInput.value) {
    roadFileInput.value.value = ''
  }
  if (singleRoadFileInput.value) {
    singleRoadFileInput.value.value = ''
  }
}
</script>

<style lang="scss">
/* 面板内容样式 */
.panel-content {
  padding: 20px;
  max-height: 75vh;
  overflow-y: auto;

  h4 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 16px;
  }

  .setting-item,
  .attribute-item {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;

    label {
      margin-right: 15px;
      min-width: 120px;
      margin-bottom: 8px;
      font-size: 14px;
      flex-shrink: 0;
    }

    // 按钮不占满宽度
    .ant-btn {
      flex: none;
    }

    // 输入框和选择器可以适当扩展
    .ant-input,
    .ant-select,
    .ant-input-number {
      min-width: 200px;
      max-width: 400px;
    }

    // 文本域可以更宽一些
    .ant-input-textarea {
      min-width: 300px;
      max-width: 600px;
    }
  }

  .description {
    font-size: 13px;
    color: #666;
    margin-bottom: 12px;
    line-height: 1.5;
  }
}

/* 阈值提示样式 */
.threshold-hint {
  font-size: 12px;
  color: #1890ff;
  margin-top: 6px;
  font-style: italic;
  opacity: 0.9;
  padding: 4px 8px;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 4px;
  border-left: 3px solid rgba(24, 144, 255, 0.5);
}

.status-message {
  color: #1890ff;
  font-size: 14px;
  padding: 8px;
  background: #f0f9ff;
  border-radius: 4px;
  margin-top: 10px;
}

/* 图标预览容器 */
.icon-preview-container {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 4px;
  overflow: hidden;

  &.placeholder {
    color: #999;
    font-size: 12px;
  }

  &.preview-large {
    width: 80px;
    height: 80px;
    border: 1px solid #d9d9d9;
    background-color: #fafafa;
  }

  svg {
    width: 100%;
    height: 100%;
  }
}

// 图标文件上传相关样式
.icon-file-upload {
  .selected-file-info {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background-color: #f0f9ff;
    border: 1px solid #91d5ff;
    border-radius: 4px;
    font-size: 12px;
    color: #1890ff;
  }
}

.icon-preview-section {
  margin-top: 16px;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px solid #d9d9d9;

  label {
    display: block;
    margin-bottom: 8px;
    color: #1890ff;
    font-size: 12px;
    font-weight: 500;
  }
}

// 图标库管理
.icon-list-horizontal {
  .icon-list-item {
    background: #fafafa;
    border: 1px solid #d9d9d9;
    padding: 12px 16px;
    margin-bottom: 12px;
    border-radius: 4px;
    transition: all 0.3s;

    &:hover {
      border-color: #1890ff;
      box-shadow: 0 0 8px rgba(24, 144, 255, 0.3);
    }

    .ant-list-item-meta {
      align-items: center;
    }

    .ant-list-item-action {
      margin-left: 16px;

      > li {
        padding: 0 8px;
        color: #666;
        font-size: 16px;
        transition: color 0.3s;

        &:hover {
          color: #1890ff;
        }
      }
    }
  }
}

/* 统一列表容器样式 */
.list-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
}

/* 区域列表样式 */
.area-title,
.region-title,
.trunk-line-title {
  display: flex;
  align-items: center;
  gap: 8px;

  .area-name,
  .region-name,
  .trunk-line-name {
    font-weight: 500;
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.area-info {
  font-size: 12px;
  color: #666;
}

/* 绘制工具面板样式 */
.drawing-tool-panel {
  position: fixed;
  top: 200px;
  right: 20px;
  width: 320px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 80vh;
  overflow-y: auto;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;

    .panel-title {
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }
    }

    .close-btn {
      padding: 4px 8px;
      font-size: 16px;
      line-height: 1;
    }
  }

  .panel-content {
    padding: 20px;

    .drawing-info {
      margin-bottom: 20px;
    }

    .drawing-params {
      margin-bottom: 20px;

      .param-row {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        label {
          min-width: 80px;
          margin-right: 12px;
          font-size: 14px;
          color: #262626;
        }
      }
    }

    .drawing-actions {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;

      .ant-btn {
        flex: 1;
        min-width: 80px;
      }
    }
  }
}

/* 干线创建模式提示样式 */
.creation-mode-hint {
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 20px;

  .hint-content {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;

    .hint-icon {
      color: #fa8c16;
      font-size: 16px;
      margin-right: 8px;
      margin-top: 2px;
    }

    .hint-text {
      flex: 1;

      h4 {
        margin: 0 0 8px 0;
        color: #d46b08;
        font-size: 14px;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #8c8c8c;
        font-size: 13px;
        line-height: 1.5;
      }
    }
  }

  .exit-creation-btn {
    width: 100%;
  }
}

/* 选中区域样式 */
.selected-region {
  background-color: #e6f7ff !important;
  border-color: #1890ff !important;
}

/* 路网管理列表样式 */
.road-network-manager {
  .list-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background: #fafafa;
  }

  .road-network-list {
    padding: 12px;

    // 使用CSS Grid实现响应式多列布局
    .ant-list-items {
      display: grid;
      grid-template-columns: repeat(
        auto-fit,
        minmax(280px, 1fr)
      ); // 自适应列数，最小宽度280px
      gap: 12px; // 增加间距以提供更好的视觉分离
    }
  }

  .road-network-item {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    background: white;
    transition: all 0.2s ease;
    margin-bottom: 0 !important; // 覆盖ant-list-item的默认margin
    padding: 12px !important; // 覆盖ant-list-item的默认padding

    &:hover {
      background-color: #f5f5f5;
      border-color: #d9d9d9;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    // 调整ant-list-item-meta的布局
    .ant-list-item-meta {
      margin-bottom: 0; // 减少底部间距

      .ant-list-item-meta-content {
        .ant-list-item-meta-title {
          margin-bottom: 0; // 移除标题底部间距

          .road-info-horizontal {
            display: flex;
            align-items: center;
            gap: 12px; // 元素之间的间距
            flex-wrap: wrap; // 允许换行以适应小屏幕

            .road-name {
              font-weight: 500;
              color: #262626;
              font-size: 14px;
              line-height: 1.4;
              word-break: break-all;
              flex: 0 0 auto; // 不伸缩，保持内容宽度
            }

            .road-type {
              background: #e6f7ff;
              color: #1890ff;
              padding: 2px 6px;
              border-radius: 3px;
              font-weight: 500;
              font-size: 11px;
              flex: 0 0 auto; // 不伸缩，保持内容宽度
              white-space: nowrap; // 防止文字换行
            }

            .segment-count {
              color: #666;
              font-size: 11px;
              flex: 0 0 auto; // 不伸缩，保持内容宽度
              white-space: nowrap; // 防止文字换行
            }
          }
        }
      }
    }

    // 调整操作按钮区域
    .ant-list-item-action {
      margin-left: 8px;

      .ant-list-item-action-split {
        display: none; // 隐藏分隔符
      }
    }
  }

  // 响应式设计：针对不同屏幕尺寸优化
  @media (max-width: 600px) {
    .road-network-list {
      padding: 8px; // 减少内边距

      .ant-list-items {
        grid-template-columns: 1fr; // 强制单列布局
        gap: 8px; // 减少间距
      }
    }

    // 小屏幕时调整水平布局
    .road-network-item {
      .ant-list-item-meta {
        .ant-list-item-meta-content {
          .ant-list-item-meta-title {
            .road-info-horizontal {
              gap: 8px; // 减少间距

              .road-name {
                font-size: 13px; // 稍微减小字体
                flex: 1 1 auto; // 允许伸缩，占用更多空间
                min-width: 0; // 允许收缩
              }

              .road-type {
                font-size: 10px; // 减小字体
                padding: 1px 4px; // 减小内边距
              }

              .segment-count {
                font-size: 10px; // 减小字体
              }
            }
          }
        }
      }
    }
  }

  // 中等屏幕优化
  @media (min-width: 601px) and (max-width: 900px) {
    .road-network-list .ant-list-items {
      grid-template-columns: repeat(
        auto-fit,
        minmax(250px, 1fr)
      ); // 稍小的最小宽度
    }
  }

  // 大屏幕优化
  @media (min-width: 1400px) {
    .road-network-list .ant-list-items {
      gap: 16px; // 增加间距
      grid-template-columns: repeat(
        auto-fit,
        minmax(320px, 1fr)
      ); // 稍大的最小宽度
    }
  }
}
</style>
