<template></template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { throttle } from 'lodash'
import * as THREE from 'three'

const props = defineProps({
  // Three.js 相关属性
  sceneManager: Object, // 场景管理器
  trunkLineRenderer: Object, // 干线渲染器
  picking: Object, // 统一拾取管线
})

const emit = defineEmits([
  'trunkLineHover', // 干线悬停事件
  'trunkLineHoverEnd', // 干线悬停结束事件
  'trunkLineLeftClick', // 干线左键点击事件
  'trunkLineRightClick', // 干线右键点击事件
])

// 内部状态
const hoveredTrunkLine = ref(null) // 当前鼠标悬停的干线
const isSceneInteracting = ref(false) // 场景交互状态

// 鼠标移动优化相关
const lastMousePosition = ref({ x: 0, y: 0 })
const MOUSE_MOVE_THRESHOLD = 3 // 像素阈值，小于此值不触发检测

// 构建干线事件数据的工具函数
const buildTrunkLineEvent = (trunkLineObject, originalEvent = null) => {
  const userData = trunkLineObject.userData
  const trunkLineId = userData.trunkLineId

  // 从渲染器获取完整的干线数据
  const renderInfo =
    props.trunkLineRenderer?.renderedTrunkLines?.get(trunkLineId)
  const trunkLineData = renderInfo?.trunkLine

  // 如果有原始事件，计算鼠标在3D空间中的位置
  let mouseWorldPosition = null
  if (originalEvent && props.picking) {
    const { ndc } = props.picking.getNDCFromDOM(originalEvent)
    const hit = props.picking.pick(trunkLineObject, {
      ndc,
      recursive: false,
      line2Threshold: 10,
    })
    if (hit.intersections.length > 0) {
      const p = hit.intersections[0].point
      mouseWorldPosition = new THREE.Vector3(p.x, p.y, p.z)
    }
  }

  return {
    trunkLine: trunkLineData,
    trunkLineId,
    object3D: trunkLineObject,
    userData,
    mouseWorldPosition, // 鼠标在干线上的3D位置
    ...(originalEvent && { originalEvent }),
  }
}

// 检测干线交互（点击或悬停）
const checkTrunkLineIntersection = event => {
  if (!props.trunkLineRenderer?.trunkLinesGroup || !props.picking) {
    return null
  }

  const { ndc } = props.picking.getNDCFromDOM(event)
  const result = props.picking.pick(props.trunkLineRenderer.trunkLinesGroup, {
    ndc,
    recursive: true,
    line2Threshold: 10,
  })

  for (const intersect of result.intersections) {
    let obj = intersect.object
    while (obj && obj.userData?.type !== 'trunk_line') {
      obj = obj.parent
    }
    if (obj && obj.userData?.type === 'trunk_line') {
      return obj
    }
  }

  return null
}

// 清除悬停状态
const clearHoverState = () => {
  if (hoveredTrunkLine.value) {
    emit('trunkLineHoverEnd', buildTrunkLineEvent(hoveredTrunkLine.value))
    hoveredTrunkLine.value = null
  }
}

// 鼠标按下处理
const handleMouseDown = event => {
  // 记录场景交互状态，防止在拖动时触发悬停
  if (event.buttons > 0) {
    isSceneInteracting.value = true
  }
}

// 优化的鼠标移动处理函数，添加移动阈值检测
const handleMouseMoveInternal = event => {
  if (event.buttons > 0) {
    isSceneInteracting.value = true
    clearHoverState()
    return
  }

  // 检查鼠标移动距离是否超过阈值
  const deltaX = Math.abs(event.clientX - lastMousePosition.value.x)
  const deltaY = Math.abs(event.clientY - lastMousePosition.value.y)
  const totalDelta = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

  // 如果移动距离小于阈值，不进行检测
  if (totalDelta < MOUSE_MOVE_THRESHOLD) {
    return
  }

  // 更新最后鼠标位置
  lastMousePosition.value = { x: event.clientX, y: event.clientY }

  // 检测鼠标悬停在干线上
  const trunkLine = checkTrunkLineIntersection(event)

  if (trunkLine) {
    // 如果干线变化了
    if (trunkLine !== hoveredTrunkLine.value) {
      clearHoverState()

      // 触发新干线悬停事件
      emit('trunkLineHover', buildTrunkLineEvent(trunkLine, event))
      hoveredTrunkLine.value = trunkLine
    }
  } else if (hoveredTrunkLine.value) {
    // 没有检测到干线，清除悬停状态
    clearHoverState()
  }
}

// 使用节流优化的鼠标移动处理函数
const handleMouseMove = throttle(handleMouseMoveInternal, 16) // 约60fps

// 鼠标抬起处理
const handleMouseUp = event => {
  if (isSceneInteracting.value) {
    isSceneInteracting.value = false
    return
  }

  // 只处理左键点击
  if (event.button === 0) {
    const trunkLine = checkTrunkLineIntersection(event)
    if (trunkLine) {
      emit('trunkLineLeftClick', buildTrunkLineEvent(trunkLine, event))
    }
  }
}

// 右键点击处理，只在点击到干线时阻止默认行为
const handleContextMenu = event => {
  const trunkLine = checkTrunkLineIntersection(event)
  if (trunkLine) {
    event.preventDefault()
    emit('trunkLineRightClick', buildTrunkLineEvent(trunkLine, event))
  }
}

// 鼠标离开场景容器时，发送悬停结束事件
const handleMouseLeave = () => {
  clearHoverState()
}

// 生命周期钩子
onMounted(() => {
  const container = props.sceneManager?.getContainer()
  if (container) {
    container.addEventListener('mousedown', handleMouseDown)
    container.addEventListener('mousemove', handleMouseMove)
    container.addEventListener('mouseup', handleMouseUp)
    container.addEventListener('contextmenu', handleContextMenu)
    container.addEventListener('mouseleave', handleMouseLeave)
  }
})

onBeforeUnmount(() => {
  const container = props.sceneManager?.getContainer()
  if (container) {
    container.removeEventListener('mousedown', handleMouseDown)
    container.removeEventListener('mousemove', handleMouseMove)
    container.removeEventListener('mouseup', handleMouseUp)
    container.removeEventListener('contextmenu', handleContextMenu)
    container.removeEventListener('mouseleave', handleMouseLeave)
  }

  // 清理状态
  clearHoverState()
})
</script>
