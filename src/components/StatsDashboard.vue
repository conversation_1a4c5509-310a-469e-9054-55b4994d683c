<template>
  <div class="stats-dashboard" :style="position">
    <div class="dashboard-card">
      <div class="dashboard-header">
        <span class="dashboard-title">{{ title }}</span>
      </div>
      <div class="dashboard-content">
        <div v-if="showValue" class="value-container">
          <span class="value">{{ value }}</span>
          <span v-if="unit" class="unit">{{ unit }}</span>
        </div>
        <slot name="content">
          <div class="chart-container" ref="chartContainer"></div>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, toRefs, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  title: {
    type: String,
    default: '统计数据',
  },
  value: {
    type: [String, Number],
    default: '',
  },
  unit: {
    type: String,
    default: '',
  },
  showValue: {
    type: Boolean,
    default: true,
  },
  chartType: {
    type: String,
    default: '',
    validator: value =>
      ['', 'pie', 'bar', 'hammer', 'horizontalBar'].includes(value),
  },
  chartData: {
    type: Array,
    default: () => [],
  },
  position: {
    type: Object,
    default: () => ({}),
  },
})

const { chartData, chartType } = toRefs(props)

const chartContainer = ref(null)
let chartInstance = null

// 定义适合深色背景的默认颜色方案
const defaultColors = [
  '#4fd1c5', // 青绿色
  '#2b6cb0', // 深蓝色
  '#38b2ac', // 深青绿色
  '#81e6d9', // 浅青绿色
  '#3182ce', // 蓝色
  '#0BC5EA', // 亮青色
  '#4299E1', // 天蓝色
  '#63b3ed', // 浅蓝色
]

// 根据标签生成默认颜色
const getDefaultColor = label => {
  // 使用标签字符串的哈希值来选择颜色
  const hashCode = label.split('').reduce((acc, char) => {
    return acc + char.charCodeAt(0)
  }, 0)
  return defaultColors[hashCode % defaultColors.length]
}

// 初始化图表
const initChart = () => {
  if (!chartContainer.value || !chartType.value) return

  // 如果已经有实例，先销毁
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 创建新的echarts实例，并设置设备像素比
  chartInstance = echarts.init(chartContainer.value, null, {
    renderer: 'canvas',
    devicePixelRatio: window.devicePixelRatio || 1,
  })
  // 根据图表类型设置不同的配置
  renderChart()

  // 添加窗口调整事件
  window.addEventListener('resize', handleResize)
}

// 渲染图表
const renderChart = () => {
  if (!chartInstance || !chartData.value || chartData.value.length === 0) return

  let option = {}

  switch (chartType.value) {
    case 'pie':
      option = getPieChartOption()
      break
    case 'bar':
      option = getBarChartOption()
      break
    case 'hammer':
      option = getHammerChartOption()
      break
    case 'horizontalBar':
      option = getHorizontalBarOption()
      break
    default:
      break
  }

  chartInstance.setOption(option)
}

// 饼图配置
const getPieChartOption = () => {
  const formattedData = chartData.value.map(item => ({
    name: item.label,
    value: item.value,
    itemStyle: {
      color: item.color || getDefaultColor(item.label), // 使用默认颜色方案
    },
  }))

  return {
    tooltip: {
      trigger: 'item',
      formatter: function (params) {
        // 支持换行符显示
        const name = params.name.replace(/\\n/g, '<br/>')
        return `${name}: ${params.value} (${params.percent}%)`
      },
      backgroundColor: 'rgba(10, 25, 40, 0.8)',
      borderColor: 'rgba(43, 108, 176, 0.5)',
      textStyle: {
        color: '#c5f6fa',
      },
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      itemWidth: Math.max(10, Math.min(16, window.innerWidth * 0.015)), // 响应式图例项宽度
      itemHeight: Math.max(10, Math.min(16, window.innerWidth * 0.015)), // 响应式图例项高度
      itemGap: Math.max(8, Math.min(15, window.innerWidth * 0.012)), // 响应式间距
      textStyle: {
        color: '#c5f6fa',
        fontSize: Math.max(7, Math.min(10, window.innerWidth * 0.008)), // 减小图例字体
        width: Math.max(50, Math.min(80, window.innerWidth * 0.06)), // 减小宽度
        overflow: 'break', // 关键：启用换行
      },
      // 移除formatter，让数据层处理的\n直接生效
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 6,
          borderColor: 'rgba(10, 25, 40, 0.7)',
          borderWidth: 1,
        },
        label: {
          show: true,
          position: 'inside',
          formatter: '{d}%',
          fontSize: Math.max(8, Math.min(11, window.innerWidth * 0.009)), // 减小饼图标签字体
          color: '#fff',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: Math.max(9, Math.min(12, window.innerWidth * 0.01)), // 减小强调状态字体
            fontWeight: 'bold',
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        labelLine: {
          show: false,
        },
        data: formattedData,
      },
    ],
  }
}

// 常规柱状图配置
const getBarChartOption = () => {
  const formattedData = chartData.value.map(item => ({
    name: item.label,
    value: item.value,
    itemStyle: {
      color: item.color || getDefaultColor(item.label),
      // 添加渐变效果
      borderRadius: [4, 4, 0, 0],
    },
  }))

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: '{b}: {c}',
      backgroundColor: 'rgba(10, 25, 40, 0.8)',
      borderColor: 'rgba(43, 108, 176, 0.5)',
      textStyle: {
        color: '#c5f6fa',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: formattedData.map(item => item.name),
      axisLine: {
        lineStyle: {
          color: 'rgba(43, 108, 176, 0.6)',
        },
      },
      axisLabel: {
        color: '#c5f6fa',
        interval: 0,
        rotate: 30,
        fontSize: Math.max(7, Math.min(10, window.innerWidth * 0.008)), // 减小坐标轴字体
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: 'rgba(43, 108, 176, 0.6)',
        },
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(43, 108, 176, 0.3)',
        },
      },
      axisLabel: {
        color: '#c5f6fa',
        fontSize: Math.max(7, Math.min(10, window.innerWidth * 0.008)), // 减小坐标轴字体
      },
    },
    series: [
      {
        type: 'bar',
        barWidth: '60%',
        data: formattedData,
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(20, 40, 70, 0.3)',
          borderRadius: [4, 4, 0, 0],
        },
      },
    ],
  }
}

// 锤形图配置（金字塔图）
const getHammerChartOption = () => {
  const formattedData = chartData.value.map(item => ({
    name: item.label,
    value: item.value,
    itemStyle: {
      color: item.color || getDefaultColor(item.label),
    },
  }))

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: '{b}: {c}%',
      backgroundColor: 'rgba(10, 25, 40, 0.8)',
      borderColor: 'rgba(43, 108, 176, 0.5)',
      textStyle: {
        color: '#c5f6fa',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: formattedData.map(item => item.name),
      axisLine: {
        lineStyle: {
          color: 'rgba(43, 108, 176, 0.6)',
        },
      },
      axisLabel: {
        color: '#c5f6fa',
        interval: 0,
        rotate: 30,
        fontSize: Math.max(7, Math.min(10, window.innerWidth * 0.008)), // 减小坐标轴字体
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: 'rgba(43, 108, 176, 0.6)',
        },
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(43, 108, 176, 0.3)',
        },
      },
      axisLabel: {
        color: '#c5f6fa',
        formatter: '{value}%',
        fontSize: Math.max(7, Math.min(10, window.innerWidth * 0.008)), // 减小坐标轴字体
      },
    },
    series: [
      {
        type: 'pictorialBar',
        symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
        itemStyle: {
          opacity: 0.85,
        },
        emphasis: {
          itemStyle: {
            opacity: 1,
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
        data: formattedData,
      },
    ],
  }
}

// 横向柱状图配置
const getHorizontalBarOption = () => {
  const formattedData = chartData.value.map(item => ({
    name: item.label,
    value: item.value,
    itemStyle: {
      color: item.color || getDefaultColor(item.label),
      borderRadius: [0, 4, 4, 0],
    },
  }))

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      backgroundColor: 'rgba(10, 25, 40, 0.8)',
      borderColor: 'rgba(43, 108, 176, 0.5)',
      textStyle: {
        color: '#c5f6fa',
      },
    },
    grid: {
      left: '0%', // 为Y轴标签留出适当空间
      right: '0%',
      bottom: '3%',
      top: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: 'rgba(43, 108, 176, 0.6)',
        },
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(43, 108, 176, 0.3)',
        },
      },
      axisLabel: {
        color: '#c5f6fa',
        fontSize: Math.max(7, Math.min(10, window.innerWidth * 0.008)), // 减小坐标轴字体
      },
    },
    yAxis: {
      type: 'category',
      data: formattedData.map(item => item.name),
      axisLine: {
        lineStyle: {
          color: 'rgba(43, 108, 176, 0.6)',
        },
      },
      axisLabel: {
        color: '#c5f6fa',
        fontSize: Math.max(7, Math.min(10, window.innerWidth * 0.008)), // 减小坐标轴字体
        align: 'right', // 标签右对齐，与柱状图起始位置对齐
        margin: 8, // 设置标签与轴线的距离
      },
      axisTick: {
        alignWithLabel: true, // 刻度线与标签对齐
      },
    },
    series: [
      {
        type: 'bar',
        barWidth: '60%',
        data: formattedData,
        showBackground: true,
        backgroundStyle: {
          color: 'rgba(20, 40, 70, 0.3)',
          borderRadius: [0, 4, 4, 0],
        },
      },
    ],
  }
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    // 重新渲染图表以应用新的响应式尺寸
    renderChart()
    chartInstance.resize()
  }
}

// 监听数据变化，重新渲染图表
watch(
  [chartData, chartType],
  () => {
    renderChart()
  },
  { deep: true }
)

// 组件挂载时初始化图表
onMounted(() => {
  // 延迟初始化，确保DOM已完全渲染
  setTimeout(() => {
    initChart()
  }, 100)
})

// 组件卸载时销毁图表
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})
</script>

<style scoped lang="scss">
.stats-dashboard {
  position: relative;
  z-index: 1000;
  width: 100%;
  height: 100%;
}

.dashboard-card {
  width: 100%; // 填满容器宽度
  height: 100%; // 填满容器高度
  background: rgba(10, 25, 40, 0.75);
  border: 1px solid rgba(43, 108, 176, 0.5);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  opacity: 0.95;
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
}

.dashboard-header {
  background: rgba(20, 40, 70, 0.4);
  padding: clamp(4px, 1vh, 8px) clamp(6px, 1.5vw, 12px); // 动态内边距
  border-bottom: 1px solid rgba(43, 108, 176, 0.5);
  flex-shrink: 0; // 防止标题被压缩
}

.dashboard-title {
  color: #c5f6fa;
  font-weight: 600;
  font-size: clamp(10px, 1vw, 14px); // 减小标题字体
}

.dashboard-content {
  padding: clamp(6px, 1.5vh, 12px); // 动态内边距：最小6px，最大12px
  color: #e2e8f0;
  flex: 1; // 占据剩余空间
  display: flex;
  flex-direction: column;
  min-height: 0; // 允许内容缩小到任意大小
}

.value-container {
  text-align: center;
  margin-bottom: clamp(4px, 1vh, 10px); // 动态边距
  flex-shrink: 0; // 防止数值显示被压缩
}

.value {
  font-size: clamp(16px, 2vw, 24px); // 减小数值字体
  font-weight: bold;
  color: #4fd1c5;
}

.unit {
  font-size: clamp(10px, 1.2vw, 14px); // 减小单位字体
  color: #81e6d9;
  margin-left: 5px;
}

.chart-container {
  flex: 1; // 占据剩余空间
  width: 100%;
  margin-top: clamp(4px, 1vh, 10px); // 动态上边距
  min-height: max(40px, 10vh); // 进一步减小最小高度：至少40px或10%视口高度
  overflow: hidden; // 确保内容不会溢出
}

// 自定义图例样式，支持文字换行
:deep(.echarts-legend-item) {
  white-space: normal !important;
  word-break: break-all !important;
  max-width: 80px !important;
  line-height: 1.2 !important;
}
</style>
