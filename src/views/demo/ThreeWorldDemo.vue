<template>
  <div class="demo-page">
    <div class="toolbar">
      <a-space>
        <a-button @click="world.ui.resetView()">重置视角</a-button>
        <a-button @click="world.ui.toggle2D3D()">2D/3D 切换</a-button>

        <a-switch v-model:checked="showPoints" checked-children="点On" un-checked-children="点Off" />
        <a-switch v-model:checked="showLines" checked-children="线On" un-checked-children="线Off" />
        <a-switch v-model:checked="showPolygons" checked-children="面On" un-checked-children="面Off" />

        <a-button @click="registerLayers">注册图层</a-button>
        <a-button @click="unregisterLayers">卸载图层</a-button>
        <a-button @click="clearLayers">清理图层</a-button>
      </a-space>
    </div>

    <div class="stage" ref="world.containerRef"></div>

    <Compass :cameraRotation="heading" />
  </div>
</template>

<script setup>
// 我创建一个完整的验证页面，集成核心引擎 + 点/线/面图层 + UI 控件
import { ref, onMounted, watch } from 'vue'
import { useThreeWorld } from '@/three/core/useThreeWorld'
import { usePointLayer } from '@/three/layers/usePointLayer'
import { useLine2Layer } from '@/three/layers/useLine2Layer'
import { usePolygonLayer } from '@/three/layers/usePolygonLayer'
import Compass from '@/components/three/Compass.vue'
import * as THREE from 'three'

// 初始化引擎
const world = useThreeWorld({ backgroundColor: 0x050a14 })

// 创建各图层
const pointLayer = usePointLayer(world, { id: 'demo-points' })
const lineLayer = useLine2Layer(world, { id: 'demo-lines' })
const polygonLayer = usePolygonLayer(world, { id: 'demo-polygons' })

// 图层显示开关
const showPoints = ref(true)
const showLines = ref(true)
const showPolygons = ref(true)

watch(showPoints, v => (pointLayer.group.visible = v))
watch(showLines, v => (lineLayer.group.visible = v))
watch(showPolygons, v => (polygonLayer.group.visible = v))

// 指南针：计算相机朝向（弧度）
const heading = ref(0)
function updateHeading() {
  const dir = world.camera.value.getWorldDirection(new THREE.Vector3())
  heading.value = Math.atan2(dir.x, -dir.z)
  requestAnimationFrame(updateHeading)
}

// 构建测试数据（GeoJSON）
// 点：两个设备，一个使用SVG dataURL，一个使用PNG dataURL
const svgIconDataUrl =
  'data:image/svg+xml;utf8,' +
  encodeURIComponent(
    `<svg xmlns="http://www.w3.org/2000/svg" width="128" height="128" viewBox="0 0 24 24"><path fill="#22D3EE" d="M12 2C8.14 2 5 5.14 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.86-3.14-7-7-7m0 9.5A2.5 2.5 0 1 1 12 6a2.5 2.5 0 0 1 0 5.5"/></svg>`
  )

// 一个1x1的蓝色PNG（dataURL）
const pngIconDataUrl =
  'data:image/png;base64,' +
  'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQIW2P4z8DwHwAF/wJ0V8oF6wAAAABJRU5ErkJggg=='

const pointFC = {
  type: 'FeatureCollection',
  features: [
    {
      type: 'Feature',
      properties: { id: 'D001', name: '设备A', iconDataUrl: svgIconDataUrl },
      geometry: { type: 'Point', coordinates: [117.0005, 36.0005] },
    },
    {
      type: 'Feature',
      properties: { id: 'D002', name: '设备B', iconDataUrl: pngIconDataUrl },
      geometry: { type: 'Point', coordinates: [117.0015, 36.0015] },
    },
  ],
}

// 线：一条 LineString + 一条 MultiLineString
const lineFC = {
  type: 'FeatureCollection',
  features: [
    {
      type: 'Feature',
      properties: { id: 'R001', name: '道路一' },
      geometry: {
        type: 'LineString',
        coordinates: [
          [117.0, 36.0],
          [117.002, 36.001],
          [117.004, 36.002],
        ],
      },
    },
    {
      type: 'Feature',
      properties: { id: 'R002', name: '道路二' },
      geometry: {
        type: 'MultiLineString',
        coordinates: [
          [
            [117.006, 36.0],
            [117.008, 36.001],
          ],
          [
            [117.008, 36.001],
            [117.01, 36.0025],
          ],
        ],
      },
    },
  ],
}

// 面：一个 Polygon + 一个 MultiPolygon
const polygonFC = {
  type: 'FeatureCollection',
  features: [
    {
      type: 'Feature',
      properties: { id: 'A001', name: '园区A' },
      geometry: {
        type: 'Polygon',
        coordinates: [
          [
            [116.999, 35.999],
            [117.003, 35.999],
            [117.003, 36.001],
            [116.999, 36.001],
            [116.999, 35.999],
          ],
        ],
      },
    },
    {
      type: 'Feature',
      properties: { id: 'A002', name: '园区B(多块)' },
      geometry: {
        type: 'MultiPolygon',
        coordinates: [
          [
            [
              [117.004, 36.002],
              [117.006, 36.002],
              [117.006, 36.0005],
              [117.004, 36.0005],
              [117.004, 36.002],
            ],
          ],
          [
            [
              [117.0065, 36.003],
              [117.0075, 36.003],
              [117.0075, 36.0022],
              [117.0065, 36.0022],
              [117.0065, 36.003],
            ],
          ],
        ],
      },
    },
  ],
}

function registerLayers() {
  try {
    world.registerLayer(pointLayer)
    world.registerLayer(lineLayer)
    world.registerLayer(polygonLayer)
  } catch (e) {
    // 已注册会报错，此处忽略
  }
}

function unregisterLayers() {
  world.unregisterLayer(pointLayer.id)
  world.unregisterLayer(lineLayer.id)
  world.unregisterLayer(polygonLayer.id)
}

function clearLayers() {
  pointLayer.clear()
  lineLayer.clear()
  polygonLayer.clear()
}

onMounted(async () => {
  registerLayers()
  await pointLayer.renderPoints({ data: pointFC })
  lineLayer.renderLine({ data: lineFC, options: { lineColor: '#22D3EE', lineWidth: 3 } })
  polygonLayer.renderPolygon({ data: polygonFC, options: { fillColor: '#0ea5e9', outlineColor: '#e2e8f0' } })
  requestAnimationFrame(updateHeading)
})
</script>

<style scoped>
.demo-page {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: #0b1220;
}
.toolbar {
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 10;
  background: rgba(0, 0, 0, 0.3);
  padding: 8px 12px;
  border-radius: 8px;
}
.stage {
  position: absolute;
  inset: 0;
}
</style>

