<template>
  <div class="demo-page">
    <div class="toolbar">
      <a-space>
        <a-button @click="world.ui.resetView()">重置视角</a-button>
        <a-button @click="onToggle2D3D">2D/3D 切换</a-button>

        <a-switch
          v-model:checked="showPoints"
          checked-children="点On"
          un-checked-children="点Off"
        />
        <a-switch
          v-model:checked="showLines"
          checked-children="线On"
          un-checked-children="线Off"
        />
        <a-switch
          v-model:checked="showPolygons"
          checked-children="面On"
          un-checked-children="面Off"
        />

        <a-button @click="registerLayers">注册图层</a-button>
        <a-button @click="unregisterLayers">卸载图层</a-button>
        <a-button @click="clearLayers">清理图层</a-button>
      </a-space>
    </div>

    <div class="stage" ref="world.containerRef"></div>

    <Compass :cameraRotation="heading" />

    <!-- 右键菜单 -->
    <div
      v-if="contextMenu.visible"
      class="context-menu"
      :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
    >
      <div class="context-item">{{ contextMenu.title || '对象' }}</div>
      <div class="context-sub">{{ contextMenu.subtitle || '' }}</div>
      <a-button size="small" @click="contextMenu.visible = false"
        >关闭</a-button
      >
    </div>

    <!-- 调试信息 -->
    <div class="debug-panel">
      <div>Scene: {{ !!world.scene }}</div>
      <div>Camera: {{ cameraType }}</div>
      <div>Renderer: {{ rendererSize }}</div>
      <div>
        Objects: 点 {{ pointLayer.group.children.length }} | 线
        {{ lineLayer.group.children.length }} | 面
        {{ polygonLayer.group.children.length }}
      </div>
    </div>
  </div>
</template>

<script setup>
// 我创建一个完整的验证页面，集成核心引擎 + 点/线/面图层 + UI 控件
import { ref, onMounted, watch, onBeforeUnmount, computed } from 'vue'
import { useThreeWorld } from '@/three/core/useThreeWorld'
import { usePointLayer } from '@/three/layers/usePointLayer'
import { useLine2Layer } from '@/three/layers/useLine2Layer'
import { usePolygonLayer } from '@/three/layers/usePolygonLayer'
import Compass from '@/components/three/Compass.vue'
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'

// 初始化引擎
const world = useThreeWorld({ backgroundColor: 0x050a14 })
const contextMenu = ref({ visible: false, x: 0, y: 0, title: '', subtitle: '' })
const controlsRef = ref(null)

// 创建各图层
const pointLayer = usePointLayer(world, { id: 'demo-points' })
const lineLayer = useLine2Layer(world, { id: 'demo-lines' })
const polygonLayer = usePolygonLayer(world, { id: 'demo-polygons' })

// 图层显示开关
const showPoints = ref(true)
const showLines = ref(true)
const showPolygons = ref(true)

watch(showPoints, v => (pointLayer.group.visible = v))
watch(showLines, v => (lineLayer.group.visible = v))
watch(showPolygons, v => (polygonLayer.group.visible = v))

// 指南针：计算相机朝向（弧度）
const heading = ref(0)
function updateHeading() {
  const dir = world.camera.value.getWorldDirection(new THREE.Vector3())
  heading.value = Math.atan2(dir.x, -dir.z)
  requestAnimationFrame(updateHeading)
}

// 调试信息
const cameraType = computed(() =>
  world.is2DMode.value ? 'Orthographic(2D)' : 'Perspective(3D)'
)
const rendererSize = computed(() => {
  const v = new THREE.Vector2()
  world.renderer.getSize(v)
  return `${v.x}x${v.y}`
})

// 构建测试数据（GeoJSON）
// 点：多个设备，混合 SVG/PNG dataURL
const svgIconDataUrl =
  'data:image/svg+xml;utf8,' +
  encodeURIComponent(
    `<svg xmlns="http://www.w3.org/2000/svg" width="128" height="128" viewBox="0 0 24 24"><path fill="#22D3EE" d="M12 2C8.14 2 5 5.14 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.86-3.14-7-7-7m0 9.5A2.5 2.5 0 1 1 12 6a2.5 2.5 0 0 1 0 5.5"/></svg>`
  )
const pngIconDataUrl =
  'data:image/png;base64,' +
  'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVQIW2P4z8DwHwAF/wJ0V8oF6wAAAABJRU5ErkJggg=='

const pointFC = {
  type: 'FeatureCollection',
  features: [
    {
      type: 'Feature',
      properties: { id: 'D001', name: '设备A', iconDataUrl: svgIconDataUrl },
      geometry: { type: 'Point', coordinates: [117.0005, 36.0005] },
    },
    {
      type: 'Feature',
      properties: { id: 'D002', name: '设备B', iconDataUrl: pngIconDataUrl },
      geometry: { type: 'Point', coordinates: [117.0015, 36.0015] },
    },
    {
      type: 'Feature',
      properties: { id: 'D003', name: '设备C', iconDataUrl: svgIconDataUrl },
      geometry: { type: 'Point', coordinates: [117.002, 36.002] },
    },
    {
      type: 'Feature',
      properties: { id: 'D004', name: '设备D', iconDataUrl: pngIconDataUrl },
      geometry: { type: 'Point', coordinates: [117.003, 36.0025] },
    },
  ],
}

// 线：一条 LineString + 一条 MultiLineString
const lineFC = {
  type: 'FeatureCollection',
  features: [
    {
      type: 'Feature',
      properties: { id: 'R001', name: '道路一' },
      geometry: {
        type: 'LineString',
        coordinates: [
          [117.0, 36.0],
          [117.002, 36.001],
          [117.004, 36.002],
        ],
      },
    },
    {
      type: 'Feature',
      properties: { id: 'R002', name: '道路二' },
      geometry: {
        type: 'MultiLineString',
        coordinates: [
          [
            [117.006, 36.0],
            [117.008, 36.001],
          ],
          [
            [117.008, 36.001],
            [117.01, 36.0025],
          ],
        ],
      },
    },
  ],
}

// 面：一个 Polygon + 一个 MultiPolygon
const polygonFC = {
  type: 'FeatureCollection',
  features: [
    {
      type: 'Feature',
      properties: { id: 'A001', name: '园区A' },
      geometry: {
        type: 'Polygon',
        coordinates: [
          [
            [116.999, 35.999],
            [117.003, 35.999],
            [117.003, 36.001],
            [116.999, 36.001],
            [116.999, 35.999],
          ],
        ],
      },
    },
    {
      type: 'Feature',
      properties: { id: 'A002', name: '园区B(多块)' },
      geometry: {
        type: 'MultiPolygon',
        coordinates: [
          [
            [
              [117.004, 36.002],
              [117.006, 36.002],
              [117.006, 36.0005],
              [117.004, 36.0005],
              [117.004, 36.002],
            ],
          ],
          [
            [
              [117.0065, 36.003],
              [117.0075, 36.003],
              [117.0075, 36.0022],
              [117.0065, 36.0022],
              [117.0065, 36.003],
            ],
          ],
        ],
      },
    },
  ],
}

function registerLayers() {
  try {
    world.registerLayer(pointLayer)
    world.registerLayer(lineLayer)
    world.registerLayer(polygonLayer)
  } catch (e) {
    // 已注册会报错，此处忽略
  }
}

function unregisterLayers() {
  world.unregisterLayer(pointLayer.id)
  world.unregisterLayer(lineLayer.id)
}

onMounted(async () => {
  registerLayers()
  await pointLayer.renderPoints({ data: pointFC })
  lineLayer.renderLine({
    data: lineFC,
    options: { lineColor: '#22D3EE', lineWidth: 3 },
  })
  polygonLayer.renderPolygon({
    data: polygonFC,
    options: { fillColor: '#0ea5e9', outlineColor: '#e2e8f0' },
  })
  bindMouseEvents()
  requestAnimationFrame(updateHeading)
})

onBeforeUnmount(() => {
  // 解绑鼠标事件，避免内存泄漏
  const el = world.renderer?.domElement
  if (el) {
    el.removeEventListener('contextmenu', onContextMenu)
    el.removeEventListener('pointermove', onPointerMove)
    el.removeEventListener('pointerdown', onPointerDown)
  }
})
world.unregisterLayer(polygonLayer.id)
// 绑定鼠标交互
function bindMouseEvents() {
  const el = world.renderer?.domElement
  if (!el) return
  el.addEventListener('contextmenu', onContextMenu)
  el.addEventListener('pointermove', onPointerMove)
  el.addEventListener('pointerdown', onPointerDown)
}
function unbindMouseEvents() {
  const el = world.renderer?.domElement
  if (!el) return
  el.removeEventListener('contextmenu', onContextMenu)
  el.removeEventListener('pointermove', onPointerMove)
  el.removeEventListener('pointerdown', onPointerDown)
}

function onPointerMove(e) {
  const { clientX, clientY } = e
  const rect = world.renderer.domElement.getBoundingClientRect()
  const ndc = new THREE.Vector2(
    ((clientX - rect.left) / rect.width) * 2 - 1,
    -(((clientY - rect.top) / rect.height) * 2 - 1)
  )
  const raycaster = new THREE.Raycaster()
  raycaster.setFromCamera(ndc, world.camera.value)
  const intersects = raycaster.intersectObjects(pointLayer.group.children, true)
  document.body.style.cursor = intersects.length > 0 ? 'pointer' : 'default'
}

function onPointerDown(e) {
  if (e.button === 2) return
  const rect = world.renderer.domElement.getBoundingClientRect()
  const ndc = new THREE.Vector2(
    ((e.clientX - rect.left) / rect.width) * 2 - 1,
    -(((e.clientY - rect.top) / rect.height) * 2 - 1)
  )
  const raycaster = new THREE.Raycaster()
  raycaster.setFromCamera(ndc, world.camera.value)
  const intersects = raycaster.intersectObjects([
    ...pointLayer.group.children,
    ...polygonLayer.group.children,
    ...lineLayer.group.children,
  ])
  if (intersects.length > 0) {
    const obj = intersects[0].object
    if (obj.material && 'emissive' in obj.material) {
      obj.material.emissive = new THREE.Color('#ffaa00')
    }
  }
}

function onContextMenu(e) {
  e.preventDefault()
  const rect = world.renderer.domElement.getBoundingClientRect()
  contextMenu.value.x = e.clientX - rect.left
  contextMenu.value.y = e.clientY - rect.top
  contextMenu.value.title = '右键菜单'
  contextMenu.value.subtitle = '在此可扩展更多操作'
  contextMenu.value.visible = true
}

function onToggle2D3D() {
  world.ui.toggle2D3D()
  if (typeof lineLayer.onAfterRender === 'function') lineLayer.onAfterRender()
}

bindMouseEvents()

function clearLayers() {
  pointLayer.clear()
  lineLayer.clear()
  polygonLayer.clear()
}

onMounted(async () => {
  registerLayers()
  await pointLayer.renderPoints({ data: pointFC })
  lineLayer.renderLine({
    data: lineFC,
    options: { lineColor: '#22D3EE', lineWidth: 3 },
  })
  polygonLayer.renderPolygon({
    data: polygonFC,
    options: { fillColor: '#0ea5e9', outlineColor: '#e2e8f0' },
  })
  requestAnimationFrame(updateHeading)
})
</script>

<style scoped>
.demo-page {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: #0b1220;
}
.toolbar {
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 10;
  background: rgba(0, 0, 0, 0.3);
  padding: 8px 12px;
  border-radius: 8px;
}
.stage {
  position: absolute;
  inset: 0;
}
</style>
