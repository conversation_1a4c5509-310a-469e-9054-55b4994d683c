<template>
  <div class="city-model-container">
    <MainHeader
      ref="mainHeader"
      :hide-nav-buttons="false"
      @nav-click="handleNavigation"
      @map-config-click="handleMapConfigPanel"
    />
    <!-- 使用 ThreeCanvas 组件替换原来的容器 -->
    <ThreeCanvas
      :background-color="0x050a14"
      :enable-lights="true"
      :min-distance="minCameraDistance"
      :max-distance="maxCameraDistance"
      :ground-y="COORDINATE_CONFIG.Y_LEVELS.ROADS || 0"
      :camera-change-callback="handleCameraChange"
      :groups-factory="createSceneGroups"
      @ready="onThreeReady"
      @error="onThreeError"
    />

    <!-- 地图配置模态框 -->
    <MapConfigModal
      v-model:visible="isMapConfigModalVisible"
      :initial-panel="activeMapPanelInModal"
      :is-drawing-mode="isDrawingMode"
      :is-drawing-area-mode="isDrawingAreaMode"
      :current-drawing-points-count="currentDrawingPoints.length"
      :current-drawing-area-points-count="currentDrawingAreaPoints.length"
      :selected-road-level="selectedRoadLevel"
      :current-road-name="currentRoadName"
      :road-level-options="roadLevelOptions"
      :selected-area-name="null"
      :selected-area-name-for-toolbar="selectedAreaNameForDrawing"
      :selected-area-classification="selectedAreaClassification"
      :available-city-names="availableCityNames"
      :highlighted-city-name="highlightedCityName"
      @city-data-loaded="handleCityDataLoaded"
      @road-data-loaded="handleRoadDataLoaded"
      @highlight-city-name-updated="updateHighlightCityName"
      @add-geojson-feature="handleAddGeoJsonFeature"
      @update:selected-road-level="selectedRoadLevel = $event"
      @update:current-road-name="currentRoadName = $event"
      @update:selected-area-name-for-toolbar="
        selectedAreaNameForDrawing = $event
      "
      @update:selected-area-classification="selectedAreaClassification = $event"
      :road-display-mode="roadDisplayMode"
      :hide-threshold="roadDistanceThresholds.hide"
      :primary-threshold="roadDistanceThresholds.primary"
      :secondary-threshold="roadDistanceThresholds.secondary"
      @toggle-drawing-mode="toggleDrawingMode"
      @finalize-current-road="finalizeCurrentRoad"
      @cancel-current-drawing="cancelCurrentDrawing"
      @toggle-draw-area-mode="toggleDrawAreaMode"
      @finalize-current-area="finalizeCurrentArea"
      @cancel-current-area-drawing="cancelCurrentAreaDrawing"
      @update:road-display-mode="setRoadDisplayMode"
      @update:hide-threshold="roadDistanceThresholds.hide = $event"
      @update:primary-threshold="roadDistanceThresholds.primary = $event"
      @update:secondary-threshold="roadDistanceThresholds.secondary = $event"
      @apply-distance-thresholds="applyDistanceThresholds"
      @reset-distance-thresholds="resetDistanceThresholds"
      @start-trunk-line-creation="handleStartTrunkLineCreation"
      @cancel-trunk-line-creation="handleCancelTrunkLineCreation"
      @start-trunk-line-editing="handleStartTrunkLineEditing"
      @delete-trunk-line="handleDeleteTrunkLine"
    />

    <!-- 设备交互处理组件 - 在聚合模式下禁用 -->
    <DeviceInteractionHandler
      v-if="
        isThreeInitialized &&
        sceneManager &&
        deviceMarkersGroup &&
        !deviceClusterSystem.isClusterMode.value
      "
      :scene-manager="sceneManager"
      :device-markers-group="deviceMarkersGroup"
      :picking="pickingApi"
      @device-left-click="handleDeviceLeftClick"
      @device-right-click="handleDeviceRightClick"
      @device-hover="handleDeviceHover"
      @device-hover-end="handleDeviceHoverEnd"
    />

    <!-- 干线交互处理组件 -->
    <TrunkLineInteractionHandler
      v-if="isThreeInitialized && sceneManager && trunkLineRenderer"
      :scene-manager="sceneManager"
      :trunk-line-renderer="trunkLineRenderer"
      :picking="pickingApi"
      @trunk-line-hover="handleTrunkLineHover"
      @trunk-line-hover-end="handleTrunkLineHoverEnd"
      @trunk-line-left-click="handleTrunkLineLeftClick"
      @trunk-line-right-click="handleTrunkLineRightClick"
    />

    <!-- 区域交互处理组件 -->
    <AreaInteractionHandler
      v-if="isThreeInitialized && sceneManager && userPolygonsGroup"
      :scene-manager="sceneManager"
      :user-polygons-group="userPolygonsGroup"
      :picking="pickingApi"
      @area-hover="handleAreaHover"
      @area-hover-end="handleAreaHoverEnd"
      @area-left-click="handleAreaLeftClick"
      @area-right-click="handleAreaRightClick"
    />

    <!-- 道路交互处理组件 -->
    <RoadInteractionHandler
      v-if="isThreeInitialized && sceneManager && roadNetworkGroup"
      :scene-manager="sceneManager"
      :road-network-group="roadNetworkGroup"
      :dynamic-road-loader="dynamicRoadLoader"
      :picking="pickingApi"
      @road-delete="handleRoadDelete"
    />

    <!-- 设备悬停信息提示框 -->
    <DeviceTooltip
      v-if="shouldShowDeviceTooltip"
      tooltip-type="device"
      :tooltip-data="hoveredDeviceInfo"
      :object3d="hoveredDevice3D"
      :scene-manager="sceneManager"
      :traffic-metrics="currentDeviceTrafficMetrics"
      :show-traffic-metrics="!!currentDeviceTrafficMetrics"
      @close="handleCloseDeviceTooltip"
      @mouse-enter="handleTooltipMouseEnter"
      @mouse-leave="handleTooltipMouseLeave"
      @intersection-monitor="handleIntersectionMonitor"
      @device-monitor="handleDeviceMonitor"
      @running-monitor="handleRunningMonitor"
    />

    <!-- 干线悬停信息提示框 -->
    <DeviceTooltip
      v-if="shouldShowTrunkLineTooltip"
      tooltip-type="trunkLine"
      :tooltip-data="hoveredTrunkLineInfo"
      :object3d="hoveredTrunkLine3D"
      :mouse-world-position="hoveredTrunkLineMousePosition"
      :scene-manager="sceneManager"
      :traffic-metrics="currentCorridorTrafficMetrics"
      :show-traffic-metrics="!!currentCorridorTrafficMetrics"
      @close="handleCloseTrunkLineTooltip"
    />

    <!-- 区域悬停信息提示框 -->
    <DeviceTooltip
      v-if="shouldShowAreaTooltip"
      tooltip-type="area"
      :tooltip-data="hoveredAreaInfo"
      :object3d="hoveredArea3D"
      :mouse-world-position="hoveredAreaMousePosition"
      :scene-manager="sceneManager"
      :traffic-metrics="currentAreaTrafficMetrics"
      :show-traffic-metrics="!!currentAreaTrafficMetrics"
      @close="handleCloseAreaTooltip"
    />

    <!-- 设备监控中心模态框 -->
    <DeviceMonitorModal
      :visible="isDeviceConfigVisible"
      @close="handleCloseDeviceMonitor"
      @focus-on-device="handleFocusOnDevice"
    />

    <!-- 浮动干线创建面板 -->
    <FloatingTrunkLineCreator
      :visible="isTrunkLineCreationMode || isTrunkLineEditingMode"
      :selected-road-ids="selectedRoadIdsForTrunkLine"
      :road-network-data="jinanRoadNetworkGeoJson"
      :dynamic-road-loader="dynamicRoadLoader"
      :edit-mode="isTrunkLineEditingMode"
      :editing-trunk-line="editingTrunkLineData"
      @close="handleCancelTrunkLineOperation"
      @remove-road-selection="handleRemoveRoadSelection"
    />

    <!-- 设备配置模态框 -->
    <DeviceSettingsModal
      :visible="isSettingsModalVisible"
      :device="selectedSettingsDevice"
      @close="isSettingsModalVisible = false"
      @update="handleSettingsUpdate"
    />

    <!-- 监控模态框 -->
    <MonitorModal
      :visible="isMonitorModalVisible"
      :device="selectedMonitorDevice"
      :monitor-type="monitorType"
      @close="isMonitorModalVisible = false"
    />

    <!-- 看板管理组件 -->
    <DashboardManager
      :devices="apiDevices"
      :edges-status="edgesStatus"
      :detectors-status="detectorsStatus"
      :control-modes-data="controlModesData"
      @device-click="handleFocusOnDevice"
    />
  </div>
</template>

<script setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  computed,
  watchEffect,
  nextTick,
  watch,
} from 'vue'
import * as THREE from 'three'
import {
  transformGeoJsonCoordinates,
  transformCoordinate,
  COORDINATE_CONFIG,
} from '@/utils/coordinateTransform'
import { Line2 } from 'three/examples/jsm/lines/Line2.js'
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial.js'
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry.js'
import MainHeader from '@/components/three/MainHeader.vue'
import MapConfigModal from '@/components/MapConfigModal.vue'
import DeviceSettingsModal from '@/components/DeviceSettingsModal.vue' // 引入设备配置模态框
import MonitorModal from '@/components/MonitorModal.vue' // 引入运行监视模态框
import DeviceInteractionHandler from '@/components/DeviceInteractionHandler.vue' // 引入设备交互处理组件
import TrunkLineInteractionHandler from '@/components/TrunkLineInteractionHandler.vue' // 引入干线交互处理组件
import AreaInteractionHandler from '@/components/AreaInteractionHandler.vue' // 引入区域交互处理组件
import RoadInteractionHandler from '@/components/RoadInteractionHandler.vue' // 引入道路交互处理组件
import DeviceTooltip from '@/components/DeviceTooltip.vue' // 引入设备提示框组件
import DashboardManager from '@/components/DashboardManager.vue' // 引入看板管理组件
import DeviceMonitorModal from '@/components/DeviceMonitorModal.vue' // 引入设备监控中心模态框
import FloatingTrunkLineCreator from '@/components/FloatingTrunkLineCreator.vue' // 引入浮动干线创建面板
import ThreeCanvas from '@/components/ThreeCanvas.vue' // 引入 ThreeCanvas 组件
import ThreeSceneManager from '@/utils/ThreeSceneManager'
import GeoJsonRenderer from '@/utils/GeoJsonRenderer'
import DynamicRoadLoader from '@/utils/DynamicRoadLoader'
import { TrunkLineRenderer } from '@/utils/TrunkLineRenderer'
import SmartRoadLabelManager from '@/utils/SmartRoadLabelManager'
import UserRoadLabelManager from '@/utils/UserRoadLabelManager'
import PreviewLineManager from '@/utils/PreviewLineManager'
import roadDeleteService from '@/utils/RoadDeleteService'

import { throttle } from 'lodash' // 添加lodash的throttle函数
import { dashboardApi } from '@/api' // 引入看板API
import { useDeviceInteraction } from '@/composables/useDeviceInteraction'
import { useDataManager } from '@/composables/useDataManager' // 引入设备交互 Composable
import { useTrafficMetrics } from '@/composables/useTrafficMetrics' // 引入交通指标 Composable
import { useDeviceCluster } from '@/composables/useDeviceCluster' // 引入设备聚合 Composable
import { useTooltipPriority } from '@/composables/useTooltipPriority' // 引入提示框优先级管理

import { message } from 'ant-design-vue'
import { storeToRefs } from 'pinia'
import { transformSceneToGeo } from '@/utils/coordinateTransform'
import { getServiceLevelThreeColor } from '@/utils/trafficColors'
import eventBus from '@/utils/eventBus'
import axios from 'axios'
import { getBaseUrl } from '@/config/server.config'

// 业务侧分组工厂：按需创建与业务绑定的分组
const createSceneGroups = scene => {
  const groups = {
    cityOutlineGroup: new THREE.Group(),
    roadNetworkGroup: new THREE.Group(),
    labelsGroup: new THREE.Group(),
    roadLabelsGroup: new THREE.Group(),
    deviceMarkersGroup: new THREE.Group(),
    deviceClusterGroup: new THREE.Group(),
    userDrawingInProgressGroup: new THREE.Group(),
    finalizedUserRoadsGroup: new THREE.Group(),
    userDrawnRoadLabelsGroup: new THREE.Group(),
    userPointsGroup: new THREE.Group(),
    userPolygonsGroup: new THREE.Group(),
  }
  Object.values(groups).forEach(g => scene.add(g))
  return groups
}

// 移除 threeContainer，改用 ThreeCanvas 组件
let sceneManager = null
let geoJsonRenderer = null
let dynamicRoadLoader = null // 动态道路加载器实例
let trunkLineRenderer = null // 干线渲染器实例
let smartRoadLabelManager = null // 智能路名标签管理器实例
let userRoadLabelManager = null // 用户道路标签管理器实例
let previewLineManager = null // 预览线管理器实例

let cityOutlineGroup,
  roadNetworkGroup,
  deviceMarkersGroup,
  deviceClusterGroup, // 设备聚合组
  userDrawingInProgressGroup,
  finalizedUserRoadsGroup
let labelsGroup, roadLabelsGroup, userDrawnRoadLabelsGroup
let userPointsGroup // 用户点位组
let userPolygonsGroup // 用户区域组

let materialsCache = {}

const isThreeInitialized = ref(false)

// LOD 和相机距离相关的 ref
const displayedCameraDistance = ref(0)

// 初始化设备聚合系统
const deviceClusterSystem = useDeviceCluster({
  distanceThreshold: 4, // 相机距离阈值，> 4m时启用聚合
  pixelThreshold: 60, // 屏幕像素距离阈值，≤60px时聚合
  baseScale: 0.15, // 聚合图标缩放
  yLevel: COORDINATE_CONFIG.Y_LEVELS.DEVICES, // Y轴层级
})

const POI_BASE_CLICK_RADIUS = 0.008 // 增大基础点击半径，从0.003增加到0.008
const POI_CLICK_RADIUS_SCALE_FACTOR = 0.0003 // 增大缩放因子，从0.0001增加到0.0003

// 道路显示模式 - 提前定义避免初始化错误
const roadDisplayMode = ref('auto')

// 统一的默认阈值配置
const DEFAULT_THRESHOLDS = { hide: 10, primary: 5, secondary: 2 }

// 距离阈值 - 提前定义
const roadDistanceThresholds = ref({ ...DEFAULT_THRESHOLDS })

// 处理键盘事件 - 提前定义避免初始化错误
const handleKeyDown = event => {
  // ESC键只用于取消绘制模式
  if (event.key === 'Escape') {
    if (isDrawingMode.value || isDrawingAreaMode.value) {
      // 如果在绘制模式，ESC取消绘制
      cancelCurrentDrawing()
      cancelCurrentAreaDrawing()
      toggleDrawAreaMode() // 退出模式

      // 按ESC键取消当前绘制操作
      return
    }
  }
}

// 处理键盘释放事件
const handleKeyUp = () => {
  // 预留用于其他键盘事件处理
}

// 用户设置加载函数 - 提前定义避免初始化错误
const loadUserSettings = async () => {
  try {
    // 1. 加载最大相机距离
    const savedMaxDistance = await getFromIndexedDB(
      'settings',
      'maxCameraDistance'
    )
    if (savedMaxDistance !== null && savedMaxDistance !== undefined) {
      maxCameraDistance.value = savedMaxDistance
    }

    // 2. 加载路网显示模式
    const savedMode = await getSavedRoadDisplayMode()
    if (savedMode) {
      roadDisplayMode.value = savedMode
    }

    // 3. 加载距离阈值
    const savedThresholds = await getSavedDistanceThresholds()
    if (savedThresholds) {
      roadDistanceThresholds.value = savedThresholds
    }
  } catch (error) {
    // 加载用户设置失败，使用默认值
  }
}

// 修复：设备和区域可见性应该独立控制，不与主干道绑定
const isDeviceVisible = computed(() => true) // 设备始终可见
const isDeviceLabelVisible = computed(() => true) // 设备标签始终可见
const isAreaVisible = computed(() => true) // 区域始终可见
const isAreaLabelVisible = computed(() => true) // 区域标签始终可见

const minCameraDistance = ref(0.1) // 最小相机距离
const maxCameraDistance = ref(25) // 最大相机距离限制为25
const highlightedCityName = ref('济南市') // 用于存储高亮城市名称

// 计算属性：从城市GeoJSON数据中提取城市名称列表
const availableCityNames = computed(() => {
  const features = jinanCityGeoJson.value?.features || []
  const cityNamesSet = new Set()

  features.forEach(feature => {
    const name = feature.properties?.name
    if (name) {
      cityNamesSet.add(name.trim())
    }
  })

  return Array.from(cityNamesSet).sort()
})

// 绘制道路相关状态
const isDrawingMode = ref(false)
// 注意：设备添加现在通过API接口完成
const currentDrawingPoints = ref([]) // 存储当前正在绘制的道路的点 (场景坐标)
const currentRoadName = ref('') // 用于存储当前绘制的道路名称
const SELECTION_HIGHLIGHT_COLOR = new THREE.Color(0x38b2ac) // 深青绿色选中

// 点位/设备选择相关
const selectedPointObjectRef = ref(null)

// 区域绘制时的参数
const selectedAreaNameForDrawing = ref('')
const selectedAreaClassification = ref('A')

// 地图配置模态框可见性状态
const isMapConfigModalVisible = ref(false)

// 使用设备交互 Composable
const {
  // 状态
  hoveredDeviceInfo,
  hoveredDevice3D,
  selectedDeviceWorldPosition,
  isSettingsModalVisible,
  selectedSettingsDevice,
  isMonitorModalVisible,
  selectedMonitorDevice,
  monitorType,
  isDeviceConfigVisible,
  // 方法
  handleDeviceHover: originalHandleDeviceHover,
  handleDeviceHoverEnd: originalHandleDeviceHoverEnd,
  handleTooltipMouseEnter,
  handleTooltipMouseLeave,
  handleDeviceLeftClick,
  handleDeviceRightClick,
  handleIntersectionMonitor,
  handleDeviceMonitor,
  handleRunningMonitor,
  handleCloseDeviceMonitor,
  handleSettingsUpdate,
  prepareAndShowMonitor,
  cleanup: cleanupDeviceInteraction,
} = useDeviceInteraction()

// 设备悬停处理 - 集成优先级管理
const handleDeviceHover = eventData => {
  // 调用原始处理函数
  originalHandleDeviceHover(eventData)

  // 注册到优先级管理系统
  if (hoveredDeviceInfo.value) {
    registerHover('DEVICE', {
      info: hoveredDeviceInfo.value,
      object3D: hoveredDevice3D.value,
      mousePosition: null,
    })
  }
}

const handleDeviceHoverEnd = eventData => {
  // 调用原始处理函数（包含300ms延迟逻辑）
  originalHandleDeviceHoverEnd(eventData)

  // 不立即从优先级系统注销，让设备的固定显示逻辑来控制
  // 注销将在 handleTooltipMouseLeave 或 handleCloseDeviceTooltip 中处理
}

// 提示框关闭处理函数 - 优先级注销由useDeviceInteraction处理
const handleCloseDeviceTooltip = () => {
  hoveredDeviceInfo.value = null
}

const handleCloseTrunkLineTooltip = () => {
  unregisterHover('TRUNK_LINE')
  hoveredTrunkLineInfo.value = null
}

const handleCloseAreaTooltip = () => {
  unregisterHover('AREA')
  hoveredAreaInfo.value = null
}

// 干线悬停状态管理
const hoveredTrunkLineInfo = ref(null)
const hoveredTrunkLine3D = ref(null)
const hoveredTrunkLineMousePosition = ref(null) // 鼠标在干线上的位置

// 区域悬停状态管理
const hoveredAreaInfo = ref(null)
const hoveredArea3D = ref(null)
const hoveredAreaMousePosition = ref(null) // 鼠标在区域上的位置

// 道路操作状态管理
const isRoadOperationInProgress = ref(false)

// 提示框优先级管理系统 - 解决重叠显示问题
const { registerHover, unregisterHover, isActiveTooltip } = useTooltipPriority()

// 监听设备提示框状态变化，自动管理优先级注销
watch(hoveredDeviceInfo, (newValue, oldValue) => {
  if (!newValue && oldValue) {
    // 设备提示框被隐藏时，从优先级系统注销
    unregisterHover('DEVICE')
  }
})
211
// 基于优先级的提示框显示状态
const shouldShowDeviceTooltip = computed(
  () => isActiveTooltip('DEVICE') && hoveredDeviceInfo.value
)
const shouldShowTrunkLineTooltip = computed(
  () => isActiveTooltip('TRUNK_LINE') && hoveredTrunkLineInfo.value
)
const shouldShowAreaTooltip = computed(
  () => isActiveTooltip('AREA') && hoveredAreaInfo.value
)

// 使用数据管理 Composable
const {
  // 状态 - 只保留实际使用的
  jinanCityGeoJson,
  jinanRoadNetworkGeoJson,
  userDrawnRoadsGeoJson,
  userPointsGeoJson,
  userPolygonsGeoJson,
  cityOutlineGeoCenter,
  roadNetworkGeoCenter,

  // IndexedDB 操作
  initIndexedDB,
  saveToIndexedDB,
  getFromIndexedDB,

  // 数据加载
  loadUserDrawnRoads,
  loadUserPoints,
  loadUserPolygons,

  // Store 初始化
  initializeAllStores,
  checkDataConsistency,

  // 数据保存
  saveUserDrawnRoads,
  saveUserPolygons,

  // 数据事件处理
  handleCityDataLoaded,
  handleRoadDataLoaded,

  // 中心点计算
  calculateAverageCenterFromGeoJson,
  calculateRoadNetworkCenter,

  // Store 引用
  devicesStore: dataManagerDevicesStore,
  trunkLinesStore: dataManagerTrunkLinesStore,
  regionsStore: dataManagerRegionsStore,
  sceneStateStore: dataManagerSceneStateStore,

  // 常量
  CITY_DATA_STORE,
  ROAD_DATA_STORE,
} = useDataManager()

// 使用交通指标 Composable
const {
  // 响应式数据
  deviceMetrics,
  corridorMetrics,
  areaMetrics,

  // 方法
  fetchAllMetrics,
  getDeviceMetricsById,
  getCorridorMetricsById,
  getAreaMetricsById,
} = useTrafficMetrics()

// 交通指标相关计算属性
const currentDeviceTrafficMetrics = computed(() => {
  if (!hoveredDeviceInfo.value) return null
  const deviceId = hoveredDeviceInfo.value.id
  const metricsData = getDeviceMetricsById(deviceId)
  return metricsData?.metrics || null
})

const currentCorridorTrafficMetrics = computed(() => {
  if (!hoveredTrunkLineInfo.value) return null
  const routeId = hoveredTrunkLineInfo.value.route_id
  const metricsData = getCorridorMetricsById(routeId)
  return metricsData?.metrics || null
})

const currentAreaTrafficMetrics = computed(() => {
  if (!hoveredAreaInfo.value) return null
  const regionId =
    hoveredAreaInfo.value.properties?.region_id ||
    hoveredAreaInfo.value.properties?.server_id
  const metricsData = getAreaMetricsById(regionId)
  return metricsData?.metrics || null
})

// 干线管理相关状态
const isTrunkLineCreationMode = ref(false)
const isTrunkLineEditingMode = ref(false)
const editingTrunkLineData = ref(null)
const selectedRoadIdsForTrunkLine = ref([])

// --- 数据源 & Store --- 现在由 useDataManager 提供
// Store 引用现在通过 useDataManager 获取，避免重复声明
const devicesStore = dataManagerDevicesStore
const { devices: apiDevices } = storeToRefs(devicesStore) // 从store获取API设备列表
const trunkLinesStore = dataManagerTrunkLinesStore
const { trunkLines } = storeToRefs(trunkLinesStore) // 从store获取干线列表
const regionsStore = dataManagerRegionsStore
// const { regions } = storeToRefs(regionsStore) // 从store获取区域列表 - 暂未使用
const sceneStateStore = dataManagerSceneStateStore

// --- GeoJSON 数据转换 ---
// 将从API获取的设备列表转换为GeoJSON格式，以便渲染
const apiDeviceGeoJson = computed(() => {
  if (!apiDevices.value) {
    return { type: 'FeatureCollection', features: [] }
  }
  // 确保apiDevices.value是数组
  if (!Array.isArray(apiDevices.value)) {
    return { type: 'FeatureCollection', features: [] }
  }
  const features = apiDevices.value
    .filter(
      device =>
        device.edge_coord &&
        typeof device.edge_coord.lng === 'number' &&
        typeof device.edge_coord.lat === 'number'
    )
    .map(device => ({
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: [device.edge_coord.lng, device.edge_coord.lat],
      },
      properties: {
        // 确保使用正确的字段名
        id: device.edge_id, // 使用edge_id作为id
        name: device.edge_name, // 使用edge_name作为name
        ip: device.ip_address, // 使用ip_address作为ip
        ...device, // 将整个设备对象作为属性，方便后续使用
        source: 'api',
      },
    }))
  return { type: 'FeatureCollection', features: features }
})

// 动态缩放和地图板属性的 ref
const cityOutlineScale = ref(COORDINATE_CONFIG.SCALE_FACTOR) // 继承全局配置的比例因子
const roadNetworkScale = ref(COORDINATE_CONFIG.SCALE_FACTOR) // 继承全局配置的比例因子

// 通用拾取 API 引用
import { ref as vueRef } from 'vue'
let pickingApi = vueRef(null)

// 将 getMaterialForRoadType 依赖的常量移至顶层
const roadColor = new THREE.Color(0x1a365d) // 深蓝色道路
const lineWidthFactor = 0.05 // 线宽转换因子 (原为 0.02)
const lineWidthsByHighwayType = {
  // 不同道路类型的目标线宽（非世界单位）
  trunk: 200,
  primary: 100,
  secondary: 50,
  tertiary: 30,
  // 'custom' 将使用 defaultLineWidth，可以按需在这里添加 'custom' 的特定宽度
}
const defaultRoadLineWidth = 40 // 默认目标线宽

// 城市轮廓相关常量
const CITY_OUTLINE_Y_LEVEL = COORDINATE_CONFIG.Y_LEVELS.CITY_OUTLINE
const CITY_OUTLINE_DEFAULT_LINEWIDTH = 1.5
const CITY_OUTLINE_HIGHLIGHT_LINEWIDTH = 4 // 重命名，更清晰
const CITY_OUTLINE_COLOR = 0x1a365d // 统一颜色，深灰蓝色

const processCityOutline = geojson => {
  // 处理城市轮廓，使用统一的颜色配置
  const centerPoint = geoJsonRenderer.processCityOutline(
    geojson,
    highlightedCityName.value,
    cityOutlineScale.value,
    cityOutlineGroup,
    CITY_OUTLINE_Y_LEVEL,
    CITY_OUTLINE_DEFAULT_LINEWIDTH,
    CITY_OUTLINE_HIGHLIGHT_LINEWIDTH,
    CITY_OUTLINE_COLOR,
    CITY_OUTLINE_COLOR // 统一颜色，简化参数
  )
  return centerPoint
}

// 将 getMaterialForRoadType 函数移至顶层 (因为 GeoJsonRenderer 可能需要它作为回调)
const getMaterialForRoadType = (highwayType, constructionType) => {
  // 确保 sceneManager 已初始化
  if (!sceneManager) {
    console.warn('sceneManager 未初始化，无法获取材质')
    return null
  }

  const renderer = sceneManager.getRenderer()
  let targetLineWidth
  const lineWidthFactorLocal = lineWidthFactor

  if (highwayType === 'construction' && constructionType) {
    targetLineWidth =
      lineWidthsByHighwayType[constructionType] || defaultRoadLineWidth
  } else if (lineWidthsByHighwayType[highwayType]) {
    targetLineWidth = lineWidthsByHighwayType[highwayType]
  } else {
    targetLineWidth = defaultRoadLineWidth
  }

  const worldLineWidth = targetLineWidth * lineWidthFactorLocal
  const cacheKey = `road_type_${highwayType}_construct_${constructionType}_lw_${targetLineWidth}`

  if (!materialsCache[cacheKey]) {
    const rendererSize = new THREE.Vector2()
    renderer.getSize(rendererSize)

    materialsCache[cacheKey] = new LineMaterial({
      color: roadColor,
      linewidth: worldLineWidth,
      vertexColors: false,
      dashed: false,
      alphaToCoverage: true,
      resolution: rendererSize,
    })
  } else {
    // 更新已缓存材质的分辨率
    const rendererSize = new THREE.Vector2()
    renderer.getSize(rendererSize)
    materialsCache[cacheKey].resolution.set(
      rendererSize.width,
      rendererSize.height
    )
  }
  return materialsCache[cacheKey]
}

// 初始化拾取与事件监听（改为使用 ThreeCanvas 提供的管线）
const initRaycastAndEvents = () => {
  // 设置材质缓存的重置回调
  sceneManager.setMaterialsForResizing(materialsCache)

  // 绑定容器级的点击结束（用于区分拖动与点击）
  const container = sceneManager.getContainer()
  if (!container.hasAttribute('data-global-listener')) {
    container.addEventListener('pointerup', handleSceneClick, false)
    container.setAttribute('data-global-listener', 'true')

    const handleMouseDown = event => {
      mouseDownTime = new Date().getTime()
      mouseDownPos = { x: event.clientX, y: event.clientY }
      if (event.button === 2) {
        document.addEventListener('mouseup', handleGlobalMouseUp, {
          once: true,
        })
      }
      document.addEventListener('mousemove', handleMouseMove, false)
      document.addEventListener('mouseup', handleMouseUp, { once: true })
    }

    const handleMouseMove = event => {
      if (!isDragging) {
        const distanceX = Math.abs(event.clientX - mouseDownPos.x)
        const distanceY = Math.abs(event.clientY - mouseDownPos.y)
        const totalDistance = Math.sqrt(
          distanceX * distanceX + distanceY * distanceY
        )
        if (totalDistance > 5) {
          isDragging = true
        }
      }
    }

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove)
    }

    const handleGlobalMouseUp = event => {
      if (event.button === 2) {
        sceneManager.getControls().dispatchEvent({ type: 'end' })
      }
      document.removeEventListener('mousemove', handleMouseMove)
    }

    container.addEventListener('mousedown', handleMouseDown, false)
  }
}

// 性能优化：缓存上次的距离和角度，减少不必要的计算
let lastDistance = 0

// 更新设备聚合显示（使用新的 composable）
const updateDeviceClustering = async () => {
  if (!sceneManager || !deviceMarkersGroup) return

  const camera = sceneManager.getCamera()
  const container = sceneManager.getContainer()
  const currentDistance = displayedCameraDistance.value
  const visibleDevices = deviceMarkersGroup.children

  // 记录聚合模式切换前的状态
  const wasClusterMode = deviceClusterSystem.isClusterMode.value

  // 使用设备聚合系统进行更新
  await deviceClusterSystem.update(
    camera,
    visibleDevices,
    container,
    currentDistance,
    deviceMarkersGroup
  )

  // 如果聚合模式发生了变化，清除设备悬停状态
  const isClusterMode = deviceClusterSystem.isClusterMode.value
  if (wasClusterMode !== isClusterMode) {
    // 清除设备悬停状态，避免交互混乱
    hoveredDeviceInfo.value = null
    hoveredDevice3D.value = null
  }
}

const handleCameraChange = throttle(() => {
  const controls = sceneManager.getControls()
  const camera = sceneManager.getCamera()

  const distance = controls.getDistance()
  const displayDistanceChanged = Math.abs(distance - lastDistance) > 0.1

  // 只在距离有明显变化时更新显示值
  if (displayDistanceChanged) {
    displayedCameraDistance.value = distance
    lastDistance = distance
  }

  // 设备图标现在使用固定大小，不需要根据距离更新

  // 更新设备聚合显示（仅在距离有明显变化时）
  if (displayDistanceChanged) {
    updateDeviceClustering()
  }

  // 确保相机距离不超过限制（添加小的容差值避免浮点数精度问题）
  const tolerance = 0.001 // 1毫米的容差
  if (distance > maxCameraDistance.value + tolerance) {
    const direction = new THREE.Vector3()
    direction.subVectors(camera.position, controls.target)
    direction.normalize()
    direction.multiplyScalar(maxCameraDistance.value)
    camera.position.copy(controls.target).add(direction)
    controls.update()
    displayedCameraDistance.value = maxCameraDistance.value
    return // 避免在同一帧内执行后续逻辑
  }

  // 只有距离变化超过阈值时才更新道路（进一步优化）
  if (displayDistanceChanged) {
    const isSignificantChange =
      Math.abs(distance - lastProcessedDistance.value) >
      distanceUpdateThreshold.value
    const crossedThreshold = checkRoadThresholdsCrossed(
      lastProcessedDistance.value,
      distance
    )

    if (
      (isSignificantChange || crossedThreshold) &&
      dynamicRoadLoader &&
      roadDisplayMode.value === 'auto'
    ) {
      lastProcessedDistance.value = distance

      // 直接执行道路更新，减少延迟
      dynamicRoadLoader.updateRoadsByDistance(distance, camera)

      // 在路网更新后恢复交通指标颜色
      nextTick(() => {
        updateAllColors()
      })

      // 同时更新智能路名显示
      if (
        smartRoadLabelManager &&
        roadLabelsGroup &&
        jinanRoadNetworkGeoJson.value
      ) {
        const currentRoadLevel = getCurrentRoadLevel(distance)
        smartRoadLabelManager.renderLabels(
          jinanRoadNetworkGeoJson.value,
          roadNetworkScale.value,
          roadLabelsGroup,
          COORDINATE_CONFIG.Y_LEVELS.ROADS,
          camera,
          currentRoadLevel
        )
      }
    }

    // 只在距离变化时更新标签的视锥体剔除
    if (dynamicRoadLoader) {
      dynamicRoadLoader.updateVisibleLabelsByCamera(camera)
    }

    // 标签缩放更新：使用独立的距离记录，避免与道路更新冲突
    const labelScaleThreshold = 0.2 // 适中的标签缩放阈值，减少更新频率
    const isLabelScaleChange =
      Math.abs(distance - lastLabelScaleDistance.value) > labelScaleThreshold

    if (isLabelScaleChange) {
      lastLabelScaleDistance.value = distance // 更新标签缩放距离记录

      // 直接执行，避免requestAnimationFrame的额外开销
      // 更新用户道路标签的缩放
      if (userRoadLabelManager && userDrawnRoadLabelsGroup) {
        userRoadLabelManager.updateUserRoadLabelsScale(
          userDrawnRoadLabelsGroup,
          camera
        )
      }

      // 更新智能路名标签的缩放
      if (smartRoadLabelManager && roadLabelsGroup) {
        smartRoadLabelManager.updateLabelsScale(roadLabelsGroup, camera)
      }

      // 更新用户区域标签的缩放
      if (userPolygonsGroup) {
        updateUserAreaLabelsScale(userPolygonsGroup, camera)
      }

      // 更新城市轮廓标签的缩放
      if (labelsGroup) {
        updateUserAreaLabelsScale(labelsGroup, camera)
      }
    }
  }
}, 100) // 优化throttle延迟，平衡响应性和性能

// 监听 GeoJSON 数据并处理它
watchEffect(() => {
  if (isThreeInitialized.value && jinanCityGeoJson.value) {
    // 处理城市轮廓数据
    const centerPoint = processCityOutline(jinanCityGeoJson.value)
    if (centerPoint) {
      // 保存城市中心点，但不立即聚焦（等待路网数据）
      cityOutlineGeoCenter.value = transformSceneToGeo(
        centerPoint.x,
        centerPoint.z
      )
    } else {
      // 如果没有找到高亮城市，则计算整个轮廓的中心
      const calculatedCenter = calculateAverageCenterFromGeoJson(
        jinanCityGeoJson.value
      )
      cityOutlineGeoCenter.value = calculatedCenter
    }

    // 检查是否有路网数据，如果有则聚焦到路网中心，否则等待
    if (jinanRoadNetworkGeoJson.value) {
      nextTick(() => {
        focusOnRoadNetworkCenter()
      })
    }
  }
})

// 监听路网数据更新
watchEffect(() => {
  if (
    isThreeInitialized.value &&
    jinanRoadNetworkGeoJson.value &&
    sceneManager
  ) {
    // 计算路网中心
    const calculatedCenter = calculateRoadNetworkCenter(
      jinanRoadNetworkGeoJson.value
    )
    if (calculatedCenter) {
      roadNetworkGeoCenter.value = calculatedCenter

      // 聚焦到路网中心
      nextTick(() => {
        focusOnRoadNetworkCenter()
      })
    } else {
      // 如果无法计算路网中心，但有城市中心，则聚焦到城市中心
      nextTick(() => {
        focusOnCalculatedCenter()
      })
    }
  }
})

watchEffect(
  () => {
    // 将路网处理与相机距离解耦，只在路网数据或可见性状态变化时处理
    if (
      isThreeInitialized.value &&
      jinanRoadNetworkGeoJson.value &&
      dynamicRoadLoader &&
      sceneManager
    ) {
      const camera = sceneManager.getCamera()
      // 始终尝试更新显示，根据当前模式（自动或手动选择）
      if (roadDisplayMode.value === 'auto') {
        dynamicRoadLoader.updateRoadsByDistance(
          displayedCameraDistance.value,
          camera
        )
      } else {
        // 如果是手动选择的特定级别，调用setRoadDisplayMode再次刷新
        setRoadDisplayMode(roadDisplayMode.value)
      }

      // 在路网更新后恢复高亮状态和交通指标颜色
      nextTick(() => {
        restoreRoadHighlights()
        // 恢复交通指标颜色
        updateAllColors()
      })
    }
  },
  { flush: 'post' }
) // 使用post确保在DOM更新后执行

// 添加对摄像机距离变化的监听，确保在距离变化时更新路网
watch(
  () => displayedCameraDistance.value,
  (newDistance, oldDistance) => {
    // 只在距离有较大变化时更新（大于阈值或跨越显示级别边界）
    const crossedThreshold = checkRoadThresholdsCrossed(
      oldDistance,
      newDistance
    )

    const significantChange = Math.abs(newDistance - oldDistance) > 50 // 距离变化超过50米

    if (
      (crossedThreshold || significantChange) &&
      dynamicRoadLoader &&
      roadDisplayMode.value === 'auto' &&
      sceneManager
    ) {
      const camera = sceneManager.getCamera()
      dynamicRoadLoader.updateRoadsByDistance(newDistance, camera)

      // 在路网更新后恢复交通指标颜色
      nextTick(() => {
        updateAllColors()
      })
    }
  }
)

// watchEffect 以根据相机距离和 GeoJSON 数据响应式地更新区域标签
watchEffect(() => {
  if (isThreeInitialized.value && jinanCityGeoJson.value && labelsGroup) {
    // 确保数据和标签组已准备好
    processDistrictLabels()
  } else if (
    isThreeInitialized.value &&
    !jinanCityGeoJson.value &&
    labelsGroup
  ) {
    // 如果 GeoJSON 数据不可用 (例如，被设为 null)，确保清除标签。
    // processDistrictLabels 在其开始处也会这样做，但这里针对数据消失的场景提供额外保障。
    while (labelsGroup.children.length > 0) {
      const oldChild = labelsGroup.children[0]
      labelsGroup.remove(oldChild)
      if (oldChild.geometry) {
        oldChild.geometry.dispose()
      }
      if (oldChild.material) {
        if (oldChild.material.map) {
          oldChild.material.map.dispose()
        }
        oldChild.material.dispose()
      }
    }
  }
})

// 处理 DataManager 组件的事件 - 现在使用 useDataManager 提供的函数
// handleCityDataLoaded 和 handleRoadDataLoaded 现在由 useDataManager 提供

const updateHighlightCityName = name => {
  highlightedCityName.value = name

  // 如果城市轮廓数据已存在，则用新的高亮名称重新处理它
  if (jinanCityGeoJson.value && sceneManager && geoJsonRenderer) {
    const centerPoint = processCityOutline(jinanCityGeoJson.value)
    if (centerPoint) {
      cityOutlineGeoCenter.value = transformSceneToGeo(
        centerPoint.x,
        centerPoint.z
      )

      // 优先聚焦到路网中心，如果有的话
      if (roadNetworkGeoCenter.value) {
        focusOnRoadNetworkCenter()
      } else if (sceneManager) {
        sceneManager.focusOnPoint(centerPoint, 5)
      }
    }
  }
}

// 原 focusCameraOnCityCenter 函数将被移除或其逻辑整合进 initializeLODSystem

// ... a后续可能是其他函数或 onMounted 等 ...

const processDeviceLocations = geojson => {
  if (!geoJsonRenderer || !deviceMarkersGroup) {
    // GeoJsonRenderer not ready or group missing
    return
  }

  // 设备图标渲染函数：只负责渲染设备，可见性由外部控制

  if (!geojson || !geojson.features || geojson.features.length === 0) {
    geoJsonRenderer.processDeviceLocations(
      null,
      roadNetworkScale.value,
      deviceMarkersGroup,
      COORDINATE_CONFIG.Y_LEVELS.DEVICES,
      null, // 不设置静态颜色，由交通指标动态控制
      0.05
    )
    return
  }

  geoJsonRenderer.processDeviceLocations(
    geojson,
    roadNetworkScale.value, // 或 deviceSpecificScale.value
    deviceMarkersGroup,
    COORDINATE_CONFIG.Y_LEVELS.DEVICES, // yLevel
    null, // 不设置静态颜色，由交通指标动态控制
    0.05, // markerRadius
    isDeviceLabelVisible.value // 添加：控制标签可见性
  )
}

/**
 * 更新设备颜色基于交通指标
 */
const updateDeviceColors = () => {
  if (!deviceMarkersGroup || !deviceMetrics.value.length) return

  deviceMarkersGroup.children.forEach(deviceMarker => {
    const deviceData = deviceMarker.userData.feature
    const deviceId = deviceData.properties.edge_id || deviceData.properties.id

    if (deviceId) {
      const metricsData = getDeviceMetricsById(deviceId)
      if (metricsData && metricsData.metrics.serviceLevel) {
        const threeColor = getServiceLevelThreeColor(
          metricsData.metrics.serviceLevel
        )

        // 更新设备图标颜色
        if (deviceMarker.material) {
          deviceMarker.material.color.setRGB(
            threeColor.r,
            threeColor.g,
            threeColor.b
          )
        }

        // 如果设备有子元素（如边框），也更新它们的颜色
        if (deviceMarker.children[0]?.material) {
          deviceMarker.children[0].material.color.setRGB(
            threeColor.r,
            threeColor.g,
            threeColor.b
          )
        }
      }
    }
  })
}

/**
 * 更新干线颜色基于交通指标
 */
const updateCorridorColors = () => {
  if (!trunkLineRenderer?.trunkLinesGroup || !corridorMetrics.value.length)
    return

  // 遍历干线组中的每个干线Group
  trunkLineRenderer.trunkLinesGroup.children.forEach(trunkLineGroup => {
    const trunkLineId = trunkLineGroup.userData?.trunkLineId

    if (trunkLineId) {
      const metricsData = getCorridorMetricsById(trunkLineId)
      if (metricsData && metricsData.metrics.serviceLevel) {
        const threeColor = getServiceLevelThreeColor(
          metricsData.metrics.serviceLevel
        )

        // 更新干线组中所有Mesh对象的颜色
        trunkLineGroup.children.forEach(mesh => {
          if (mesh.material) {
            mesh.material.color.setRGB(threeColor.r, threeColor.g, threeColor.b)
          }
        })
      }
    }
  })
}

/**
 * 更新区域颜色基于交通指标
 */
const updateAreaColors = () => {
  if (!userPolygonsGroup || !areaMetrics.value.length) return

  userPolygonsGroup.children.forEach(child => {
    const feature = child.userData?.feature
    const regionId =
      feature?.properties?.region_id || feature?.properties?.server_id

    if (regionId) {
      const metricsData = getAreaMetricsById(regionId)
      if (metricsData && metricsData.metrics.serviceLevel) {
        const threeColor = getServiceLevelThreeColor(
          metricsData.metrics.serviceLevel
        )

        // 只更新填充Mesh的颜色，保持轮廓颜色不变
        if (
          child.userData?.featureType === 'user-polygon-fill' &&
          child.material
        ) {
          child.material.color.setRGB(threeColor.r, threeColor.g, threeColor.b)
        }
      }
    }
  })
}

/**
 * 更新所有对象的颜色
 */
const updateAllColors = () => {
  updateDeviceColors()
  updateCorridorColors()
  updateAreaColors()
}

/**
 * 更新干线动态宽度
 */
const updateTrunkLineDynamicWidth = () => {
  if (!trunkLineRenderer || !sceneManager) return

  const camera = sceneManager.getCamera()
  if (!camera) return

  const controls = sceneManager.getControls()
  if (!controls) return

  const cameraDistance = controls.getDistance()
  trunkLineRenderer.updateDynamicWidth(cameraDistance)
}

/**
 * 初始化交通指标数据
 */
const initTrafficMetrics = async () => {
  await fetchAllMetrics()

  // 等待一小段时间确保数据加载完成，然后更新颜色
  setTimeout(() => {
    updateAllColors()
  }, 1000)

  // 设置定时更新（每3秒）- 添加内存泄漏防护
  let isUpdating = false // 防止并发更新
  const trafficMetricsTimer = setInterval(async () => {
    // 防止并发更新导致内存堆积
    if (isUpdating) return

    isUpdating = true
    try {
      await fetchAllMetrics()
      updateAllColors()
    } catch (error) {
      console.warn('交通指标更新失败:', error)
    } finally {
      isUpdating = false
    }
  }, 3000)

  // 在组件卸载时清理定时器
  onBeforeUnmount(() => {
    if (trafficMetricsTimer) {
      clearInterval(trafficMetricsTimer)
    }
    // 强制垃圾回收提示
    isUpdating = false
  })
}

onMounted(async () => {
  await nextTick() // 确保 DOM 更新完成

  try {
    // 初始化 IndexedDB
    await initIndexedDB()
    // IndexedDB 初始化成功

    // 加载用户设置
    await loadUserSettings()
  } catch (error) {
    // IndexedDB 初始化失败，继续使用内存存储
  }

  // 添加键盘事件监听
  window.addEventListener('keydown', handleKeyDown)
  window.addEventListener('keyup', handleKeyUp)

  // 添加页面卸载时的强制清理
  window.addEventListener('beforeunload', () => {
    // 页面卸载时的清理，sceneManager 可能还未初始化,这个空函数可以删除吗
  })
  // 添加窗口大小变化事件监听
  window.addEventListener('resize', handleWindowResize)

  // 获取看板数据
  await fetchDashboardData()

  // 启动看板数据定时刷新
  startDashboardDataRefresh()

  // 监听路网数据更新事件
  eventBus.on('roadNetworkUpdated', handleRoadNetworkUpdated)

  // 从localStorage加载最大相机距离
  const savedMaxDistance = localStorage.getItem('maxCameraDistance')
  if (savedMaxDistance) {
    const parsedDistance = parseFloat(savedMaxDistance)
    if (!isNaN(parsedDistance)) {
      maxCameraDistance.value = parsedDistance
    }
  }
})

// 窗口resize事件处理函数
const handleWindowResize = () => {
  if (isMapConfigModalVisible.value || isDeviceConfigVisible.value) {
    updateMapConfigButtonRect()
  }
}

// 用于存储看板数据刷新定时器ID
let dashboardRefreshInterval = null

onBeforeUnmount(() => {
  // 移除事件监听器
  window.removeEventListener('resize', handleWindowResize)
  window.removeEventListener('keydown', handleKeyDown)
  window.removeEventListener('keyup', handleKeyUp)
  eventBus.off('roadNetworkUpdated', handleRoadNetworkUpdated)

  // 移除相机变化事件监听器
  if (sceneManager) {
    const controls = sceneManager.getControls()
    if (controls) {
      controls.removeEventListener('change', handleCameraChange)
    }
  }

  // 清理定时器
  if (dashboardRefreshInterval) {
    clearInterval(dashboardRefreshInterval)
    dashboardRefreshInterval = null
  }

  // 清理设备交互相关的定时器
  cleanupDeviceInteraction()

  // 清理设备聚合资源
  deviceClusterSystem.clearClusterGroup()

  // 清理其他管理器
  if (trunkLineRenderer) {
    trunkLineRenderer.dispose()
    trunkLineRenderer = null
  }

  if (previewLineManager) {
    previewLineManager.dispose()
    previewLineManager = null
  }

  if (geoJsonRenderer) {
    geoJsonRenderer = null
  }
  if (dynamicRoadLoader) {
    dynamicRoadLoader = null
  }
  if (smartRoadLabelManager) {
    smartRoadLabelManager = null
  }
  if (userRoadLabelManager) {
    userRoadLabelManager = null
  }

  // 清理缓存的材质
  Object.values(materialsCache).forEach(material => {
    if (material) {
      material.dispose()
    }
  })
  materialsCache = {}

  // 移除点击外部关闭菜单的事件监听
  document.removeEventListener('click', handleOutsideClick)
})

// 优化的文字纹理创建函数
const createTextTexture = (
  text,
  fontSize = 32,
  textColor = '#4a5568',
  strokeColor = 'black',
  strokeWidth = 1
) => {
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')

  // 使用更高的分辨率倍数提高文字清晰度
  const pixelRatio = window.devicePixelRatio || 1
  const scaleFactor = Math.max(4, pixelRatio * 2) // 至少4倍分辨率

  // 根据文本长度和字体大小估算画布大小
  const font = `bold ${fontSize}px "Microsoft YaHei", "PingFang SC", Arial, sans-serif`
  context.font = font
  const textMetrics = context.measureText(text)
  const baseWidth = textMetrics.width + strokeWidth * 2 + 20
  const baseHeight = fontSize + strokeWidth * 2 + 10

  // 应用高分辨率
  const canvasWidth = baseWidth * scaleFactor
  const canvasHeight = baseHeight * scaleFactor
  canvas.width = canvasWidth
  canvas.height = canvasHeight

  // 缩放上下文以匹配高分辨率
  context.scale(scaleFactor, scaleFactor)

  // 画布调整大小后重新应用字体和设置
  context.font = `bold ${fontSize}px "Microsoft YaHei", "PingFang SC", Arial, sans-serif`
  context.fillStyle = textColor
  context.strokeStyle = strokeColor
  context.lineWidth = strokeWidth
  context.textAlign = 'center'
  context.textBaseline = 'middle'

  // 启用最高质量的抗锯齿
  context.textRenderingOptimization = 'optimizeQuality'
  context.imageSmoothingEnabled = true
  context.imageSmoothingQuality = 'high'

  // 将文本位置调整到画布中心（使用基础尺寸）
  const textX = baseWidth / 2
  const textY = baseHeight / 2

  // 绘制文字描边和填充
  if (strokeWidth > 0) {
    context.strokeText(text, textX, textY)
  }
  context.fillText(text, textX, textY)

  const texture = new THREE.CanvasTexture(canvas)
  texture.needsUpdate = true

  // 设置最佳纹理过滤器以提高清晰度
  texture.magFilter = THREE.LinearFilter
  texture.minFilter = THREE.LinearFilter
  texture.generateMipmaps = false
  texture.flipY = true // 修复文字上下颠倒问题

  return texture
}

const calculatePolygonCenter = coordinates => {
  let minLng = Infinity,
    maxLng = -Infinity,
    minLat = Infinity,
    maxLat = -Infinity
  coordinates.forEach(coord => {
    const [lng, lat] = coord
    minLng = Math.min(minLng, lng)
    maxLng = Math.max(maxLng, lng)
    minLat = Math.min(minLat, lat)
    maxLat = Math.max(maxLat, lat)
  })
  return [(minLng + maxLng) / 2, (minLat + maxLat) / 2]
}

// 更新区域标签缩放的函数
const updateDistrictLabelsScale = cameraDistance => {
  // 计算新的缩放比例
  let fontSizeScale = 0.5
  if (cameraDistance < 10) {
    fontSizeScale = 0.5 + (cameraDistance / 10) * 0.5
  } else if (cameraDistance < 20) {
    fontSizeScale = 1.0 + ((cameraDistance - 10) / 10) * 0.6
  } else if (cameraDistance < 50) {
    fontSizeScale = 1.6 + ((cameraDistance - 20) / 30) * 0.8
  } else {
    fontSizeScale = 2.4 + Math.min((cameraDistance - 50) / 50, 1) * 1.0
  }

  // 更新所有现有标签的缩放
  labelsGroup.children.forEach(labelMesh => {
    if (labelMesh.userData?.isScalableLabel) {
      labelMesh.scale.set(fontSizeScale, fontSizeScale, 1)
    }
  })
}

// 清除区域标签的辅助函数
const clearDistrictLabels = () => {
  while (labelsGroup.children.length > 0) {
    const oldChild = labelsGroup.children[0]
    labelsGroup.remove(oldChild)
    if (oldChild.geometry) {
      oldChild.geometry.dispose()
    }
    if (oldChild.material) {
      if (oldChild.material.map) {
        oldChild.material.map.dispose()
      }
      oldChild.material.dispose()
    }
  }
}

const processDistrictLabels = () => {
  if (!labelsGroup) {
    // labelsGroup 未初始化，无法处理区域标签
    return
  }

  const cameraDistance = displayedCameraDistance.value

  // 当摄像机距离设置为5以下时销毁标签
  if (cameraDistance < 5) {
    if (labelsGroup.children.length > 0) {
      clearDistrictLabels() // 真正销毁标签
    }
    return
  }

  // 如果标签已存在，只更新缩放，不重建
  if (labelsGroup.children.length > 0) {
    updateDistrictLabelsScale(cameraDistance)
    return
  }

  // 检查 GeoJSON 数据是否就绪
  if (!jinanCityGeoJson.value || !jinanCityGeoJson.value.features) {
    return
  }

  // 首次创建标签
  createDistrictLabels()
}

// 创建区域标签的函数
const createDistrictLabels = () => {
  const cameraDistance = displayedCameraDistance.value
  const currentScale = cityOutlineScale.value
  const labelYPosition = COORDINATE_CONFIG.Y_LEVELS.AREA_LABELS

  // 根据相机距离计算字体大小
  const baseFontSize = 48
  let fontSizeScale = 0.5

  if (cameraDistance < 10) {
    fontSizeScale = 0.5 + (cameraDistance / 10) * 0.5
  } else if (cameraDistance < 20) {
    fontSizeScale = 1.0 + ((cameraDistance - 10) / 10) * 0.6
  } else if (cameraDistance < 50) {
    fontSizeScale = 1.6 + ((cameraDistance - 20) / 30) * 0.8
  } else {
    fontSizeScale = 2.4 + Math.min((cameraDistance - 50) / 50, 1) * 1.0
  }

  const adjustedFontSize = Math.round(baseFontSize * fontSizeScale)

  // 处理全部区域标签（移除数量上限）
  for (const feature of jinanCityGeoJson.value.features) {
    const name = feature.properties?.name
    if (!name) continue
    // 计算区域标签的中心点
    let representativeCoord = null
    // 处理不同类型的几何体
    if (
      feature.geometry.type === 'Polygon' &&
      feature.geometry.coordinates[0].length > 0
    ) {
      // 对于 Polygon，使用其第一个多边形外环的中心
      representativeCoord = calculatePolygonCenter(
        feature.geometry.coordinates[0]
      )
    } else if (
      feature.geometry.type === 'MultiPolygon' &&
      feature.geometry.coordinates[0] &&
      feature.geometry.coordinates[0][0].length > 0
    ) {
      // 对于 MultiPolygon，使用其第一个多边形外环的中心
      representativeCoord = calculatePolygonCenter(
        feature.geometry.coordinates[0][0]
      )
    } else if (feature.geometry.type === 'Point') {
      // 处理 Point 特征（如果存在）
      representativeCoord = feature.geometry.coordinates
    }

    if (representativeCoord) {
      const texture = createTextTexture(name, adjustedFontSize) // 使用调整后的字体大小

      // 创建平放在地面的标签几何体 - 适中的尺寸便于观察缩放效果
      const geometryWidth = 0.8 // 适中宽度
      const geometryHeight = 0.2 // 适中高度
      const labelGeometry = new THREE.PlaneGeometry(
        geometryWidth,
        geometryHeight
      )
      const labelMaterial = new THREE.MeshBasicMaterial({
        map: texture,
        transparent: true,
        depthTest: false, // 禁用深度测试，确保标签始终可见
        side: THREE.DoubleSide, // 双面材质，确保从各个角度都能看到
        alphaTest: 0.1, // 设置alpha测试，避免透明度问题
      })
      const labelMesh = new THREE.Mesh(labelGeometry, labelMaterial)

      // 计算位置
      const transformedLabelPos = transformGeoJsonCoordinates(
        [representativeCoord],
        0,
        currentScale
      )
      if (transformedLabelPos.length > 0) {
        labelMesh.position.set(
          transformedLabelPos[0][0],
          labelYPosition, // 使用原始标签层级高度
          transformedLabelPos[0][1]
        )
      }

      // 旋转标签使其平放在地面，并修复文字方向
      labelMesh.rotation.x = -Math.PI / 2

      // 应用字体缩放，让缩放效果更明显
      labelMesh.scale.set(fontSizeScale, fontSizeScale, 1)

      // 设置用户数据，用于点击检测和区域关联
      labelMesh.userData = {
        feature: feature,
        featureType: 'district-label',
        parentFeatureName: name,
        isAreaLabel: true,
        isScalableLabel: true, // 标记为可缩放标签
      }

      labelsGroup.add(labelMesh)
    }
  }
}

// 更新用户区域标签的缩放
const updateUserAreaLabelsScale = (targetGroup, camera) => {
  if (!targetGroup || !camera || !targetGroup.children.length) return

  // 批量处理，减少函数调用开销
  const cameraPosition = camera.position
  const baseDistance = 5
  const dynamicScaleMultiplier = 1.0
  const defaultRoadTypeScale = 1.3

  targetGroup.children.forEach(child => {
    if (child.userData?.isScalableLabel) {
      const distance = cameraPosition.distanceTo(child.position)
      const rawScale = distance / baseDistance
      const dynamicScale = Math.sqrt(rawScale) * dynamicScaleMultiplier
      const roadTypeScale =
        child.userData.scaleMultiplier || defaultRoadTypeScale
      const finalScale = dynamicScale * roadTypeScale

      child.scale.set(finalScale, finalScale, 1)
    }
  })
}

// Three.js 就绪处理函数
const onThreeReady = ({ sceneManager: sm, groups, picking }) => {
  // 赋值给原来的变量
  sceneManager = sm

  // 从 groups 中获取分组
  cityOutlineGroup = groups.cityOutlineGroup
  roadNetworkGroup = groups.roadNetworkGroup
  labelsGroup = groups.labelsGroup
  roadLabelsGroup = groups.roadLabelsGroup
  deviceMarkersGroup = groups.deviceMarkersGroup
  deviceClusterGroup = groups.deviceClusterGroup
  userDrawingInProgressGroup = groups.userDrawingInProgressGroup
  finalizedUserRoadsGroup = groups.finalizedUserRoadsGroup
  userDrawnRoadLabelsGroup = groups.userDrawnRoadLabelsGroup
  userPointsGroup = groups.userPointsGroup
  userPolygonsGroup = groups.userPolygonsGroup

  // 保存通用拾取 API（可按需使用）
  pickingApi.value = picking

  // 标记初始化完成
  isThreeInitialized.value = true

  // 设置相机变化回调（在sceneManager完全初始化后）
  const controls = sceneManager.getControls()
  if (controls) {
    controls.addEventListener('change', handleCameraChange)
  }

  // 继续原来的初始化流程
  initAfterThreeReady()
}

// Three.js 错误处理
const onThreeError = error => {
  console.error('Three.js 初始化失败:', error)
  message.error('三维场景初始化失败，请刷新页面重试')
}

// 原来的 initThree 逻辑移到这个函数
const initAfterThreeReady = () => {
  // 初始化 GeoJsonRenderer
  geoJsonRenderer = new GeoJsonRenderer(sceneManager, materialsCache)

  // 初始化动态道路加载器
  dynamicRoadLoader = new DynamicRoadLoader(
    geoJsonRenderer,
    roadNetworkGroup,
    roadLabelsGroup,
    {
      roadYLevel: COORDINATE_CONFIG.Y_LEVELS.ROADS,
      roadNetworkScale: roadNetworkScale.value,
      getMaterialForRoadType: getMaterialForRoadType,
    }
  )

  // 初始化干线渲染器
  trunkLineRenderer = new TrunkLineRenderer(sceneManager, {
    roadNetworkScale: roadNetworkScale.value,
    yLevel: COORDINATE_CONFIG.Y_LEVELS.TRUNK_LINES, // 使用专门的干线层级
    defaultLineWidth: 40, // 再增加一倍，从20增加到40
    defaultOpacity: 0.9,
  })

  // 初始化智能路名标签管理器
  smartRoadLabelManager = new SmartRoadLabelManager()

  // 初始化用户道路标签管理器
  userRoadLabelManager = new UserRoadLabelManager()

  // 初始化预览线管理器
  previewLineManager = new PreviewLineManager(sceneManager, {
    updateInterval: 16, // 约60fps的更新频率
  })

  // 仍然设置默认阈值，但不加载任何数据
  dynamicRoadLoader.setDistanceThresholds(roadDistanceThresholds.value)

  // 初始化设备聚合系统
  deviceClusterSystem.initClusterGroup(deviceClusterGroup)

  // 初始化射线拾取和事件监听
  initRaycastAndEvents()

  // 继续数据加载流程
  loadDataAfterThreeReady()
}

// 原来的数据加载逻辑
const loadDataAfterThreeReady = async () => {
  try {
    // 1. 先初始化所有Store，确保服务器数据可用
    await initializeAllStores()

    // 2. 检查数据一致性
    await checkDataConsistency()

    // 3. 加载用户绘制的道路数据
    await loadUserDrawnRoads()

    // 4. 加载用户点位数据
    await loadUserPoints()

    // 5. 加载用户区域数据（现在会优先从服务器获取）
    await loadUserPolygons()

    // 从 IndexedDB 加载城市数据
    const cityData = await getFromIndexedDB(CITY_DATA_STORE, 'default')
    if (cityData) {
      // 从IndexedDB加载了城市数据
      jinanCityGeoJson.value = cityData
    } else {
      // 如果IndexedDB中没有数据，则从服务器获取ID为1的行政区划数据
      try {
        const API_BASE_URL = getBaseUrl('geoData')
        const response = await axios.get(`${API_BASE_URL}/boundarys/1`)

        if (response.data && response.data.boundary) {
          // 保存到 IndexedDB
          await saveToIndexedDB(
            CITY_DATA_STORE,
            'default',
            response.data.boundary
          )

          // 加载到地图
          jinanCityGeoJson.value = response.data.boundary
        }
      } catch (error) {
        // 自动获取行政区划数据失败，继续使用默认数据
      }
    }

    // 从 IndexedDB 加载路网数据
    const roadData = await getFromIndexedDB(ROAD_DATA_STORE, 'default')
    if (roadData) {
      // 从IndexedDB加载了路网数据
      jinanRoadNetworkGeoJson.value = roadData
      // 如果动态道路加载器已经初始化，更新其数据源
      if (dynamicRoadLoader) {
        dynamicRoadLoader.updateRoadDataSource(roadData)
        const camera = sceneManager?.getCamera()
        if (camera) {
          if (roadDisplayMode.value === 'auto') {
            dynamicRoadLoader.updateRoadsByDistance(
              displayedCameraDistance.value,
              camera
            )

            // 在路网更新后恢复交通指标颜色
            nextTick(() => {
              updateAllColors()
            })
          } else {
            setRoadDisplayMode(roadDisplayMode.value) // 使用用户之前设置的显示模式
          }

          if (smartRoadLabelManager && roadLabelsGroup) {
            const currentRoadLevel = getCurrentRoadLevel(
              displayedCameraDistance.value
            )
            smartRoadLabelManager.renderLabels(
              roadData,
              roadNetworkScale.value,
              roadLabelsGroup,
              COORDINATE_CONFIG.Y_LEVELS.ROADS,
              camera,
              currentRoadLevel
            )
          }
        }
      }
    } else {
      // 如果IndexedDB中没有数据，则从服务器获取ID为1的路网数据
      try {
        const API_BASE_URL = getBaseUrl('geoData')
        const response = await axios.get(`${API_BASE_URL}/road_networks/1`)

        if (response.data && response.data.road_network) {
          try {
            // 保存到 IndexedDB
            await saveToIndexedDB(
              ROAD_DATA_STORE,
              'default',
              response.data.road_network
            )
          } catch (saveError) {
            console.error('路网数据保存失败:', saveError)
            // 即使保存失败，也继续加载到地图
          }

          // 加载到地图
          jinanRoadNetworkGeoJson.value = response.data.road_network

          // 如果动态道路加载器已经初始化，更新其数据源
          if (dynamicRoadLoader) {
            dynamicRoadLoader.updateRoadDataSource(response.data.road_network)
            const camera = sceneManager?.getCamera()
            if (camera) {
              if (roadDisplayMode.value === 'auto') {
                dynamicRoadLoader.updateRoadsByDistance(
                  displayedCameraDistance.value,
                  camera
                )

                // 在路网更新后恢复交通指标颜色
                nextTick(() => {
                  updateAllColors()
                })
              } else {
                setRoadDisplayMode(roadDisplayMode.value)
              }

              // 使用路名管理器渲染路名
              if (
                smartRoadLabelManager &&
                roadLabelsGroup &&
                response.data &&
                camera
              ) {
                const currentRoadLevel = getCurrentRoadLevel(
                  displayedCameraDistance.value
                )
                smartRoadLabelManager.renderLabels(
                  response.data,
                  roadNetworkScale.value,
                  roadLabelsGroup,
                  COORDINATE_CONFIG.Y_LEVELS.ROADS,
                  camera,
                  currentRoadLevel
                )
              }
            }
          }
        }
      } catch (error) {
        // 自动获取路网数据失败，继续使用缓存数据
      }
    }
  } catch (error) {
    // 加载数据失败，使用默认配置
  }

  // 获取导航按钮位置
  nextTick(() => {
    updateMapConfigButtonRect()
  })

  // 初始化交通指标数据
  initTrafficMetrics()
}

// 聚焦到地理点的函数
const focusOnGeoPoint = (
  geoPoint,
  scale,
  yLevel,
  viewDistanceFactor = 1.5,
  minDistance = 20,
  minHeight = 15
) => {
  if (!geoPoint || !sceneManager) return false

  const controls = sceneManager.getControls()
  const camera = sceneManager.getCamera()
  if (!controls || !camera) return false

  // 将地理中心转换为3D场景坐标
  // 确保 geoPoint 格式正确：如果是对象 {lng, lat}，转换为数组 [lng, lat]
  const geoPointArray = Array.isArray(geoPoint)
    ? geoPoint
    : [geoPoint.lng || geoPoint[0], geoPoint.lat || geoPoint[1]]

  const transformedCenterArray = transformGeoJsonCoordinates(
    [geoPointArray],
    0,
    scale
  )
  if (transformedCenterArray.length === 0) return false

  const sceneCenter = new THREE.Vector3(
    transformedCenterArray[0][0],
    yLevel,
    transformedCenterArray[0][1]
  )

  // 计算合适的观察距离
  const viewDistance = scale * viewDistanceFactor
  const cameraOffsetZ = Math.max(viewDistance, minDistance) // 保证一个最小距离
  const cameraOffsetY = Math.max(viewDistance / 2, minHeight) // 保持一定高度

  // 设置相机位置和目标
  controls.target.copy(sceneCenter)
  camera.position.set(
    sceneCenter.x,
    sceneCenter.y + cameraOffsetY,
    sceneCenter.z + cameraOffsetZ
  )

  controls.update()
  // 相机已聚焦到坐标点

  return true
}

// 聚焦到路网中心
const focusOnRoadNetworkCenter = () => {
  if (!roadNetworkGeoCenter.value) {
    // 如果没有路网中心，尝试使用城市中心
    if (cityOutlineGeoCenter.value) {
      focusOnCalculatedCenter()
    }
    return
  }

  // 使用更近的视距因子和更低的最小高度，提供更近的视角
  const focused = focusOnGeoPoint(
    roadNetworkGeoCenter.value,
    roadNetworkScale.value,
    COORDINATE_CONFIG.Y_LEVELS.ROADS,
    0.1, // 视距因子
    0, // 最小距离
    0 // 最小高度
  )

  if (focused) {
    // 已聚焦到路网中心
  }
}

// 修改：使用通用函数聚焦到城市中心
const focusOnCalculatedCenter = () => {
  if (!cityOutlineGeoCenter.value) return

  const focused = focusOnGeoPoint(
    cityOutlineGeoCenter.value,
    cityOutlineScale.value,
    COORDINATE_CONFIG.Y_LEVELS.CITY_OUTLINE,
    1.5, // 视距因子，城市轮廓需要更远视距
    50, // 最小距离
    30 // 最小高度
  )

  if (focused) {
    // 已聚焦到城市轮廓中心
  }
}

//绘制控制函数
const toggleDrawingMode = () => {
  if (!isDrawingMode.value) {
    // 当前非绘制模式，则进入
    isDrawingAreaMode.value = false

    // 保持相机可用
    isDrawingMode.value = true
  } else {
    // 当前是绘制模式，则退出
    cancelCurrentDrawing()
  }
}

// 区分点击和拖动的变量
let mouseDownTime = 0
let mouseDownPos = { x: 0, y: 0 }
let isDragging = false

// 处理场景点击事件，包括位置选择
const handleSceneClick = event => {
  // 只响应鼠标左键的点击
  if (event.button !== 0) {
    return
  }

  // 如果点击了弹出看板，不处理点击事件（防止点击看板时取消选择）
  if (event.target.closest('.device-popup-panel')) {
    return
  }

  // 检测是否是点击而非拖动
  const clickTime = new Date().getTime()
  const timeDiff = clickTime - mouseDownTime
  const distanceX = Math.abs(event.clientX - mouseDownPos.x)
  const distanceY = Math.abs(event.clientY - mouseDownPos.y)
  const totalDistance = Math.sqrt(distanceX * distanceX + distanceY * distanceY)

  // 如果时间太长或距离太远，认为是拖动而非点击，不执行选择逻辑
  if (timeDiff > 300 || totalDistance > 5 || isDragging) {
    isDragging = false // 重置拖动状态
    return
  }

  if (!sceneManager || !sceneManager.getCamera()) {
    return
  }
  const camera = sceneManager.getCamera()

  // 使用 ThreeCanvas 的 picking API 获取 NDC
  const { ndc } = pickingApi.value.getNDCFromDOM(event)

  // 配置 Line2 阈值（与原逻辑一致）
  // 注意：raycaster 由 ThreeCanvas 维护，这里仅在 pick 时传入阈值

  // 优先处理位置选择模式
  if (sceneStateStore.deviceForPlacement) {
    // 处理设备位置选择
    const ground = pickingApi.value.getGroundIntersectionFromNDC(ndc.x, ndc.y)
    if (ground) {
      const clickPoint = new THREE.Vector3(ground.x, ground.y, ground.z)

      // 将3D场景坐标转换为地理坐标（经纬度）
      // 使用专门的坐标转换工具函数进行精确转换
      const geoCoordinates = transformSceneToGeo(clickPoint.x, clickPoint.z)

      // 创建一个全新的对象，确保引用变化并格式化精度
      const coordinates = {
        lng: Number(geoCoordinates.lng.toFixed(6)),
        lat: Number(geoCoordinates.lat.toFixed(6)),
      }

      // 通过事件总线发送坐标
      eventBus.emit('location-selected', coordinates)

      // 选择完成后清除设备放置状态并退出模式
      sceneStateStore.setDeviceForPlacement(null)

      // 结束处理，不继续其他点击逻辑
      return
    }
  }

  // 优先处理模式相关的操作
  if (isDrawingMode.value) {
    console.log('道路绘制模式下的点击事件')
    const ground = pickingApi.value.getGroundIntersectionFromNDC(ndc.x, ndc.y)
    if (ground) {
      const intersectPoint = new THREE.Vector3(ground.x, ground.y, ground.z)
      currentDrawingPoints.value.push(intersectPoint.clone())

      // 更新预览线（显示已绘制的完整路径，不包含实时鼠标跟随）
      if (previewLineManager) {
        if (currentDrawingPoints.value.length === 1) {
          // 第一个点：启动预览线
          previewLineManager.startPreview(currentDrawingPoints.value, 'road')
        } else {
          // 后续点：重新启动预览线以显示完整路径
          previewLineManager.endPreview()
          previewLineManager.startPreview(currentDrawingPoints.value, 'road')
        }
      }

      // 转换为地理坐标用于日志显示
      const geoCoord = transformSceneToGeo(intersectPoint.x, intersectPoint.z)
      console.log('添加新的绘制点:', {
        场景坐标: {
          x: intersectPoint.x.toFixed(3),
          y: intersectPoint.y.toFixed(3),
          z: intersectPoint.z.toFixed(3),
        },
        地理坐标: {
          经度: geoCoord.lng.toFixed(6),
          纬度: geoCoord.lat.toFixed(6),
        },
        totalPoints: currentDrawingPoints.value.length,
      })
    } else {
      console.log('未检测到地面交点')
    }
    return // 结束，不进行后续选择操作
  }

  if (isDrawingAreaMode.value) {
    const ground = pickingApi.value.getGroundIntersectionFromNDC(ndc.x, ndc.y)
    if (ground) {
      const clickPoint = new THREE.Vector3(ground.x, ground.y, ground.z)
      currentDrawingAreaPoints.value.push(clickPoint)

      // 更新预览线（显示已绘制的完整路径，不包含实时鼠标跟随）
      if (previewLineManager) {
        if (currentDrawingAreaPoints.value.length === 1) {
          // 第一个点：启动预览线
          previewLineManager.startPreview(
            currentDrawingAreaPoints.value,
            'area'
          )
        } else {
          // 后续点：重新启动预览线以显示完整路径
          previewLineManager.endPreview()
          previewLineManager.startPreview(
            currentDrawingAreaPoints.value,
            'area'
          )
        }
      }

      // 转换为地理坐标用于日志显示
      const geoCoord = transformSceneToGeo(clickPoint.x, clickPoint.z)
      console.log('添加新的区域绘制点:', {
        场景坐标: {
          x: clickPoint.x.toFixed(3),
          y: clickPoint.y.toFixed(3),
          z: clickPoint.z.toFixed(3),
        },
        地理坐标: {
          经度: geoCoord.lng.toFixed(6),
          纬度: geoCoord.lat.toFixed(6),
        },
        totalPoints: currentDrawingAreaPoints.value.length,
      })
    }
    return // 结束
  }

  // 如果不是任何绘制/添加模式，则执行选择逻辑
  // 重构选中逻辑：基于射线与对象交点的距离来确定选中对象

  // 获取射线与所有可能对象的交点
  // 关闭递归检测(recursive=false)，防止子对象穿透检测问题
  // 并添加距离阈值筛选，只选择真正接近点击位置的对象
  // const _clickPrecision = 3.0 // 射线检测的最大距离，保留以优化性能

  // 对设备和POI使用递归检测，并应用点击半径过滤器
  // 使用 ThreeCanvas 的 picking，对设备与 POI 进行递归拾取
  const deviceIntersections = deviceMarkersGroup
    ? pickingApi.value.pick(deviceMarkersGroup, { ndc, recursive: true })
        .intersections
    : []
  const poiIntersections = userPointsGroup
    ? pickingApi.value.pick(userPointsGroup, { ndc, recursive: true })
        .intersections
    : []
  const intersectsDevices = filterByClickRadius(deviceIntersections)
  const intersectsPoi = filterByClickRadius(poiIntersections)

  // 路线检测（用于干线创建或编辑模式）
  const intersectsRoads = []
  if (isTrunkLineCreationMode.value || isTrunkLineEditingMode.value) {
    // 检测主路网组
    if (roadNetworkGroup && roadNetworkGroup.children.length > 0) {
      const roadIntersects = pickingApi.value.pick(roadNetworkGroup, {
        ndc,
        recursive: true,
        line2Threshold: 10,
      }).intersections
      roadIntersects.forEach(intersect => {
        if (
          intersect.object.userData &&
          (intersect.object.userData.featureId ||
            intersect.object.userData.featureName)
        ) {
          intersectsRoads.push({ ...intersect, type: 'road' })
        }
      })
    }

    // 检测动态路网组（不同距离下的路网）- 在干线创建模式下检测所有组
    if (dynamicRoadLoader && dynamicRoadLoader.renderedRoadGroups) {
      Object.entries(dynamicRoadLoader.renderedRoadGroups).forEach(
        ([level, group]) => {
          // 在干线创建模式下，检测所有有子对象的组，不管是否可见
          if (group && group.children.length > 0) {
            // 临时显示组以进行射线检测
            const originalVisible = group.visible
            if (!originalVisible) {
              group.visible = true
            }

            const dynamicRoadIntersects = pickingApi.value.pick(group, {
              ndc,
              recursive: true,
              line2Threshold: 10,
            }).intersections

            // 恢复原始可见性
            if (!originalVisible) {
              group.visible = false
            }

            dynamicRoadIntersects.forEach(intersect => {
              // 放宽检测条件，只要是 Line2 对象就认为是有效路线
              if (
                intersect.object.isLine2 ||
                intersect.object.type === 'Line2'
              ) {
                // 如果 userData 为空，尝试生成一个临时ID
                if (
                  !intersect.object.userData.featureId &&
                  !intersect.object.userData.featureName &&
                  !intersect.object.userData.id
                ) {
                  // 生成临时ID
                  const tempId = `temp_road_${Date.now()}_${Math.random()
                    .toString(36)
                    .substring(2, 11)}`
                  intersect.object.userData.tempId = tempId
                  // 为路线生成临时ID
                }

                // 添加路网级别信息用于调试
                intersect.roadLevel = level
                intersectsRoads.push({ ...intersect, type: 'road' })
                // 找到有效路线
              }
            })
          }
        }
      )
    }
  }

  // 将所有有效的交点合并到一个列表中，并按距离排序
  const allIntersects = [
    ...intersectsDevices.map(i => ({ ...i, type: 'device' })),
    ...intersectsPoi.map(i => ({ ...i, type: 'poi' })),
    ...intersectsRoads,
  ].sort((a, b) => a.distance - b.distance)

  // 根据有无交点来决定是选中还是清空
  if (allIntersects.length > 0) {
    const closestIntersect = allIntersects[0]
    const object = closestIntersect.object

    if (closestIntersect.type === 'poi') {
      // 向上查找带有feature的父对象
      let poiObject = object
      while (poiObject && !poiObject.userData.feature) {
        poiObject = poiObject.parent
      }
      if (poiObject && poiObject.userData.feature) {
        handlePointObjectSelection(poiObject)
      }
    } else if (closestIntersect.type === 'road') {
      // 处理路线选择（干线创建或编辑模式）
      if (isTrunkLineCreationMode.value || isTrunkLineEditingMode.value) {
        handleRoadSelection(closestIntersect.object)
      }
    }
    // 设备对象点击由DeviceInteractionHandler组件处理
  } else {
    // 没有交点，清除所有选中状态
    clearAllSelections()

    // 清除设备选中状态
    clearDeviceSelection()
  }
}

// POI对象选中处理函数 - 现在只处理POI，设备由DeviceInteractionHandler处理
const handlePointObjectSelection = object => {
  if (!object) {
    return
  }

  // 向上查找带有feature的父对象（通常是一个Group）
  let featureGroup = object
  while (featureGroup && !featureGroup.userData.feature) {
    featureGroup = featureGroup.parent
  }

  if (!featureGroup?.userData?.feature) return

  // 判断对象类型
  const isDevice = featureGroup.userData.feature.properties?.id // 设备对象通常有id属性

  if (isDevice) {
    return
  }

  // 处理POI对象
  // 选择POI

  const originalMaterials = new Map()
  featureGroup.traverse(node => {
    if (node.isMesh && node.material) {
      originalMaterials.set(node, node.material) // 保存原始材质
      const highlightMaterial = node.material.clone()
      highlightMaterial.color.set(SELECTION_HIGHLIGHT_COLOR)

      // 为标准材质添加自发光效果以增强高亮
      if (
        highlightMaterial.isMeshStandardMaterial ||
        highlightMaterial.isMeshPhysicalMaterial
      ) {
        highlightMaterial.emissive = SELECTION_HIGHLIGHT_COLOR
        highlightMaterial.emissiveIntensity = 0.7
      }
      node.material = highlightMaterial
    }
  })

  selectedPointObjectRef.value = {
    mesh: featureGroup,
    feature: featureGroup.userData.feature,
    originalMaterials: originalMaterials,
  }

  // 选中POI对象
}

// 清除所有选中状态的统一方法
const clearAllSelections = () => {
  // 清除点对象（POI/设备）的选中状态
  clearPointObjectSelection()

  // 清除设备选中状态
  selectedDeviceWorldPosition.value = null
}

// saveUserDrawnRoads 和 saveUserPolygons 现在由 useDataManager 提供

/**
 * 将用户绘制的区域保存到服务器
 * @param {Object} areaFeature - GeoJSON区域特征
 */
const saveAreaToServer = async areaFeature => {
  try {
    // 将3D场景坐标转换为地理坐标（经纬度）
    const convertSceneToGeoCoordinates = coordinates => {
      if (areaFeature.geometry.type === 'Polygon') {
        // Polygon格式: [[[x,y,z], [x,y,z], ...]]
        return coordinates.map(ring =>
          ring.map(point => {
            const geoCoord = transformSceneToGeo(point[0], point[2])
            return [geoCoord.lng, geoCoord.lat] // 只返回经纬度，不包含高度
          })
        )
      }
      return coordinates
    }

    // 转换为API所需的格式
    const regionData = {
      region_name: areaFeature.properties.name,
      classification: areaFeature.properties.classification || 'A', // 默认分类
      region: {
        type: areaFeature.geometry.type,
        coordinates: convertSceneToGeoCoordinates(
          areaFeature.geometry.coordinates
        ),
      },
    }

    // 调用区域Store创建区域
    const createdRegion = await regionsStore.createNewRegion(regionData)
    // 区域已保存到服务器

    return createdRegion
  } catch (error) {
    message.error(`保存区域到服务器失败: ${error.message}`)
    throw error
  }
}

// 完成当前道路绘制
const finalizeCurrentRoad = async () => {
  if (currentDrawingPoints.value.length < 2) {
    return
  }

  // 结束预览线
  if (previewLineManager) {
    previewLineManager.endPreview()
  }

  // 1. 将场景坐标转换为地理坐标（经纬度），确保是二维数组[经度,纬度]
  const geoCoordinates = currentDrawingPoints.value.map(p => {
    const geoCoord = transformSceneToGeo(p.x, p.z)
    // 只返回经度和纬度，不包含Y坐标，确保与原始路网数据格式一致
    return [geoCoord.lng, geoCoord.lat]
  })

  // 2. 创建符合GeoJSON标准的Feature
  const roadTimestamp = Date.now()
  const newRoadFeature = {
    type: 'Feature',
    geometry: {
      type: 'LineString',
      coordinates: geoCoordinates,
    },
    properties: {
      '@id': `way/${roadTimestamp}`,
      highway: selectedRoadLevel.value,
      name: currentRoadName.value || `未命名道路 ${roadTimestamp}`,
      source: 'user-drawn',
    },
    id: `way/${roadTimestamp}`,
  }

  // 3. 询问用户是否要将道路添加到现有路网
  const addToExistingNetwork = confirm('是否将此道路添加到已有路网中？')

  if (addToExistingNetwork) {
    // 3.1 直接使用默认路网ID 1
    const networkId = 1
    try {
      // 3.2 从IndexedDB或服务器获取路网数据
      let roadNetworkData = null

      // 先尝试从IndexedDB获取（使用 useDataManager 提供的方法）
      try {
        roadNetworkData = await getFromIndexedDB(ROAD_DATA_STORE, 'default')
        if (roadNetworkData) {
          // 从IndexedDB获取了路网数据
        }
      } catch (error) {
        // 从IndexedDB获取路网数据失败，继续从API获取
      }

      // 如果IndexedDB中没有，则从API获取
      if (!roadNetworkData) {
        const API_BASE_URL = getBaseUrl('geoData')
        const response = await axios.get(
          `${API_BASE_URL}/road_networks/${networkId}`
        )

        if (response.data && response.data.road_network) {
          roadNetworkData = response.data.road_network
        } else if (
          response.data &&
          response.data.type === 'FeatureCollection'
        ) {
          roadNetworkData = response.data
        } else if (response.data && typeof response.data === 'object') {
          roadNetworkData = response.data
        }
      }

      // 3.3 将新道路添加到路网中
      let roadNetworkGeoJson = roadNetworkData

      if (
        roadNetworkData &&
        roadNetworkData.data &&
        roadNetworkData.data.type === 'FeatureCollection'
      ) {
        roadNetworkGeoJson = roadNetworkData.data
      }

      // 添加新道路到路网
      if (
        !roadNetworkGeoJson ||
        roadNetworkGeoJson.type !== 'FeatureCollection'
      ) {
        roadNetworkGeoJson = {
          type: 'FeatureCollection',
          features: [],
        }

        if (roadNetworkData && roadNetworkData.data) {
          roadNetworkData.data = roadNetworkGeoJson
        } else {
          roadNetworkData = roadNetworkGeoJson
        }
      }

      roadNetworkGeoJson.features.push(newRoadFeature)

      if (roadNetworkData && roadNetworkData.data) {
        roadNetworkData.data = roadNetworkGeoJson
      } else {
        roadNetworkData = roadNetworkGeoJson
      }

      // 3.4 上传更新后的路网到服务器
      const API_BASE_URL = getBaseUrl('geoData')

      // 调试日志：记录道路创建功能发送的数据结构
      console.log('创建功能发送的数据结构:', {
        network_id: networkId,
        road_network_type: typeof roadNetworkData,
        road_network_has_data: !!(roadNetworkData && roadNetworkData.data),
        road_network_features_count: roadNetworkGeoJson.features.length,
        road_network_structure: roadNetworkData,
      })

      await axios.post(`${API_BASE_URL}/road_networks`, {
        network_id: networkId,
        road_network: roadNetworkData,
      })

      // 3.5 更新IndexedDB中的路网数据（使用 useDataManager 提供的方法）
      try {
        await saveToIndexedDB(ROAD_DATA_STORE, 'default', roadNetworkData)
        // 路网数据已更新到IndexedDB
      } catch (error) {
        // 更新IndexedDB中的路网数据失败，不影响功能
      }

      // 3.6 更新当前显示的路网
      if (
        roadNetworkData &&
        roadNetworkData.data &&
        roadNetworkData.data.type === 'FeatureCollection'
      ) {
        jinanRoadNetworkGeoJson.value = roadNetworkData.data
      } else if (
        roadNetworkData &&
        roadNetworkData.type === 'FeatureCollection'
      ) {
        jinanRoadNetworkGeoJson.value = roadNetworkData
      }

      // 3.7 重新获取服务器路网数据并刷新显示
      try {
        // 重新获取路网数据
        const refreshResponse = await axios.get(
          `${API_BASE_URL}/road_networks/${networkId}`
        )

        let refreshedRoadNetworkData = null
        if (refreshResponse.data && refreshResponse.data.road_network) {
          refreshedRoadNetworkData = refreshResponse.data.road_network
        } else if (
          refreshResponse.data &&
          refreshResponse.data.type === 'FeatureCollection'
        ) {
          refreshedRoadNetworkData = refreshResponse.data
        } else if (
          refreshResponse.data &&
          typeof refreshResponse.data === 'object'
        ) {
          refreshedRoadNetworkData = refreshResponse.data
        }

        if (refreshedRoadNetworkData) {
          // 更新路网数据
          if (
            refreshedRoadNetworkData.data &&
            refreshedRoadNetworkData.data.type === 'FeatureCollection'
          ) {
            jinanRoadNetworkGeoJson.value = refreshedRoadNetworkData.data
          } else if (refreshedRoadNetworkData.type === 'FeatureCollection') {
            jinanRoadNetworkGeoJson.value = refreshedRoadNetworkData
          }

          // 更新DynamicRoadLoader
          if (dynamicRoadLoader) {
            dynamicRoadLoader.updateRoadDataSource(refreshedRoadNetworkData)
            const camera = sceneManager?.getCamera()
            if (camera) {
              // 根据当前显示模式刷新路网
              if (roadDisplayMode.value === 'auto') {
                dynamicRoadLoader.updateRoadsByDistance(
                  displayedCameraDistance.value,
                  camera
                )

                // 在路网更新后恢复交通指标颜色
                nextTick(() => {
                  updateAllColors()
                })
              } else {
                setRoadDisplayMode(roadDisplayMode.value)
              }

              // 使用路名管理器渲染路名
              if (
                smartRoadLabelManager &&
                roadLabelsGroup &&
                refreshedRoadNetworkData
              ) {
                const currentRoadLevel = getCurrentRoadLevel(
                  displayedCameraDistance.value
                )
                smartRoadLabelManager.renderLabels(
                  refreshedRoadNetworkData,
                  roadNetworkScale.value,
                  roadLabelsGroup,
                  COORDINATE_CONFIG.Y_LEVELS.ROADS,
                  camera,
                  currentRoadLevel
                )
              }
            }
          }
        }
      } catch (refreshError) {
        // 刷新路网数据失败，不影响原有功能
      }

      alert(`道路已成功添加到路网ID: ${networkId} 并上传到服务器`)
      cancelCurrentDrawing()
      return
    } catch (error) {
      alert(`添加道路到路网失败: ${error.message || '未知错误'}`)
    }
  }

  // 4. 如果用户选择不添加到现有路网，或者添加失败，则添加到用户绘制的道路中
  userDrawnRoadsGeoJson.value.features.push(newRoadFeature)
  // 道路已添加到用户绘制道路数据中

  // 5. 保存数据到IndexedDB
  try {
    await saveUserDrawnRoads()
  } catch (saveError) {
    console.error('保存用户绘制道路数据失败:', saveError)
    message.error('保存道路数据失败，但道路已添加到地图中')
  }

  // 6. 为了渲染需要，创建一个场景坐标版本的Feature用于显示
  // 保存场景坐标的副本，仅用于渲染
  const sceneCoordinates = currentDrawingPoints.value.map(p => [p.x, p.y, p.z])
  const sceneFeatureForRender = JSON.parse(JSON.stringify(newRoadFeature))
  sceneFeatureForRender.geometry.coordinates = sceneCoordinates

  // 7. 渲染道路
  const renderedLine = directRenderRoad(sceneFeatureForRender)
  if (!renderedLine) {
    renderFinalizedRoad(sceneFeatureForRender)
  }

  // 8. 清理绘制状态
  cancelCurrentDrawing()
}

// 渲染单条已完成的道路
const renderFinalizedRoad = roadFeature => {
  if (
    !geoJsonRenderer ||
    !finalizedUserRoadsGroup ||
    !userDrawnRoadLabelsGroup ||
    !roadFeature?.geometry ||
    roadFeature.geometry.type !== 'LineString'
  ) {
    return
  }
  geoJsonRenderer.renderFinalizedRoad(
    roadFeature, // 道路特征
    finalizedUserRoadsGroup, // 道路组
    userDrawnRoadLabelsGroup, // 道路标签组
    getMaterialForUserDrawnRoad, // 获取材质回调
    COORDINATE_CONFIG.Y_LEVELS.ROADS, // roadYLevel
    0.01, // labelFontSize - 标签字体大小
    0x1a2b3d // labelColorHex - 更暗的标签颜色
  )
}

const cancelCurrentDrawing = () => {
  // 1. 结束预览线
  if (previewLineManager) {
    previewLineManager.endPreview()
  }

  // 2. 重置状态变量
  currentDrawingPoints.value = []
  currentRoadName.value = ''

  // 4. 确保退出绘制模式
  isDrawingMode.value = false
}

// 道路等级选择相关
const roadLevelOptions = ref([
  { value: 'primary', text: '主干道' },
  { value: 'secondary', text: '次干道' },
  { value: 'tertiary', text: '支路' },
  { value: 'trunk', text: '快速路' },
  { value: 'motorway', text: '高速公路' },
  { value: 'custom', text: '自定义' },
])
const selectedRoadLevel = ref(roadLevelOptions.value[0].value) // 默认选择第一个等级

const getMaterialForUserDrawnRoad = level => {
  // 直接调用 getMaterialForRoadType 来复用 OSM 道路的样式逻辑
  const renderer = sceneManager?.getRenderer() // 获取 renderer
  const material = getMaterialForRoadType(level, null)
  if (!material) {
    if (!renderer) {
      return null
    }
    const rendererSize = new THREE.Vector2()
    renderer.getSize(rendererSize)
    return new LineMaterial({
      // 极简回退材质
      color: 0xff00ff, // 紫色以示区别
      linewidth: 0.5, // 增加线宽使其更容易看到
      resolution: rendererSize,
      alphaToCoverage: true, // 确保抗锯齿
      dashed: false,
    })
  }

  return material // 直接返回原始material，与路网保持完全一致
}

// 用于处理从 DataManager 传入的单个 GeoJSON Feature
const handleAddGeoJsonFeature = rawFeature => {
  if (
    !rawFeature?.geometry ||
    rawFeature.geometry.type !== 'LineString' ||
    !rawFeature.geometry.coordinates
  ) {
    alert('提供的GeoJSON数据无效，必须是LineString类型的Feature。')
    return
  }

  // 1. 坐标转换 (地理坐标 -> 场景3D坐标)
  const geographicCoords = rawFeature.geometry.coordinates
  if (
    !Array.isArray(geographicCoords) ||
    geographicCoords.length < 2 ||
    !geographicCoords.every(c => Array.isArray(c) && c.length >= 2)
  ) {
    alert('GeoJSON坐标格式无效，应为经纬度点数组。')
    return
  }

  let sceneCoords3D
  try {
    // transformGeoJsonCoordinates 期望 [[lon,lat], [lon,lat]]
    // 并返回 [[sceneX, sceneZ], [sceneX, sceneZ]]
    const transformedFlatCoords = transformGeoJsonCoordinates(
      geographicCoords,
      0,
      roadNetworkScale.value
    )
    sceneCoords3D = transformedFlatCoords.map(coord => [
      coord[0],
      COORDINATE_CONFIG.Y_LEVELS.ROADS,
      coord[1],
    ])
  } catch (e) {
    alert('坐标转换失败，请检查GeoJSON中的坐标数据。')
    return
  }

  // 2. 创建用于存储和渲染的 Feature 对象 (深拷贝以避免修改原始传入对象)
  const featureForScene = JSON.parse(JSON.stringify(rawFeature))
  featureForScene.geometry.coordinates = sceneCoords3D

  // 3. 属性处理 (确保必要属性存在)
  if (!featureForScene.properties) {
    featureForScene.properties = {}
  }
  // 如果 DataManager 未提供ID，则生成一个
  if (!featureForScene.properties.id) {
    featureForScene.properties.id = `imported-road-${Date.now()}`
  }
  // 如果 DataManager 未提供名称，则使用默认名称
  if (!featureForScene.properties.name) {
    featureForScene.properties.name = `导入道路-${featureForScene.properties.id}`
  }
  // 设置或覆盖 source 和 timestamp
  featureForScene.properties.source =
    rawFeature.properties.source || 'geojson-individual' // DataManager可指定更具体的source
  featureForScene.properties.timestamp = Date.now()

  // 4. 添加到数据源
  userDrawnRoadsGeoJson.value.features.push(featureForScene)

  // 5. 渲染到场景
  renderFinalizedRoad(featureForScene)

  // 6. 更新数据存储并确保页面立即更新
  saveUserDrawnRoads()
}

// 注意：设备添加功能现在由设备控制面板处理

// 切换绘制区域模式的方法
const toggleDrawAreaMode = () => {
  isDrawingAreaMode.value = !isDrawingAreaMode.value
  if (isDrawingAreaMode.value) {
    // 当前非绘制模式，则进入
    isDrawingMode.value = false // 退出道路绘制
    currentDrawingAreaPoints.value = [] // 开始新的区域绘制时清空之前的点

    // 结束道路预览线（如果存在）
    if (previewLineManager) {
      previewLineManager.endPreview()
    }
  } else {
    // 退出模式时清理状态
    currentDrawingAreaPoints.value = [] // 清空点

    // 结束预览线
    if (previewLineManager) {
      previewLineManager.endPreview()
    }
  }
}
// 绘制区域相关状态
const isDrawingAreaMode = ref(false)
const currentDrawingAreaPoints = ref([]) // 存储当前正在绘制的区域的点 (场景坐标)

// 取消当前区域绘制的方法
const cancelCurrentAreaDrawing = () => {
  // 结束预览线
  if (previewLineManager) {
    previewLineManager.endPreview()
  }

  currentDrawingAreaPoints.value = []
  selectedAreaNameForDrawing.value = '' // 清空区域名称输入
  selectedAreaClassification.value = 'A' // 重置区域分类为默认值
}

// 完成当前区域绘制的方法
const finalizeCurrentArea = async () => {
  if (currentDrawingAreaPoints.value.length < 3) {
    message.error(
      `无法完成区域：至少需要三个顶点。目前顶点数: ${currentDrawingAreaPoints.value.length}`
    )
    return
  }

  // 结束预览线
  if (previewLineManager) {
    previewLineManager.endPreview()
  }

  // 检查区域名称是否已设置
  if (!selectedAreaNameForDrawing.value.trim()) {
    message.warning('请先在区域管理面板中设置区域名称')
    return
  }

  // 使用区域管理面板中设置的参数
  const areaName = selectedAreaNameForDrawing.value.trim()
  const areaDescription = '' // 暂时不支持描述，可以后续添加
  const areaClassification = selectedAreaClassification.value || 'A'

  // 转换顶点为 GeoJSON Polygon 坐标格式 (场景坐标)
  // GeoJSON Polygon coordinates: [[ [x,y,z], [x,y,z], ... ]] (一个外部环)
  const polygonSceneCoordinates = currentDrawingAreaPoints.value.map(p => [
    p.x,
    p.y,
    p.z,
  ])

  // 确保多边形闭合：第一个点和最后一个点相同
  if (polygonSceneCoordinates.length > 0) {
    const firstPoint = polygonSceneCoordinates[0]
    const lastPoint =
      polygonSceneCoordinates[polygonSceneCoordinates.length - 1]
    if (
      firstPoint[0] !== lastPoint[0] ||
      firstPoint[1] !== lastPoint[1] ||
      firstPoint[2] !== lastPoint[2]
    ) {
      polygonSceneCoordinates.push([...firstPoint]) // 添加第一个点的副本到末尾
    }
  }

  const newAreaFeature = {
    type: 'Feature',
    properties: {
      id: `user-area-${Date.now()}`,
      name: areaName,
      description: areaDescription || '',
      classification: areaClassification,
      source: 'user-drawn-area',
      timestamp: Date.now(),
    },
    geometry: {
      type: 'Polygon',
      coordinates: [polygonSceneCoordinates], // 单个环
    },
  }

  // 添加到本地数据
  userPolygonsGeoJson.value.features.push(newAreaFeature)

  // 保存到本地存储
  await saveUserPolygons()

  // 尝试保存到服务器
  try {
    await saveAreaToServer(newAreaFeature)
  } catch (error) {
    // 服务器保存失败，但本地已保存
    message.warning(`区域 "${areaName}" 已保存到本地，但服务器同步失败`)
  }

  // 清理当前绘制状态
  currentDrawingAreaPoints.value = []
  selectedAreaNameForDrawing.value = '' // 清空区域名称输入
  selectedAreaClassification.value = 'A' // 重置区域分类为默认值
  // 用户应手动点击"结束绘制区域"来退出模式
}

// 清除点对象选择和高亮的方法
const clearPointObjectSelection = () => {
  if (!selectedPointObjectRef.value) {
    return
  }

  const { mesh, originalMaterials } = selectedPointObjectRef.value

  // 恢复原始材质，并清理高亮材质
  mesh.traverse(node => {
    if (node.isMesh && originalMaterials.has(node)) {
      if (node.material) {
        node.material.dispose() // 清理高亮材质
      }
      node.material = originalMaterials.get(node) // 恢复原始材质
    }
  })

  // 清除设备选中状态
  selectedDeviceWorldPosition.value = null

  // 重置选中状态
  selectedPointObjectRef.value = null
}

// 监视用户绘制的道路，同样根据摄像机距离控制可见性
watchEffect(() => {
  if (
    !isThreeInitialized.value ||
    !finalizedUserRoadsGroup ||
    !userDrawnRoadLabelsGroup
  ) {
    return
  }

  // 根据路网显示模式和相机距离来判断用户道路可见性
  let userRoadsVisible = true
  let shouldShowPrimary = false
  let shouldShowSecondary = false
  let shouldShowTertiary = false

  // 使用与路网相同的显示逻辑
  if (roadDisplayMode.value === 'auto') {
    const distance = displayedCameraDistance.value
    const thresholds = roadDistanceThresholds.value

    if (distance > thresholds.hide) {
      userRoadsVisible = false
    } else if (distance > thresholds.primary) {
      shouldShowPrimary = true
    } else if (distance > thresholds.secondary) {
      shouldShowPrimary = true
      shouldShowSecondary = true
    } else {
      shouldShowPrimary = true
      shouldShowSecondary = true
      shouldShowTertiary = true
    }
  } else if (roadDisplayMode.value === 'primary') {
    shouldShowPrimary = true
  } else if (roadDisplayMode.value === 'primary_secondary') {
    shouldShowPrimary = true
    shouldShowSecondary = true
  } else if (roadDisplayMode.value === 'all') {
    shouldShowPrimary = true
    shouldShowSecondary = true
    shouldShowTertiary = true
  } else {
    userRoadsVisible = false
  }
  // 控制所有用户绘制道路的可见性
  finalizedUserRoadsGroup.visible = userRoadsVisible
  // 用户绘制道路标签的可见性：只要道路可见且摄像机距离合适就显示标签
  userDrawnRoadLabelsGroup.visible =
    userRoadsVisible && displayedCameraDistance.value < 5.0

  // 如果用户道路为空但有数据，尝试重新渲染
  if (
    userRoadsVisible &&
    userDrawnRoadsGeoJson.value?.features?.length > 0 &&
    finalizedUserRoadsGroup.children.length === 0
  ) {
    renderUserRoadsFromGeoJson()
  }

  // 根据道路等级应用过滤可见性
  if (userRoadsVisible && finalizedUserRoadsGroup.children.length > 0) {
    finalizedUserRoadsGroup.children.forEach(road => {
      const roadLevel =
        road.userData?.originalHighwayType ||
        road.userData?.feature?.properties?.level ||
        'tertiary'

      // 根据等级设置可见性
      if (roadLevel === 'primary') {
        road.visible = shouldShowPrimary
      } else if (roadLevel === 'secondary') {
        road.visible = shouldShowSecondary
      } else {
        road.visible = shouldShowTertiary // tertiary和其他等级
      }

      // 找到对应的标签并设置相同的可见性
      if (userDrawnRoadLabelsGroup) {
        const roadName = road.userData?.featureName
        const roadId = road.userData?.feature?.properties?.id

        userDrawnRoadLabelsGroup.children.forEach(label => {
          if (
            (roadId && label.userData?.featureId === roadId) ||
            (roadName && label.userData?.featureName === roadName)
          ) {
            label.visible = road.visible
          }
        })
      }
    })
  }
})

// 监视设备/POI的显示，与主干道显示隐藏绑定
let lastDeviceVisibilityState = null
watchEffect(() => {
  if (!isThreeInitialized.value || !deviceMarkersGroup) {
    return
  }

  // 根据路网显示模式判断设备是否应该显示
  let shouldShowDevices = true

  if (roadDisplayMode.value === 'auto') {
    const distance = displayedCameraDistance.value
    const thresholds = roadDistanceThresholds.value

    // 当路网隐藏时，设备也隐藏
    if (distance > thresholds.hide) {
      shouldShowDevices = false
    }
  } else if (roadDisplayMode.value === 'hide') {
    shouldShowDevices = false
  }

  const finalVisibility = shouldShowDevices && isDeviceVisible.value

  // 只在可见性真正改变时才更新，避免不必要的重绘
  // 但在聚合模式下，设备图标的可见性由聚合逻辑控制
  if (
    lastDeviceVisibilityState !== finalVisibility &&
    !deviceClusterSystem.isClusterMode.value
  ) {
    deviceMarkersGroup.visible = finalVisibility
    lastDeviceVisibilityState = finalVisibility
  }
})

// 单独监视设备数据变化，避免频繁重新渲染
watchEffect(() => {
  if (!isThreeInitialized.value || !deviceMarkersGroup) {
    return
  }

  if (apiDeviceGeoJson.value) {
    processDeviceLocations(apiDeviceGeoJson.value)

    // 设备数据变化后，如果处于聚合模式，需要更新聚合
    if (deviceClusterSystem.isClusterMode.value) {
      nextTick(() => {
        updateDeviceClustering()
      })
    }
  }
})

// 监视用户点位的显示，与主干道显示隐藏绑定
let lastUserPointsVisibilityState = null
watchEffect(() => {
  if (!isThreeInitialized.value || !userPointsGroup) {
    return
  }

  // 根据路网显示模式判断用户点位是否应该显示
  let shouldShowUserPoints = true

  if (roadDisplayMode.value === 'auto') {
    const distance = displayedCameraDistance.value
    const thresholds = roadDistanceThresholds.value

    // 当路网隐藏时，用户点位也隐藏
    if (distance > thresholds.hide) {
      shouldShowUserPoints = false
    }
  } else if (roadDisplayMode.value === 'hide') {
    shouldShowUserPoints = false
  }

  const finalVisibility = shouldShowUserPoints && isDeviceVisible.value

  // 只在可见性真正改变时才更新，避免不必要的重绘
  if (lastUserPointsVisibilityState !== finalVisibility) {
    userPointsGroup.visible = finalVisibility
    lastUserPointsVisibilityState = finalVisibility
  }
})

// 单独监视用户点位数据变化
watchEffect(() => {
  // 监视用户点位数据并处理渲染
  if (
    isThreeInitialized.value &&
    geoJsonRenderer &&
    userPointsGroup &&
    userPointsGeoJson.value
  ) {
    geoJsonRenderer.processUserPoints(
      userPointsGeoJson.value.features,
      userPointsGroup,
      {
        color: 0x48bb78, // 原为0x00ff00绿色，改为柔和的绿色
        size: 0.05,
        showLabels: isDeviceLabelVisible.value, // 根据距离控制标签可见性
        labelColor: 0x4a5568, // 深灰色标签
        labelFontSize: 0.08,
        labelYOffset: 0.25, // 标签 Y 轴偏移
      }
    )
  }
})

// 监听配置变化，强制重新渲染区域，与主干道显示隐藏绑定
watch(
  [
    isThreeInitialized,
    userPolygonsGeoJson,
    isAreaVisible,
    isAreaLabelVisible,
    roadDisplayMode,
    displayedCameraDistance,
  ],
  ([initialized, geoJson, areaVisible, labelVisible]) => {
    if (!initialized || !geoJsonRenderer || !userPolygonsGroup) {
      return
    }

    // 根据路网显示模式判断用户绘制区域是否应该显示
    let shouldShowUserAreas = true

    if (roadDisplayMode.value === 'auto') {
      const distance = displayedCameraDistance.value
      const thresholds = roadDistanceThresholds.value

      // 当路网隐藏时，用户绘制区域也隐藏
      if (distance > thresholds.hide) {
        shouldShowUserAreas = false
      }
    } else if (roadDisplayMode.value === 'hide') {
      shouldShowUserAreas = false
    }

    // 控制区域组的可见性
    userPolygonsGroup.visible = shouldShowUserAreas && areaVisible

    if (!geoJson) {
      return
    }

    geoJsonRenderer.processUserPolygons(geoJson.features, userPolygonsGroup, {
      outlineColor: 0x4fd1c5,
      outlineLinewidth: 2,
      fillColor: null, // 不设置静态颜色，由交通指标API动态控制
      yLevelOffset: COORDINATE_CONFIG.Y_LEVELS.AREAS,
      showLabels: labelVisible,
      labelColor: 0x4fd1c5,
      labelFontSize: 1.0,
      labelYOffset: COORDINATE_CONFIG.Y_LEVELS.AREA_LABELS,
    })

    // 在下一帧应用初始缩放
    nextTick(() => {
      const camera = sceneManager?.getCamera()
      if (camera) {
        updateUserAreaLabelsScale(userPolygonsGroup, camera)
      }
    })
  },
  { immediate: true }
)

// 强制刷新区域渲染的方法
const refreshAreaRendering = () => {
  if (isThreeInitialized.value && geoJsonRenderer && userPolygonsGroup) {
    // 清理现有区域
    geoJsonRenderer.clearGroup(userPolygonsGroup)

    // 重新渲染
    if (userPolygonsGeoJson.value?.features?.length > 0) {
      geoJsonRenderer.processUserPolygons(
        userPolygonsGeoJson.value.features,
        userPolygonsGroup,
        {
          outlineColor: 0x4fd1c5,
          outlineLinewidth: 2,
          fillColor: null, // 不设置静态颜色，由交通指标API动态控制
          yLevelOffset: COORDINATE_CONFIG.Y_LEVELS.AREAS,
          showLabels: isAreaLabelVisible.value,
          labelColor: 0x4fd1c5,
          labelFontSize: 1.0,
          labelYOffset: COORDINATE_CONFIG.Y_LEVELS.AREA_LABELS,
        }
      )
    }
  }
}

// 在开发环境下暴露到全局，方便调试
if (import.meta.env.DEV) {
  window.refreshAreaRendering = refreshAreaRendering
}

// 优化：使用watch替代watchEffect，减少干线渲染频率
watch(
  [isThreeInitialized, trunkLines],
  ([initialized, lines]) => {
    if (!initialized || !trunkLineRenderer || !lines) {
      return
    }

    // 清除所有现有干线渲染
    trunkLineRenderer.clearAllTrunkLines()

    // 渲染所有干线
    if (lines.length > 0) {
      trunkLineRenderer.renderTrunkLines(lines)
    }
  },
  { immediate: true, deep: true } // 需要深度监听以检测数组变化
)

//监视路网显示状态，按等级控制干线可见性
watchEffect(() => {
  if (!isThreeInitialized.value || !trunkLineRenderer || !trunkLines.value) {
    return
  }

  // 根据路网显示逻辑判断不同等级干线的显示
  let shouldShowPrimary = false
  let shouldShowSecondary = false
  let shouldShowTertiary = false

  if (roadDisplayMode.value === 'auto') {
    const distance = displayedCameraDistance.value
    const thresholds = roadDistanceThresholds.value

    // 根据距离阈值分级显示干线
    if (distance <= thresholds.hide) {
      if (distance > thresholds.primary) {
        // 只显示主要干线
        shouldShowPrimary = true
      } else if (distance > thresholds.secondary) {
        // 显示主要和次要干线
        shouldShowPrimary = true
        shouldShowSecondary = true
      } else {
        // 显示所有等级干线
        shouldShowPrimary = true
        shouldShowSecondary = true
        shouldShowTertiary = true
      }
    }
  } else if (roadDisplayMode.value === 'primary') {
    shouldShowPrimary = true
  } else if (roadDisplayMode.value === 'primarySecondary') {
    shouldShowPrimary = true
    shouldShowSecondary = true
  } else if (roadDisplayMode.value === 'all') {
    shouldShowPrimary = true
    shouldShowSecondary = true
    shouldShowTertiary = true
  }
  // roadDisplayMode.value === 'hide' 时，所有干线都不显示

  // 简化干线可见性控制：根据路网显示模式控制整体干线可见性
  const shouldShowAnyTrunkLines =
    shouldShowPrimary || shouldShowSecondary || shouldShowTertiary

  // 控制所有干线的整体可见性
  trunkLineRenderer.setAllTrunkLinesVisibility(shouldShowAnyTrunkLines)
})

// 使用 IndexedDB 加载路网显示模式
const getSavedRoadDisplayMode = async () => {
  const saved = await getFromIndexedDB('settings', 'roadDisplayMode')
  return saved || 'auto' // 默认为 auto
}

// 距离阈值使用 IndexedDB
const getSavedDistanceThresholds = async () => {
  const saved = await getFromIndexedDB('settings', 'roadDistanceThresholds')
  return saved || DEFAULT_THRESHOLDS
}

// 默认使用固定值，在onMounted中异步加载保存的值（已在前面定义）

// 统一的阈值应用逻辑
const updateThresholds = async (newThresholds, successMessage) => {
  roadDistanceThresholds.value = newThresholds

  try {
    await saveToIndexedDB('settings', 'roadDistanceThresholds', newThresholds)
    message.success(successMessage)
  } catch (error) {
    message.error('保存失败')
    return
  }

  // 应用到渲染器
  if (dynamicRoadLoader) {
    dynamicRoadLoader.setDistanceThresholds(newThresholds)
    const camera = sceneManager?.getCamera()
    if (camera) {
      setRoadDisplayMode(roadDisplayMode.value)
    }
  }
}

// 应用新的距离阈值设置
const applyDistanceThresholds = async thresholds => {
  const newThresholds = Object.keys(DEFAULT_THRESHOLDS).reduce((acc, key) => {
    acc[key] = parseFloat(thresholds[key]) || DEFAULT_THRESHOLDS[key]
    return acc
  }, {})
  await updateThresholds(newThresholds, '距离阈值设置已保存')
}

// 重置距离阈值为默认值
const resetDistanceThresholds = async () => {
  await updateThresholds({ ...DEFAULT_THRESHOLDS }, '已重置为默认阈值设置')
}

const lastProcessedDistance = ref(0) // 记录上次处理道路更新的距离
const lastLabelScaleDistance = ref(0) // 记录上次处理标签缩放的距离
const distanceUpdateThreshold = ref(0.5) // 距离变化超过0.5个单位才处理道路更新

// 检查是否跨越了任何道路显示阈值
const checkRoadThresholdsCrossed = (oldDist, newDist) => {
  const thresholds = Object.values(roadDistanceThresholds.value)
  return thresholds.some(
    threshold =>
      (oldDist > threshold && newDist <= threshold) ||
      (oldDist <= threshold && newDist > threshold)
  )
}

// 根据路网显示模式更新道路
const setRoadDisplayMode = async mode => {
  roadDisplayMode.value = mode
  await saveToIndexedDB('settings', 'roadDisplayMode', mode)

  if (!dynamicRoadLoader || !sceneManager?.getCamera()) return

  const camera = sceneManager.getCamera()
  let effectiveDistance

  const thresholds = roadDistanceThresholds.value

  if (
    typeof thresholds.hide !== 'number' ||
    typeof thresholds.primary !== 'number' ||
    typeof thresholds.secondary !== 'number'
  )
    return

  switch (mode) {
    case 'auto':
      effectiveDistance = displayedCameraDistance.value
      break
    case 'primary':
      effectiveDistance = (thresholds.hide + thresholds.primary) / 2
      break
    case 'primary_secondary':
      effectiveDistance = (thresholds.primary + thresholds.secondary) / 2
      break
    case 'all':
      // 显示所有道路
      effectiveDistance = 0.1
      break
    default:
      effectiveDistance = thresholds.hide + 1
  }

  if (isNaN(effectiveDistance)) return

  // 强制重置当前加载级别，以确保更新
  dynamicRoadLoader.currentLoadedLevel = null
  // 统一调用核心更新函数
  dynamicRoadLoader.updateRoadsByDistance(effectiveDistance, camera)

  // 在路网更新后恢复交通指标颜色
  nextTick(() => {
    updateAllColors()
  })

  // 重新处理区域标签，确保在显示模式更改时应用正确的标签可见性
  processDistrictLabels()
}

// 添加watchEffect，在道路显示模式变化时，强制重新加载
watchEffect(() => {
  // 监听roadDisplayMode变化
  const currentMode = roadDisplayMode.value

  // 如果已经初始化了动态加载器，且模式为auto，则重新更新路网
  if (dynamicRoadLoader && isThreeInitialized.value && currentMode === 'auto') {
    // 重置当前加载级别，强制刷新
    dynamicRoadLoader.currentLoadedLevel = null

    // 更新路网显示
    const camera = sceneManager?.getCamera()
    if (camera && isDeviceVisible.value) {
      dynamicRoadLoader.updateRoadsByDistance(
        displayedCameraDistance.value,
        camera
      )

      // 在路网更新后恢复交通指标颜色
      nextTick(() => {
        updateAllColors()
      })
    }
  }
})

// 直接渲染道路的方法，不依赖GeoJsonRenderer
const directRenderRoad = roadFeature => {
  if (
    !roadFeature?.geometry?.coordinates ||
    roadFeature.geometry.coordinates.length < 2
  ) {
    return null
  }

  // 1. 准备数据
  const name = roadFeature.properties?.name || '未命名道路'
  const highway =
    roadFeature.properties?.highway ||
    roadFeature.properties?.level ||
    'tertiary'
  const construction = roadFeature.properties?.construction
  const coords = roadFeature.geometry.coordinates

  // 2. 获取材质
  const material = getMaterialForRoadType(highway, construction)
  if (!material) {
    return null
  }

  // 3. 创建位置数组
  const positions = []
  coords.forEach(coord => {
    const x = coord[0]
    const y = coord.length >= 3 ? coord[1] : COORDINATE_CONFIG.Y_LEVELS.ROADS
    const z = coord.length >= 3 ? coord[2] : coord[1]
    positions.push(x, y, z)
  })

  if (positions.length < 6) {
    return null
  }

  // 4. 创建几何体
  const lineGeom = new LineGeometry()
  lineGeom.setPositions(positions)

  // 5. 创建线对象
  const line = new Line2(lineGeom, material)
  line.computeLineDistances()

  // 6. 设置用户数据
  line.userData = {
    isUserDrawnRoad: true,
    featureName: name,
    originalHighwayType: highway,
    constructionType: construction,
    dataSource: 'user-drawn',
    feature: roadFeature,
    usesIndependentMaterial: false,
    colorToRestoreAfterSelection: material.color.getHex(),
    id: roadFeature.id || roadFeature.properties?.id,
  }

  // 7. 添加到场景
  finalizedUserRoadsGroup.add(line)
  finalizedUserRoadsGroup.visible = true

  // 8. 添加路名标签
  if (name) {
    userDrawnRoadLabelsGroup.visible = true

    // 计算道路总长度，决定标签数量
    let totalLength = 0
    for (let i = 1; i < coords.length; i++) {
      const p1 = coords[i - 1]
      const p2 = coords[i]
      const dx = p2[0] - p1[0]
      const dy = p2[1] - p1[1]
      const dz =
        coords[i].length >= 3 && coords[i - 1].length >= 3 ? p2[2] - p1[2] : 0
      totalLength += Math.sqrt(dx * dx + dy * dy + dz * dz)
    }

    // 根据长度决定标签数量
    let labelCount = 1
    const maxLabels = 3

    if (totalLength > 5) {
      labelCount = Math.min(Math.ceil(totalLength / 5), maxLabels)
    }

    // 添加标签
    for (let i = 0; i < labelCount; i++) {
      const position = labelCount === 1 ? 0.5 : i / (labelCount - 1)
      const labelIndex = Math.floor(position * (coords.length - 1))

      if (labelIndex >= 0 && labelIndex < coords.length) {
        const labelPos = coords[labelIndex]

        // 不计算方向，保持文字水平
        createRoadLabel(name, labelPos, null, roadFeature)
      }
    }
  }

  return line
}

// 辅助函数：创建道路标签（3D文字贴在地面上）
const createRoadLabel = (name, position, _direction, roadFeature) => {
  if (!name || !position || !userDrawnRoadLabelsGroup) return null

  // 创建高分辨率画布来绘制文字
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')

  // 设置高分辨率画布大小和文字样式
  const fontSize = 64 // 增大字体大小，提高可读性

  // 先设置字体以测量文字尺寸
  context.font = `${fontSize}px Microsoft YaHei, Arial, sans-serif`
  const textMetrics = context.measureText(name)

  // 动态计算画布大小，根据文字内容调整
  const padding = fontSize * 0.1 // 进一步减小内边距
  canvas.width = Math.max(textMetrics.width + padding * 2, 64) // 减小最小宽度
  canvas.height = Math.max(fontSize + padding * 2, 32) // 减小最小高度

  context.fillStyle = 'rgba(0, 0, 0, 0)' // 透明背景
  context.fillRect(0, 0, canvas.width, canvas.height)

  // 启用抗锯齿和高质量渲染
  context.imageSmoothingEnabled = true
  context.imageSmoothingQuality = 'high'
  context.textRenderingOptimization = 'optimizeQuality'

  // 重新设置字体（画布大小改变后需要重新设置）
  context.font = `${fontSize}px Microsoft YaHei, Arial, sans-serif`
  context.fillStyle = '#2d4a6b' // 使用与路网相近的深蓝色
  context.textAlign = 'center'
  context.textBaseline = 'middle'

  // 绘制文字
  context.fillText(name, canvas.width / 2, canvas.height / 2)

  // 创建纹理
  const texture = new THREE.CanvasTexture(canvas)
  texture.needsUpdate = true

  // 创建材质
  const material = new THREE.MeshBasicMaterial({
    map: texture,
    transparent: true,
    alphaTest: 0.1,
    side: THREE.DoubleSide,
  })

  // 创建几何体（平面）- 增大整体尺寸
  const geometryWidth = 0.12 // 增大宽度
  const geometryHeight = 0.03 // 增大高度
  const geometry = new THREE.PlaneGeometry(geometryWidth, geometryHeight)

  // 创建网格
  const mesh = new THREE.Mesh(geometry, material)

  // 设置位置
  mesh.position.set(position[0], position[1] + 0.001, position[2])

  // 让平面水平放置（贴在地面上）
  mesh.rotation.x = -Math.PI / 2

  // 添加用户数据
  mesh.userData = {
    isUserDrawnRoadLabel: true,
    featureName: name,
    feature: roadFeature,
    featureId: roadFeature?.id || roadFeature?.properties?.id,
    isRoadLabel: true,
  }

  userDrawnRoadLabelsGroup.add(mesh)
  return mesh
}

// 修改：查找场景元素 (道路、点位、区域)

// 添加辅助函数：渲染用户道路GeoJSON数据
const renderUserRoadsFromGeoJson = () => {
  if (!isThreeInitialized.value || !finalizedUserRoadsGroup) {
    return
  }

  // 先清除现有道路
  while (finalizedUserRoadsGroup.children.length > 0) {
    const oldRoad = finalizedUserRoadsGroup.children[0]
    finalizedUserRoadsGroup.remove(oldRoad)
    if (oldRoad.geometry) {
      oldRoad.geometry.dispose()
    }
  }

  // 清除现有标签
  if (userDrawnRoadLabelsGroup) {
    while (userDrawnRoadLabelsGroup.children.length > 0) {
      const label = userDrawnRoadLabelsGroup.children[0]
      userDrawnRoadLabelsGroup.remove(label)
      if (label.element) {
        label.element.remove()
      }
    }
  }

  // 重新渲染所有道路
  if (
    userDrawnRoadsGeoJson.value?.features &&
    userDrawnRoadsGeoJson.value.features.length > 0
  ) {
    userDrawnRoadsGeoJson.value.features.forEach(feature => {
      // 将经纬度坐标转换为场景坐标用于渲染
      const sceneFeatureForRender = JSON.parse(JSON.stringify(feature))

      if (
        feature.geometry &&
        feature.geometry.type === 'LineString' &&
        Array.isArray(feature.geometry.coordinates)
      ) {
        // 检查第一个坐标点，判断是否已经是场景坐标
        const firstCoord = feature.geometry.coordinates[0]

        if (firstCoord && Array.isArray(firstCoord)) {
          // 判断是否为经纬度坐标 (通常大于100)
          const isGeoCoord =
            firstCoord.length >= 2 &&
            (Math.abs(firstCoord[0]) > 90 || Math.abs(firstCoord[1]) > 90)

          if (isGeoCoord) {
            // 转换经纬度坐标为场景坐标
            sceneFeatureForRender.geometry.coordinates =
              feature.geometry.coordinates.map(coord => {
                // 确保坐标是二维数组[经度,纬度]
                if (coord.length >= 2) {
                  const lon = coord[0]
                  const lat = coord[1]
                  // 转换为场景坐标[x,y,z]
                  const sceneCoord = transformCoordinate(lon, lat)
                  return sceneCoord // 返回[x,y,z]格式
                }
                return coord // 保留原始格式
              })
          }
        }
      }

      // 渲染转换后的道路特征
      const renderedLine = directRenderRoad(sceneFeatureForRender)
      if (!renderedLine) {
        renderFinalizedRoad(sceneFeatureForRender)
      }
    })
  }
}

// 监听用户道路数据变化，确保及时渲染
watchEffect(() => {
  if (
    isThreeInitialized.value &&
    userDrawnRoadsGeoJson.value?.features?.length > 0 &&
    finalizedUserRoadsGroup
  ) {
    renderUserRoadsFromGeoJson()
  }
})

// 处理键盘事件（已在前面定义）

// 导出filterByClickRadius函数供DeviceInteractionHandler使用
const filterByClickRadius = intersections => {
  if (!intersections.length) {
    return []
  }

  const box = new THREE.Box3()
  const center = new THREE.Vector3()

  // 根据相机距离动态调整有效的点击半径，确保在不同缩放级别下都有合理的点击范围
  // 半径 = 基础半径 + 线性缩放因子 * 相机距离
  const distance = displayedCameraDistance.value
  const effectiveClickRadius =
    POI_BASE_CLICK_RADIUS + POI_CLICK_RADIUS_SCALE_FACTOR * distance

  // 准备距离计算数据
  const candidateIntersections = intersections
    .map(i => {
      // 向上查找包含 feature 的父级 Group
      let group = i.object
      while (group && !group.userData.feature) {
        group = group.parent
      }
      if (!group) {
        return null // 如果没有 feature 数据，则不是有效的可点击对象
      }

      // 计算对象的精确几何中心
      box.setFromObject(group)
      box.getCenter(center)

      // 计算点击点和对象中心的水平距离（忽略Y轴）
      const intersectionPoint = i.point
      const pointDistance = Math.sqrt(
        Math.pow(intersectionPoint.x - center.x, 2) +
          Math.pow(intersectionPoint.z - center.z, 2)
      )

      return {
        intersection: i,
        group,
        distance: pointDistance,
        withinRadius: pointDistance < effectiveClickRadius,
      }
    })
    .filter(item => item && item.withinRadius)

  // 如果没有候选项在半径内，返回空
  if (candidateIntersections.length === 0) {
    return []
  }

  // 按距离排序，选择最近的
  candidateIntersections.sort((a, b) => a.distance - b.distance)

  return [candidateIntersections[0].intersection]
}

// 监听最大相机距离的变化
watch(
  () => maxCameraDistance.value,
  async newMaxDistance => {
    // 保存到IndexedDB
    await saveToIndexedDB('settings', 'maxCameraDistance', newMaxDistance)

    // 只在sceneManager初始化完成后更新控制器限制
    if (sceneManager) {
      const controls = sceneManager.getControls()
      controls.maxDistance = newMaxDistance

      // 如果当前距离超过新的最大距离，需要调整当前位置
      if (displayedCameraDistance.value > newMaxDistance) {
        const camera = sceneManager.getCamera()
        const direction = new THREE.Vector3()
        direction.subVectors(camera.position, controls.target)
        direction.normalize()
        direction.multiplyScalar(newMaxDistance)
        camera.position.copy(controls.target).add(direction)
        controls.update()
        displayedCameraDistance.value = newMaxDistance
      }
    }
  }
)

// 初始化时尝试从IndexedDB加载最大相机距离（已在前面定义）

// 处理主导航按钮点击
const handleNavigation = (route, device = null) => {
  switch (route) {
    case 'intersection-monitor':
      // 设置为路口监控模式
      monitorType.value = 'intersection'
      // 清除本地存储的渠化图备份数据
      localStorage.removeItem('backup_channelization')
      prepareAndShowMonitor(device, '路口监控')
      break

    case 'device-monitor':
      // 设置为设备监控模式
      monitorType.value = 'device'
      prepareAndShowMonitor(device, '设备监控')
      break

    case 'running-monitor':
      // 设置为运行监视模式
      monitorType.value = 'running'
      prepareAndShowMonitor(device, '运行监视')
      break

    case 'device-config':
      // 处理设备配置 - 显示设备监控中心模态框
      isDeviceConfigVisible.value = !isDeviceConfigVisible.value
      // 如果打开设备配置，则关闭地图配置
      if (isDeviceConfigVisible.value) {
        isMapConfigModalVisible.value = false
      }

      break
  }
}

// 处理地图配置面板的显示
const handleMapConfigPanel = panelKey => {
  activeMapPanelInModal.value = panelKey
  isMapConfigModalVisible.value = true
}

// 控制模态框和面板的显示状态
const activeMapPanelInModal = ref('city') // 'city', 'road', or 'device'

// 地图配置按钮位置信息
const mainHeader = ref(null)
const mapConfigButtonRect = ref(null)
const deviceConfigButtonRect = ref(null)

// 获取地图配置按钮的位置信息
const updateMapConfigButtonRect = () => {
  if (mainHeader.value) {
    // 查找所有导航按钮元素
    const buttons = mainHeader.value.$el.querySelectorAll('.transparent-button')

    // 查找地图配置按钮（通常是第4个按钮）
    const mapConfigButton = Array.from(buttons).find(btn =>
      btn.textContent.includes('地图配置')
    )
    if (mapConfigButton) {
      // 获取按钮在视口中的位置信息
      const rect = mapConfigButton.getBoundingClientRect()
      mapConfigButtonRect.value = {
        x: rect.left + rect.width / 2, // 按钮中心x坐标
        y: rect.bottom, // 按钮底部y坐标
        width: rect.width,
        height: rect.height,
      }
    }

    // 查找设备配置按钮（通常是第5个按钮）
    const deviceConfigButton = Array.from(buttons).find(btn =>
      btn.textContent.includes('设备配置')
    )
    if (deviceConfigButton) {
      // 获取按钮在视口中的位置信息
      const rect = deviceConfigButton.getBoundingClientRect()
      deviceConfigButtonRect.value = {
        x: rect.left + rect.width / 2, // 按钮中心x坐标
        y: rect.bottom, // 按钮底部y坐标
        width: rect.width,
        height: rect.height,
      }
    }
  }
}

// --- 看板相关状态和计算属性 ---
// 看板数据
const edgesStatus = ref({ online: 0, total: 0 })
const detectorsStatus = ref({ online: 0, total: 0 })
const controlModesData = ref({})

// 看板数据更新状态标志
let isDashboardUpdating = false

// 获取看板数据的函数
const fetchDashboardData = async () => {
  // 防止并发请求
  if (isDashboardUpdating) {
    return
  }

  isDashboardUpdating = true
  try {
    // 并行获取所有数据，简化错误处理
    const [edgesResponse, detectorsResponse, controlModesResponse] =
      await Promise.allSettled([
        dashboardApi.getEdgesStatus(),
        dashboardApi.getDetectorsStatus(),
        dashboardApi.getControlModesStatus(),
      ])

    // 设置响应数据，失败时使用默认值
    edgesStatus.value =
      edgesResponse.status === 'fulfilled'
        ? edgesResponse.value
        : { online: 0, total: 0 }

    detectorsStatus.value =
      detectorsResponse.status === 'fulfilled'
        ? detectorsResponse.value
        : { online: 0, total: 0 }

    controlModesData.value =
      controlModesResponse.status === 'fulfilled'
        ? controlModesResponse.value
        : {}
  } catch (error) {
    console.error('获取看板数据失败:', error)
    // 设置默认值
    edgesStatus.value = { online: 0, total: 0 }
    detectorsStatus.value = { online: 0, total: 0 }
    controlModesData.value = {}
  } finally {
    isDashboardUpdating = false
  }
}

// 定时刷新看板数据的函数
const startDashboardDataRefresh = () => {
  // 防止重复创建定时器
  if (dashboardRefreshInterval) {
    console.log('看板数据定时器已存在，跳过创建')
    return dashboardRefreshInterval
  }

  // 立即获取一次数据
  fetchDashboardData()

  // 设置定时器，每5秒刷新一次数据
  dashboardRefreshInterval = setInterval(() => {
    fetchDashboardData()
  }, 5000) // 5秒

  // 返回定时器ID，以便在组件销毁时清除
  return dashboardRefreshInterval
}

// 添加：处理设备聚焦功能
const handleFocusOnDevice = device => {
  // 将地理坐标转换为场景坐标
  const deviceGeoCoord = [device.edge_coord.lng, device.edge_coord.lat]
  const transformedCoords = transformGeoJsonCoordinates(
    [deviceGeoCoord],
    0,
    roadNetworkScale.value
  )

  // 创建设备在场景中的3D坐标
  const devicePoint = new THREE.Vector3(
    transformedCoords[0][0],
    COORDINATE_CONFIG.Y_LEVELS.DEVICES,
    transformedCoords[0][1]
  )

  // 使用场景管理器的聚焦方法 - 减小距离值以获得更近的视图
  if (sceneManager) {
    sceneManager.focusOnPoint(devicePoint, 0.3) // 距离从10减小到5，提供更近的视角
  }
}

// 处理主导航按钮点击

// 处理点击外部关闭菜单
const handleOutsideClick = event => {
  // 如果当前处于绘制模式，不关闭面板
  if (isDrawingMode.value || isDrawingAreaMode.value) {
    return
  }

  // 检查是否点击了DeviceControlPanel以外的区域
  if (isDeviceConfigVisible.value) {
    const devicePanelEl = document.querySelector(
      '.city-model-container .device-control-panel'
    )
    const deviceConfigButton = document.querySelector(
      '.transparent-button:nth-child(5)'
    )

    if (
      devicePanelEl &&
      !devicePanelEl.contains(event.target) &&
      (!deviceConfigButton || !deviceConfigButton.contains(event.target))
    ) {
      // 销毁设备配置面板状态，而不仅仅是隐藏
      isDeviceConfigVisible.value = false
      // 移除事件监听
      document.removeEventListener('click', handleOutsideClick)
      // 向eventBus发送消息，通知DeviceControlPanel组件重置内部状态
      eventBus.emit('deviceConfig-destroyed')
    }
  }
}

// 在handleSceneClick中添加清除设备选中状态的代码
const clearDeviceSelection = () => {
  if (selectedPointObjectRef.value) {
    // 恢复选中对象的原始材质
    const selectedObj = selectedPointObjectRef.value
    selectedObj.originalMaterials.forEach((material, node) => {
      if (node && node.isMesh) {
        node.material = material
      }
    })

    // 清除选中引用
    selectedPointObjectRef.value = null
  }
}
// === 干线管理相关函数 ===

// 干线悬停事件处理函数 - 集成优先级管理
const handleTrunkLineHover = eventData => {
  // 设置干线提示框信息
  hoveredTrunkLineInfo.value = eventData.trunkLine
  hoveredTrunkLine3D.value = eventData.object3D
  hoveredTrunkLineMousePosition.value = eventData.mouseWorldPosition

  // 注册到优先级管理系统
  registerHover('TRUNK_LINE', {
    info: eventData.trunkLine,
    object3D: eventData.object3D,
    mousePosition: eventData.mouseWorldPosition,
  })
}

// 干线悬停结束处理函数 - 立即销毁提示框
const handleTrunkLineHoverEnd = () => {
  // 从优先级管理系统注销
  unregisterHover('TRUNK_LINE')

  // 立即清除干线提示框，不使用延迟
  hoveredTrunkLineInfo.value = null
  hoveredTrunkLine3D.value = null
  hoveredTrunkLineMousePosition.value = null
}

const handleTrunkLineLeftClick = () => {
  // 干线点击逻辑
}

const handleTrunkLineRightClick = () => {
  // 干线右键菜单逻辑
}

// 干线提示框事件处理函数已删除

// === 道路交互相关函数 ===

// 道路删除处理函数
const handleRoadDelete = async eventData => {
  try {
    const { roadId, roadName, object3D } = eventData

    // 使用道路删除服务
    const result = await roadDeleteService.deleteRoad({
      roadId,
      roadName,
      object3D,
      roadNetworkGroup,
      dynamicRoadLoader,
      roadNetworkData: jinanRoadNetworkGeoJson.value,
      sceneManager,
      saveToIndexedDB,
      eventBus,
      onProgress: message => {
        // 进度回调，可在此处添加进度条等UI反馈
      },
    })

    if (result.success) {
      // 显示删除结果消息
      if (result.serverUpdateSuccess) {
        message.success(
          `道路 "${roadName}" 及其 ${result.deletedCount} 个路段删除成功`
        )
      } else {
        message.warning(
          `道路 "${roadName}" 及其 ${result.deletedCount} 个路段本地删除成功，但服务器同步可能存在问题`
        )
      }
    }
  } catch (error) {
    console.error('CityModel: 删除道路失败:', error)
    message.error(`删除道路 "${eventData.roadName}" 失败: ${error.message}`)
  }
}

// === 区域交互相关函数 ===

// 区域悬停事件处理函数 - 集成优先级管理
const handleAreaHover = eventData => {
  // 设置区域提示框信息
  hoveredAreaInfo.value = eventData.area
  hoveredArea3D.value = eventData.object3D
  hoveredAreaMousePosition.value = eventData.mouseWorldPosition

  // 注册到优先级管理系统
  registerHover('AREA', {
    info: eventData.area,
    object3D: eventData.object3D,
    mousePosition: eventData.mouseWorldPosition,
  })
}

// 区域悬停结束处理函数 - 立即销毁提示框
const handleAreaHoverEnd = () => {
  // 从优先级管理系统注销
  unregisterHover('AREA')

  // 立即清除区域提示框，不使用延迟
  hoveredAreaInfo.value = null
  hoveredArea3D.value = null
  hoveredAreaMousePosition.value = null
}

const handleAreaLeftClick = () => {
  // 区域点击逻辑 - 可用于选择区域、显示详细信息等
  // 目前暂未实现具体功能
}

const handleAreaRightClick = () => {
  // 区域右键菜单逻辑 - 可用于显示上下文菜单、快速操作等
  // 目前暂未实现具体功能
}

// 区域提示框事件处理函数已删除

// 只保留交互相关函数
// 开始创建干线
const handleStartTrunkLineCreation = () => {
  isTrunkLineCreationMode.value = true
  isTrunkLineEditingMode.value = false
  editingTrunkLineData.value = null
  selectedRoadIdsForTrunkLine.value = []
  clearAllSelections() // 清除其他选择状态
}

// 开始编辑干线
const handleStartTrunkLineEditing = trunkLineData => {
  isTrunkLineCreationMode.value = false
  isTrunkLineEditingMode.value = true
  editingTrunkLineData.value = trunkLineData

  // 如果干线有已选择的路段，加载到选择列表中
  if (
    trunkLineData.selectedRoadIds &&
    Array.isArray(trunkLineData.selectedRoadIds)
  ) {
    selectedRoadIdsForTrunkLine.value = [...trunkLineData.selectedRoadIds]
  } else {
    selectedRoadIdsForTrunkLine.value = []
  }

  clearAllSelections() // 清除其他选择状态
}

// 取消创建干线
const handleCancelTrunkLineCreation = () => {
  isTrunkLineCreationMode.value = false
  selectedRoadIdsForTrunkLine.value = []
  clearRoadSelectionHighlights()
  activeMapPanelInModal.value = 'trunklines'
  isMapConfigModalVisible.value = true
}

// 统一的取消干线操作函数（创建或编辑）
const handleCancelTrunkLineOperation = () => {
  if (isTrunkLineEditingMode.value) {
    isTrunkLineEditingMode.value = false
    editingTrunkLineData.value = null
  } else {
    isTrunkLineCreationMode.value = false
  }

  selectedRoadIdsForTrunkLine.value = []
  clearRoadSelectionHighlights()
  activeMapPanelInModal.value = 'trunklines'
  isMapConfigModalVisible.value = true
}

// 删除干线
const handleDeleteTrunkLine = routeId => {
  // 从渲染器中移除干线
  if (trunkLineRenderer) {
    trunkLineRenderer.removeTrunkLine(routeId)
  }
}

// 移除路线选择
const handleRemoveRoadSelection = roadId => {
  const index = selectedRoadIdsForTrunkLine.value.indexOf(roadId)
  if (index > -1) {
    selectedRoadIdsForTrunkLine.value.splice(index, 1)
    clearSpecificRoadHighlight(roadId)
  }
}

// 清除特定路线的高亮显示
const clearSpecificRoadHighlight = roadId => {
  if (roadNetworkGroup) {
    roadNetworkGroup.traverse(child => {
      if (child.userData && child.userData.featureId === roadId) {
        if (child.userData.originalMaterial) {
          child.material = child.userData.originalMaterial
          delete child.userData.originalMaterial
          delete child.userData.isHighlighted
        }
      }
    })
  }
  if (finalizedUserRoadsGroup) {
    finalizedUserRoadsGroup.traverse(child => {
      if (child.userData && child.userData.featureId === roadId) {
        if (child.userData.originalMaterial) {
          child.material = child.userData.originalMaterial
          delete child.userData.originalMaterial
          delete child.userData.isHighlighted
        }
      }
    })
  }
}

// 处理路线选择
const handleRoadSelection = roadObject => {
  if (!roadObject?.userData) return

  const featureId =
    roadObject.userData.featureId ||
    roadObject.userData.featureName ||
    roadObject.userData.id ||
    roadObject.userData.tempId
  if (!featureId) return

  const index = selectedRoadIdsForTrunkLine.value.indexOf(featureId)
  if (index > -1) {
    selectedRoadIdsForTrunkLine.value.splice(index, 1)
    removeRoadHighlight(roadObject)
  } else {
    selectedRoadIdsForTrunkLine.value.push(featureId)
    addRoadHighlight(roadObject)
  }
}

// 添加路线高亮
const addRoadHighlight = roadObject => {
  if (!roadObject?.material) return
  if (roadObject.userData.isHighlighted) {
    return
  }
  try {
    if (!roadObject.userData.originalMaterial) {
      roadObject.userData.originalMaterial = roadObject.material
    }
    let parentGroup = roadObject.parent
    const scene = sceneManager?.getScene()
    while (parentGroup && parentGroup !== scene) {
      if (!parentGroup.visible) {
        parentGroup.visible = true
        if (!parentGroup.userData.forceVisibleForHighlight) {
          parentGroup.userData.forceVisibleForHighlight = []
        }
        const roadId =
          roadObject.userData.featureId ||
          roadObject.userData.featureName ||
          roadObject.userData.id ||
          roadObject.userData.tempId ||
          'unknown'
        parentGroup.userData.forceVisibleForHighlight.push(roadId)
      }
      parentGroup = parentGroup.parent
    }
    let highlightMaterial
    if (roadObject.material.isLineMaterial) {
      highlightMaterial = roadObject.material.clone()
      highlightMaterial.color.set(0x4fd1c5)
      highlightMaterial.linewidth = Math.max(
        highlightMaterial.linewidth * 1.3,
        0.15
      )
    } else if (
      roadObject.material.isMeshBasicMaterial ||
      roadObject.material.isMeshLambertMaterial
    ) {
      highlightMaterial = roadObject.material.clone()
      highlightMaterial.color.set(0x4fd1c5)
      if (highlightMaterial.opacity !== undefined) {
        highlightMaterial.opacity = Math.min(
          highlightMaterial.opacity * 1.3,
          1.0
        )
      }
    } else {
      highlightMaterial = roadObject.material.clone()
      if (highlightMaterial.color) {
        highlightMaterial.color.set(0x4fd1c5)
      }
      if (highlightMaterial.linewidth !== undefined) {
        highlightMaterial.linewidth = Math.max(
          highlightMaterial.linewidth * 1.3,
          0.1
        )
      }
    }
    roadObject.material = highlightMaterial
    roadObject.userData.isHighlighted = true
  } catch (error) {
    // 添加路线高亮失败
  }
}

// 移除路线高亮
const removeRoadHighlight = roadObject => {
  if (!roadObject || !roadObject.userData.originalMaterial) {
    return
  }
  try {
    roadObject.material = roadObject.userData.originalMaterial
    delete roadObject.userData.originalMaterial
    delete roadObject.userData.isHighlighted

    const roadId =
      roadObject.userData.featureId ||
      roadObject.userData.featureName ||
      roadObject.userData.id ||
      roadObject.userData.tempId ||
      'unknown'
    let parentGroup = roadObject.parent
    const scene = sceneManager?.getScene()
    while (parentGroup && parentGroup !== scene) {
      if (parentGroup.userData.forceVisibleForHighlight) {
        const index =
          parentGroup.userData.forceVisibleForHighlight.indexOf(roadId)
        if (index > -1) {
          parentGroup.userData.forceVisibleForHighlight.splice(index, 1)
          if (parentGroup.userData.forceVisibleForHighlight.length === 0) {
            const currentDistance = displayedCameraDistance.value
            const shouldBeVisible = checkIfGroupShouldBeVisible(
              parentGroup,
              currentDistance
            )
            if (!shouldBeVisible) {
              parentGroup.visible = false
            }
            delete parentGroup.userData.forceVisibleForHighlight
          }
        }
      }
      parentGroup = parentGroup.parent
    }
  } catch (error) {
    // 移除路线高亮失败
  }
}

// 检查路网组在当前距离下是否应该可见
const checkIfGroupShouldBeVisible = (group, distance) => {
  if (!dynamicRoadLoader || !dynamicRoadLoader.renderedRoadGroups) {
    return true // 如果没有动态加载器，默认可见
  }

  // 根据距离判断应该显示哪个级别的路网
  let targetLevel
  if (distance > dynamicRoadLoader.distanceThresholds.hide) {
    targetLevel = 'hide'
  } else if (distance > dynamicRoadLoader.distanceThresholds.primary) {
    targetLevel = 'primary'
  } else if (distance > dynamicRoadLoader.distanceThresholds.secondary) {
    targetLevel = 'primarySecondary'
  } else {
    targetLevel = 'all'
  }

  // 检查当前组是否是应该显示的级别
  for (const [level, levelGroup] of Object.entries(
    dynamicRoadLoader.renderedRoadGroups
  )) {
    if (levelGroup === group) {
      return level === targetLevel && targetLevel !== 'hide'
    }
  }
  return true // 如果不是动态路网组，默认可见
}

// 恢复选中路线的高亮状态（在路网切换后调用）
const restoreRoadHighlights = () => {
  if (!selectedRoadIdsForTrunkLine.value.length || !dynamicRoadLoader) {
    return
  }

  // 遍历所有选中的路线ID，重新应用高亮
  selectedRoadIdsForTrunkLine.value.forEach(roadId => {
    // 在所有路网组中查找对应的路线对象
    if (dynamicRoadLoader.renderedRoadGroups) {
      Object.values(dynamicRoadLoader.renderedRoadGroups).forEach(group => {
        if (group && group.children.length > 0) {
          group.traverse(child => {
            if (child.isLine2 || child.type === 'Line2') {
              const childRoadId =
                child.userData.featureId ||
                child.userData.featureName ||
                child.userData.id ||
                child.userData.tempId

              if (childRoadId === roadId && !child.userData.isHighlighted) {
                addRoadHighlight(child)
              }
            }
          })
        }
      })
    }
  })
}

// 清除路线选择高亮
const clearRoadSelectionHighlights = () => {
  let clearedCount = 0

  try {
    // 清除主路网组的高亮
    if (roadNetworkGroup) {
      roadNetworkGroup.traverse(child => {
        if (child.userData && child.userData.originalMaterial) {
          child.material = child.userData.originalMaterial
          delete child.userData.originalMaterial
          delete child.userData.isHighlighted
          clearedCount++
        }
      })
    }

    // 清除动态路网组的高亮
    if (dynamicRoadLoader && dynamicRoadLoader.renderedRoadGroups) {
      Object.values(dynamicRoadLoader.renderedRoadGroups).forEach(group => {
        if (group) {
          group.traverse(child => {
            if (child.userData && child.userData.originalMaterial) {
              child.material = child.userData.originalMaterial
              delete child.userData.originalMaterial
              delete child.userData.isHighlighted

              // 不进行位置恢复
              // 清理位置相关的userData
              if (child.userData.originalPosition !== undefined) {
                delete child.userData.originalPosition
              }

              clearedCount++
            }
          })

          // 清除强制显示标记并恢复正确的可见性
          if (group.userData.forceVisibleForHighlight) {
            delete group.userData.forceVisibleForHighlight
            // 检查这个组是否应该在当前距离下可见
            const currentDistance = displayedCameraDistance.value
            const shouldBeVisible = checkIfGroupShouldBeVisible(
              group,
              currentDistance
            )
            group.visible = shouldBeVisible
          }
        }
      })
    }

    // 清除用户绘制路线的高亮
    if (finalizedUserRoadsGroup) {
      finalizedUserRoadsGroup.traverse(child => {
        if (child.userData && child.userData.originalMaterial) {
          child.material = child.userData.originalMaterial
          delete child.userData.originalMaterial
          delete child.userData.isHighlighted

          // 不进行位置恢复
          // 清理位置相关的userData
          if (child.userData.originalPosition !== undefined) {
            delete child.userData.originalPosition
          }

          clearedCount++
        }
      })
    }
  } catch (error) {
    // 清除路线高亮失败
  }
}

// 获取当前应该显示的道路级别
const getCurrentRoadLevel = distance => {
  if (!dynamicRoadLoader || !dynamicRoadLoader.distanceThresholds) {
    return 'all' // 如果没有动态加载器，默认显示所有道路
  }

  // 根据距离判断应该显示哪个级别的路网
  if (distance > dynamicRoadLoader.distanceThresholds.hide) {
    return 'hide'
  } else if (distance > dynamicRoadLoader.distanceThresholds.primary) {
    return 'primary'
  } else if (distance > dynamicRoadLoader.distanceThresholds.secondary) {
    return 'primarySecondary'
  } else {
    return 'all'
  }
}

// 处理路网数据更新事件
const handleRoadNetworkUpdated = async () => {
  try {
    console.log('收到路网数据更新事件，重新加载路网数据...')

    // 从IndexedDB重新加载路网数据
    const roadData = await getFromIndexedDB(ROAD_DATA_STORE, 'default')
    if (roadData) {
      // 更新路网数据
      jinanRoadNetworkGeoJson.value = roadData

      // 如果动态道路加载器已经初始化，更新其数据源
      if (dynamicRoadLoader) {
        dynamicRoadLoader.updateRoadDataSource(roadData)
        const camera = sceneManager?.getCamera()
        if (camera) {
          if (roadDisplayMode.value === 'auto') {
            dynamicRoadLoader.updateRoadsByDistance(
              displayedCameraDistance.value,
              camera
            )
          } else {
            setRoadDisplayMode(roadDisplayMode.value)
          }

          // 更新路名标签
          if (smartRoadLabelManager && roadLabelsGroup) {
            const currentRoadLevel = getCurrentRoadLevel(
              displayedCameraDistance.value
            )
            smartRoadLabelManager.renderLabels(
              roadData,
              roadNetworkScale.value,
              roadLabelsGroup,
              COORDINATE_CONFIG.Y_LEVELS.ROADS,
              camera,
              currentRoadLevel
            )
          }
        }
      }
    }
  } catch (error) {
    console.error('重新加载路网数据失败:', error)
  }
}
</script>

<style scoped lang="scss">
.city-model-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
