<template>
  <div class="login-page">
    <!-- 背景 -->
    <div class="background">
      <div class="gradient-layer"></div>
      <div class="dot-pattern"></div>
    </div>

    <!-- 登录卡片 -->
    <div class="login-container">
      <!-- 左侧面板 -->
      <div class="left-panel">
        <img src="/优程-深_副本.svg" alt="优程科技" class="company-logo" />
        <div class="welcome-text">
          <h2>智能交通信号</h2>
          <h1>边缘设施统一管控系统</h1>
          <p>优化城市交通，智慧出行管理的核心平台</p>
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <div class="login-form-container">
          <h3>账号登录</h3>

          <a-form :model="loginForm" @finish="handleLogin" class="login-form">
            <a-form-item
              name="username"
              :rules="[{ required: true, message: '请输入用户名' }]"
            >
              <a-input
                v-model:value="loginForm.username"
                placeholder="用户名"
                autocomplete="username"
                size="large"
              >
                <template #prefix><UserOutlined /></template>
              </a-input>
            </a-form-item>

            <a-form-item
              name="password"
              :rules="[{ required: true, message: '请输入密码' }]"
            >
              <a-input-password
                v-model:value="loginForm.password"
                placeholder="密码"
                autocomplete="current-password"
                size="large"
              >
                <template #prefix><LockOutlined /></template>
              </a-input-password>
            </a-form-item>

            <div class="login-options">
              <a-checkbox v-model:checked="loginForm.remember">
                记住密码
              </a-checkbox>
            </div>

            <a-button
              type="primary"
              html-type="submit"
              :loading="loading"
              class="submit-button"
              block
            >
              登录系统
            </a-button>
          </a-form>

          <div class="copyright">
            <p>© {{ new Date().getFullYear() }} 优程(苏州)数字技术有限公司</p>
            <p class="version">Version 2.1.0</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { UserOutlined, LockOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { setToken } from '@s2/utils/auth'
import { login } from '../api/auth'

const router = useRouter()
const route = useRoute()
const loading = ref(false)
const loginForm = reactive({
  username: '',
  password: '',
  remember: false,
})

onMounted(() => {
  const rememberedUsername = localStorage.getItem('stsecs2-username')
  if (rememberedUsername) {
    loginForm.username = rememberedUsername
    loginForm.remember = true
  }
})

const handleLogin = async () => {
  loading.value = true

  try {
    const response = await login(loginForm.username, loginForm.password)

    if (response.returnCode === 0) {
      setToken(response.data.token)

      if (loginForm.remember) {
        localStorage.setItem('stsecs2-username', loginForm.username)
      } else {
        localStorage.removeItem('stsecs2-username')
      }

      message.success('登录成功')
      router.push(route.query.redirect || '/')
    } else {
      message.error(response.msg || '登录失败')
    }
  } catch (error) {
    console.error('登录处理失败:', error)
    message.error('登录失败，请稍后再试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.login-page {
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
    Arial, sans-serif;
}

// 背景样式
.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background-image: url('/back2.jpg');
  background-size: cover;
  background-position: center;

  .gradient-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      rgba(15, 23, 42, 0.95),
      rgba(23, 49, 96, 0.9)
    );
  }

  .dot-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(
      rgba(255, 255, 255, 0.15) 1px,
      transparent 1px
    );
    background-size: 30px 30px;
    opacity: 0.3;
  }
}

// 登录容器
.login-container {
  width: 900px;
  height: 580px;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  display: flex;
  border: 1px solid rgba(255, 255, 255, 0.05);

  // 左侧面板
  .left-panel {
    width: 50%;
    padding: 50px;
    background: linear-gradient(
      135deg,
      rgba(25, 113, 194, 0.7),
      rgba(13, 71, 161, 0.8)
    );
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: start;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(
        circle,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 60%
      );
      opacity: 0.6;
      z-index: 0;
    }

    .company-logo {
      height: 60px;
      margin-bottom: 40px;
      position: relative;
      z-index: 1;
      filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3));
    }

    .welcome-text {
      position: relative;
      z-index: 1;

      h2 {
        font-size: 24px;
        font-weight: 300;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 8px;
      }

      h1 {
        font-size: 32px;
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 20px;
        line-height: 1.2;
      }

      p {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.7);
        max-width: 360px;
        line-height: 1.6;
      }
    }
  }

  // 右侧面板
  .right-panel {
    width: 50%;
    padding: 50px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: rgba(15, 23, 42, 0.6);
    position: relative;

    .login-form-container {
      width: 100%;
      max-width: 320px;

      h3 {
        font-size: 24px;
        font-weight: 500;
        color: #ffffff;
        margin-bottom: 30px;
        text-align: center;
      }

      .login-form {
        margin-bottom: 30px;

        :deep(.ant-form-item) {
          margin-bottom: 20px;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: rgba(255, 255, 255, 0.15);
            transition: all 0.3s;
          }

          &:focus-within::after {
            background: rgba(24, 144, 255, 0.6);
            height: 2px;
          }
        }

        :deep(.ant-input),
        :deep(.ant-input-affix-wrapper) {
          background: transparent;
          border: none;
          height: 50px;
          color: #ffffff !important;
          padding-left: 15px;
          transition: all 0.3s;
          box-shadow: none;

          &:hover,
          &:focus {
            border: none;
            background: transparent;
            box-shadow: none;
            color: #ffffff !important;
          }
        }

        :deep(.ant-input-password) {
          width: 100%;
        }

        :deep(.ant-input-affix-wrapper > input.ant-input) {
          padding: 0;
          background: transparent;
          color: #ffffff !important;
        }

        :deep(.ant-input-prefix) {
          color: rgba(255, 255, 255, 0.5);
          margin-right: 10px;
        }

        :deep(.ant-input::placeholder) {
          color: rgba(255, 255, 255, 0.3);
        }

        :deep(.ant-form-item-explain-error) {
          color: #ff4d4f;
          font-size: 12px;
          margin-top: 5px;
        }
      }

      .login-options {
        margin-bottom: 25px;

        :deep(.ant-checkbox-wrapper) {
          color: rgba(255, 255, 255, 0.7);

          .ant-checkbox-inner {
            background-color: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
          }

          .ant-checkbox-checked .ant-checkbox-inner {
            background-color: #1890ff;
            border-color: #1890ff;
          }
        }
      }

      .submit-button {
        height: 50px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 500;
        letter-spacing: 1px;
        background: linear-gradient(to right, #1890ff, #096dd9);
        border: none;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
          background: linear-gradient(to right, #40a9ff, #1890ff);
        }

        &:active {
          transform: translateY(0);
        }
      }

      .copyright {
        margin-top: 40px;
        text-align: center;

        p {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.4);
          margin-bottom: 5px;
        }

        .version {
          font-size: 11px;
          color: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
}

// 响应式样式
@media (max-width: 920px) {
  .login-container {
    width: 90%;
    max-width: 400px;
    height: auto;
    flex-direction: column;

    .left-panel,
    .right-panel {
      width: 100%;
      padding: 30px;
    }

    .left-panel {
      text-align: center;

      .welcome-text h1,
      .welcome-text h2,
      .welcome-text p {
        max-width: 100%;
      }
    }
  }
}
</style>

<style>
/* 全局样式覆盖 */
body .ant-message {
  z-index: 9999;
}

.ant-message-notice-content {
  background: rgba(15, 23, 42, 0.9) !important;
  color: white !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  padding: 10px 16px !important;
}

.ant-message-success .anticon,
.ant-message-error .anticon,
.ant-message-warning .anticon,
.ant-message-info .anticon {
  color: inherit !important;
}
/* 输入框样式覆盖 */
:where(.css-dev-only-do-not-override-1p3hq3p).ant-input-affix-wrapper-lg,
.ant-input-affix-wrapper-lg,
:where(.css-dev-only-do-not-override-1p3hq3p).ant-input-affix-wrapper,
.ant-input-affix-wrapper {
  padding-top: 0;
  padding-bottom: 0;
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
  color: #ffffff !important;
}

:where(.css-dev-only-do-not-override-1p3hq3p).ant-input-affix-wrapper
  > input.ant-input,
.ant-input-affix-wrapper > input.ant-input {
  padding: 0;
  font-size: inherit;
  border: none;
  border-radius: 0;
  outline: none;
  background: transparent !important;
  color: #ffffff !important;
}

:where(.css-dev-only-do-not-override-1p3hq3p).ant-input-affix-wrapper:focus,
:where(.css-dev-only-do-not-override-1p3hq3p).ant-input-affix-wrapper-focused,
.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused,
:where(.css-dev-only-do-not-override-1p3hq3p).ant-input-affix-wrapper:hover,
.ant-input-affix-wrapper:hover {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  color: #ffffff !important;
}

/* 密码图标颜色 */
:where(.css-dev-only-do-not-override-1p3hq3p).ant-input-affix-wrapper
  .anticon.ant-input-password-icon {
  color: rgba(255, 255, 255, 0.45) !important;
}
</style>
