import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import Components from 'unplugin-vue-components/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import { fileURLToPath, URL } from 'node:url'
import path from 'path'
import removeConsole from 'vite-plugin-remove-console'

/**
 * sigopt-box 项目模块解析插件
 */
function sigoptBoxResolverPlugin() {
  return {
    name: 'sigopt-box-resolver',
    resolveId(source, importer) {
      if (
        source.startsWith('@/') &&
        importer &&
        importer.includes('sigopt-box')
      ) {
        const relativePath = source.replace('@/', '../')
        return {
          id: relativePath,
          external: false,
        }
      }
      return null
    },
  }
}

export default defineConfig(() => {
  return {
    server: {
      open: true,
      host: true,
      port: 3001,
      fs: {
        allow: ['..'],
        strict: false,
      },
      hmr: {
        overlay: false,
      },
      warmup: {
        clientFiles: [
          './src/main.js',
          './src/App.vue',
          './src/views/Login.vue',
        ],
      },
      proxy: {
        '/api/api-proxy': {
          target: 'http://*************:8082', // 开发环境下sigopt后端的真实端口号
          changeOrigin: true,
          timeout: 60000,
          rewrite: path => path.replace(/^\/api\/api-proxy\/[a-zA-Z0-9_]+/, ''),
          configure: (proxy, _options) => {
            proxy.on('error', (_err, _req, res) => {
              if (res.writeHead && !res.headersSent) {
                res.writeHead(503, {
                  'Content-Type': 'application/json',
                })

                const errorPayload = {
                  error: true,
                  message: 'API服务暂时不可用，请稍后再试',
                  code: 'API_PROXY_ERROR',
                }

                res.end(JSON.stringify(errorPayload))
              }
            })

            proxy.on('proxyReq', (proxyReq, _req, _res) => {
              proxyReq.timeout = 60000
            })
          },
        },
        '/api': {
          target: 'http://*************:8084',
          changeOrigin: true,
          timeout: 60000,
        },
      },
    },
    preview: {
      port: 3002,
      host: true,
    },
    plugins: [
      vue({
        template: {
          compilerOptions: {},
        },
      }),
      Components({
        resolvers: [
          AntDesignVueResolver({
            importStyle: 'less',
          }),
        ],
        dirs: ['src/components', '../sigopt-box/src/components'],
        dts: true,
      }),
      sigoptBoxResolverPlugin(),
      removeConsole({
        includes: ['log', 'warn', 'info', 'debug', 'trace'],
        excludes: ['error'],
      }),
    ],
    resolve: {
      alias: {
        // 当前项目路径别名
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '@s2': fileURLToPath(new URL('./src', import.meta.url)), // 恢复@s2别名，项目中有使用

        // sigopt-box 项目路径别名
        '@sigopt': path.resolve(__dirname, '../sigopt-box/src'),
        '@sigopt/styles': path.resolve(__dirname, '../sigopt-box/src/styles'),

        // 强制使用当前项目的 Three.js 版本，避免重复导入
        three: path.resolve(__dirname, 'node_modules/three'),
      },
      dedupe: ['three', 'vue', 'pinia'], // 去重关键依赖
    },
    build: {
      chunkSizeWarningLimit: 2000,
      reportCompressedSize: false,
      minify: 'esbuild',
      sourcemap: false,
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['vue', 'vue-router', 'pinia'],
            three: ['three'],
            antd: ['ant-design-vue'],
            utils: ['axios', 'dayjs'],
          },
        },
      },
      target: 'es2015',
    },
    css: {
      preprocessorOptions: {
        scss: {
          charset: false,
        },
        less: {
          javascriptEnabled: true,
        },
      },
    },
    optimizeDeps: {
      include: ['vue', 'vue-router', 'pinia', 'axios', 'three'],
      cacheDir: '.vite/.deps_cache',
      force: false,
      exclude: ['sigopt-box', 'ant-design-vue/es'],
    },
    define: {
      // 添加全局定义以避免生产环境中的未定义错误
      __VUE_PROD_DEVTOOLS__: false,
      __VUE_OPTIONS_API__: true,
    },
  }
})
